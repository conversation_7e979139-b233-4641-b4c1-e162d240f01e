name: addc
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.2+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  intl: any
  shared_preferences: ^2.5.2
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.17
  provider: ^6.1.2
  flutter_easyloading: ^3.0.5
  lottie: ^3.3.1
  flutter_slidable: ^4.0.0
  readmore: ^3.0.0
  flutter_switch: ^0.3.2
  image_cropper: ^9.0.0
  image_picker: ^1.1.2
  date_formatter: ^0.0.4
  convex_bottom_bar: ^3.2.0
  url_launcher: ^6.3.1
  double_back_to_close: ^2.0.0
  fluttertoast: ^8.0.9
  dio: ^5.8.0+1
  aad_oauth: ^1.0.1
  microsoft_graph_api: ^0.0.7
  skeletonizer: ^2.0.1
  flutter_typeahead: ^5.2.0
  bottom_sheet_scaffold: ^0.1.9
  flutter_secure_storage: ^9.2.4
  infinite_scroll_pagination: ^4.1.0
  xml: ^6.5.0
  flutter_dotenv: ^5.2.1
  qr_flutter: ^4.1.0
  local_auth: ^2.3.0
  flutter_hooks: ^0.21.2
  firebase_core: ^3.15.1
  firebase_remote_config: ^5.4.7
  package_info_plus: ^8.1.2
  vcard_vcf: ^0.1.5
  share_plus: ^11.0.0
  path_provider: ^2.1.5
  timezone: ^0.10.1
  blur: ^4.0.2
  
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env
    - assets/icons/
    - assets/images/
    - assets/svg/
    - assets/dummy/
    - assets/lottie/
    - assets/notification/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Grold
      fonts:
        - asset: assets/fonts/Grold/Grold-Regular.ttf
        - asset: assets/fonts/Grold/Grold-ExtraBlack.ttf
          weight: 900
        - asset: assets/fonts/Grold/Grold-Black.ttf
          weight: 800
        - asset: assets/fonts/Grold/Grold-ExtraBold.ttf
          weight: 700
        - asset: assets/fonts/Grold/Grold-Bold.ttf
          weight: 600
        - asset: assets/fonts/Grold/Grold-Medium.ttf
          weight: 500
        - asset: assets/fonts/Grold/Grold-Light.ttf
          weight: 300
        - asset: assets/fonts/Grold/Grold-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Grold/Grold-Thin.ttf
          weight: 100
      # --------------------------------------------------------
      # - asset: assets/fonts/Grold/Grold-Regular.ttf
      # - asset: assets/fonts/Grold/Grold-Thin.ttf
      #   weight: 100
      # - asset: assets/fonts/Grold/Grold-ThinItalic.ttf
      #   weight: 100
      #   style: italic
      # - asset: assets/fonts/Grold/Grold-ExtraLight.ttf
      #   weight: 200
      # - asset: assets/fonts/Grold/Grold-ExtraLightItalic.ttf
      #   weight: 200
      #   style: italic
      # - asset: assets/fonts/Grold/Grold-Light.ttf
      #   weight: 300
      # - asset: assets/fonts/Grold/Grold-LightItalic.ttf
      #   weight: 300
      #   style: italic
      # # - asset: assets/fonts/Grold/Grold-SemiLight.ttf
      # #   weight: 300
      # # - asset: assets/fonts/Grold/Grold-SemiLightItalic.ttf
      # #   weight: 300
      #   # style: italic
      # - asset: assets/fonts/Grold/Grold-Italic.ttf
      #   weight: 400
      #   style: italic
      # - asset: assets/fonts/Grold/Grold-Medium.ttf
      #   weight: 500
      # # - asset: assets/fonts/Grold/Grold-Medium.otf
      # #   weight: 500
      # - asset: assets/fonts/Grold/Grold-MediumItalic.ttf
      #   weight: 500
      #   style: italic
      # - asset: assets/fonts/Grold/Grold-Bold.ttf
      #   weight: 700
      # - asset: assets/fonts/Grold/Grold-BoldItalic.ttf
      #   weight: 700
      #   style: italic
      # - asset: assets/fonts/Grold/Grold-ExtraBold.ttf
      #   weight: 800
      # - asset: assets/fonts/Grold/Grold-ExtraBoldItalic.ttf
      #   weight: 800
      #   style: italic
      # - asset: assets/fonts/Grold/Grold-ExtraBlack.ttf
      #   weight: 800
      # - asset: assets/fonts/Grold/Grold-ExtraBlackItalic.ttf
      #   weight: 800
      #   style: italic
      # - asset: assets/fonts/Grold/grold-black.ttf
      #   weight: 900
      # - asset: assets/fonts/Grold/Grold-BlackItalic.ttf
      #   weight: 900
      #   style: italic

      # - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-MediumItalic.otf
      #   weight: 500
      #   style: italic

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
