# Firebase Remote Config: show_manual_login Parameter

## Overview

The `show_manual_login` parameter is a boolean flag in Firebase Remote Config that controls whether the manual login section (username/password fields) is displayed on the login screen. This allows for dynamic control of the login options without requiring an app update.

## Default Value

The default value is `false`, which means that if Firebase Remote Config fails to initialize or fetch values, the manual login section will be hidden.

## Implementation Details

The parameter has been implemented in the following files:

1. `lib/services/firebase_remote_config_service.dart` - Added the parameter definition, default value, and getter method
2. `lib/features/authentication/view/login_screen.dart` - Modified to conditionally show the manual login section based on the parameter value

## Testing the Feature

### Using the Remote Config Debug Screen

The app includes a debug screen for testing Remote Config values:

1. Navigate to the Remote Config Debug Screen (typically accessible from a debug menu)
2. Use the "Force Fetch" button to immediately fetch the latest values from Firebase
3. Check if the `show_manual_login` parameter appears in the values list
4. Verify that the source is "remote" if you've set it in the Firebase Console, or "default" if not

### Testing in the Login Screen

1. Set the parameter value in the Firebase Console:
   - Go to Firebase Console > Remote Config
   - Add or edit the `show_manual_login` parameter
   - Set it to `true` or `false`
   - Save and publish changes

2. Launch the app and navigate to the login screen
3. Observe whether the manual login section (username/password fields) is displayed or hidden

### Forcing a Refresh

If you've just changed the value in the Firebase Console and want to test immediately:

1. Go to the Remote Config Debug Screen
2. Tap "Force Fetch" to bypass the minimum fetch interval
3. Navigate back to the login screen to see the changes

## Firebase Console Configuration

To configure this parameter in the Firebase Console:

1. Go to Firebase Console > Your Project > Remote Config
2. Click "Add parameter"
3. Parameter name: `show_manual_login`
4. Parameter type: Boolean
5. Default value: `false` (or your preferred default)
6. Optionally, add conditional values for different user segments
7. Save and publish

## Troubleshooting

If the parameter doesn't seem to be working:

1. Check the app logs for any Remote Config initialization errors
2. Verify that the parameter name in Firebase Console exactly matches `show_manual_login`
3. Use the Remote Config Debug Screen to check if the parameter is being fetched correctly
4. Ensure that the app has an active internet connection when fetching Remote Config values
5. Remember that there's a caching period (default is 12 hours in production) - use "Force Fetch" to bypass this

## Example Usage in Code

```dart
// Check if manual login should be shown
if (FirebaseRemoteConfigService.instance.showManualLogin) {
  // Show manual login UI
} else {
  // Only show SSO options
}
```
