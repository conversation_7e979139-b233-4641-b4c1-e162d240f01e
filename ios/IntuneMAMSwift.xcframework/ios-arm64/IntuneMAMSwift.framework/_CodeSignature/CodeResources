<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/IntuneMAM-Swift.h</key>
		<data>
		pbRFU3OAWl1XECYazRfKmIRjWdU=
		</data>
		<key>Headers/IntuneMAM.h</key>
		<data>
		AHfxs8WfHQr/Nc8iONrVKwygK7g=
		</data>
		<key>Headers/IntuneMAMAppConfig.h</key>
		<data>
		8ZCAepzmrJBixkPfTuPFphtBDbQ=
		</data>
		<key>Headers/IntuneMAMAppConfigManager.h</key>
		<data>
		J+oWI9zo/yv1m9CDcmvd3PqDbYQ=
		</data>
		<key>Headers/IntuneMAMCertificatePinningManager.h</key>
		<data>
		6eyJ0XknN94BRwVc/8XCbm6go+8=
		</data>
		<key>Headers/IntuneMAMComplianceManager.h</key>
		<data>
		DeketU1xl8RB9ZY4NHctetvY3s8=
		</data>
		<key>Headers/IntuneMAMDataProtectionInfo.h</key>
		<data>
		OPt82mA/rcno/HNHrNRv9PW91RI=
		</data>
		<key>Headers/IntuneMAMDataProtectionManager.h</key>
		<data>
		QoHcn9rotgFUpFHrU3DDhsl+YDs=
		</data>
		<key>Headers/IntuneMAMDefs.h</key>
		<data>
		EiUz2RGZT/p913F4bdqbRDe7N68=
		</data>
		<key>Headers/IntuneMAMDiagnosticConsole.h</key>
		<data>
		kR+46dEemGd/E8DxwhfYm9a3vPI=
		</data>
		<key>Headers/IntuneMAMEnrollmentDelegate.h</key>
		<data>
		qfX9OT2NyeYCOBXvDfba8GPjYmU=
		</data>
		<key>Headers/IntuneMAMEnrollmentManager.h</key>
		<data>
		rVTDA9p3xjoCGayvgRGbAliCHWg=
		</data>
		<key>Headers/IntuneMAMEnrollmentStatus.h</key>
		<data>
		/3o+bBn2BWE79M4JvDhFSnz6MrY=
		</data>
		<key>Headers/IntuneMAMFile.h</key>
		<data>
		dbSE9LeLBUIpoqzBKsX1e/67lOA=
		</data>
		<key>Headers/IntuneMAMFileProtectionInfo.h</key>
		<data>
		d22FFpyqQgIaO6PHbngiQoDPUUM=
		</data>
		<key>Headers/IntuneMAMFileProtectionManager.h</key>
		<data>
		R7ZPWizd7zl8vbSyiX8emA2IUL8=
		</data>
		<key>Headers/IntuneMAMLogger.h</key>
		<data>
		KddTGgEDNKpwAV2CBId0mG1YWLE=
		</data>
		<key>Headers/IntuneMAMPolicy.h</key>
		<data>
		1ZxDODJA5WXojNdCIwZE7XJP1jM=
		</data>
		<key>Headers/IntuneMAMPolicyDelegate.h</key>
		<data>
		T7grz47b79PrHHUy3VDHOJXNy0I=
		</data>
		<key>Headers/IntuneMAMPolicyManager.h</key>
		<data>
		xOlhjgOnRLpuEQKq/M2pwzbzlss=
		</data>
		<key>Headers/IntuneMAMSettings.h</key>
		<data>
		3adYKHNcev4159d1qe06aoAuKm8=
		</data>
		<key>Headers/IntuneMAMSwift.h</key>
		<data>
		dFH5oqYPivgDDv4KIHyjO2qAIxE=
		</data>
		<key>Headers/IntuneMAMTelemetry.h</key>
		<data>
		hPvUeIlKQU1/RNAqUiBjAeLXV6Q=
		</data>
		<key>Headers/IntuneMAMUIHelper.h</key>
		<data>
		T0BCX2egAy0i7d8y+OYQ2b5JmoE=
		</data>
		<key>Headers/IntuneMAMUserStatus.h</key>
		<data>
		/fSyoXu4zPu/k+2tO9qVOXAgZNM=
		</data>
		<key>Headers/IntuneMAMUserStatusManager.h</key>
		<data>
		XIOtNjcAhyr1Uz30RjeyAXzG6WE=
		</data>
		<key>Headers/IntuneMAMVersionInfo.h</key>
		<data>
		b8X9CyL6wwne6r39DcR5byQ/P6M=
		</data>
		<key>Info.plist</key>
		<data>
		/XyYXrVAVqg3bauBWH1Zfzm5g6Y=
		</data>
		<key>IntuneMAMResources.bundle/ActivityAlertViewController.nib</key>
		<data>
		lUIatnrgplk0TY+hLb+8VaI76pA=
		</data>
		<key>IntuneMAMResources.bundle/Assets.car</key>
		<data>
		ZtrNSiLqojOYCkyzxYvGVM7CUvM=
		</data>
		<key>IntuneMAMResources.bundle/AuthViewController_iPad.nib</key>
		<data>
		zHaH87on91Fv0E/O1U3DWm2oZJ8=
		</data>
		<key>IntuneMAMResources.bundle/AuthViewController_iPhone.nib</key>
		<data>
		k6mWzqkHg5g2PRrmRCvJAxVp1/s=
		</data>
		<key>IntuneMAMResources.bundle/Base.lproj/PinViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			r7lJKE3LyMibdeveQkHJJWL5xLA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/BlurViewController_iPad.nib</key>
		<data>
		wNCFUolqryRC+vudtHn4xLPPoFE=
		</data>
		<key>IntuneMAMResources.bundle/BlurViewController_iPhone.nib</key>
		<data>
		BH5KGlurn6Bi02NdOCQPPuUA7Xg=
		</data>
		<key>IntuneMAMResources.bundle/DiagnosticAppStatusViewController.nib</key>
		<data>
		Oz4JYvEPe4VuBVI0HZwOmmLCSKM=
		</data>
		<key>IntuneMAMResources.bundle/DiagnosticUploadViewController.nib</key>
		<data>
		kgSgO2B4X1CnHxoQj8Xy7s8/peA=
		</data>
		<key>IntuneMAMResources.bundle/DiagnosticViewController.nib</key>
		<data>
		yNIwVqhM8XHWr2v95gZRffB5uMQ=
		</data>
		<key>IntuneMAMResources.bundle/Info.plist</key>
		<data>
		0p5pYT43Q9AD8urm6FoCTWQ8tSQ=
		</data>
		<key>IntuneMAMResources.bundle/MTDComplianceViewController.nib</key>
		<data>
		c4Cnyyd4DRTeEBrnj56SyiA0H+I=
		</data>
		<key>IntuneMAMResources.bundle/MTDComplianceViewControllerUnlistedMTDApp.nib</key>
		<data>
		e1ScPjuMUZW0WjR4R5cEdVVpZ5s=
		</data>
		<key>IntuneMAMResources.bundle/MessageViewController.nib</key>
		<data>
		C71L0hsrBIFDQDHuJjUOTlX3XX8=
		</data>
		<key>IntuneMAMResources.bundle/OpenInExtensionViewController.nib</key>
		<data>
		/X4dmpbULIVEdgMinChjCeSd9I0=
		</data>
		<key>IntuneMAMResources.bundle/PolicyViewController_iPad.nib</key>
		<data>
		Y4TXGnpysUTWHq5DB/bDkcFjhEg=
		</data>
		<key>IntuneMAMResources.bundle/PolicyViewController_iPhone.nib</key>
		<data>
		qqpxYnYMWMfzlDN7LvnksOUTGbE=
		</data>
		<key>IntuneMAMResources.bundle/SplashViewWithAlertController_iPad.nib</key>
		<data>
		5vXnmdyjzTGeRHHmwg3UN8qEFX4=
		</data>
		<key>IntuneMAMResources.bundle/SplashViewWithAlertController_iPhone.nib</key>
		<data>
		MI9gzWTORVukrvzxZPt3SOXmTnw=
		</data>
		<key>IntuneMAMResources.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		i+8mh0jPLNoTEHS5/N7wuQrWYLA=
		</data>
		<key>IntuneMAMResources.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		yPX9YwuZFYsARZjY5K+IRA6ehTI=
		</data>
		<key>IntuneMAMResources.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		FIDwDUl9VqeiST+fzZ00037Hdgw=
		</data>
		<key>IntuneMAMResources.bundle/_CodeSignature/CodeResources</key>
		<data>
		v3Nz0gy/xDpaTBWUCt/Qc0BJTYc=
		</data>
		<key>IntuneMAMResources.bundle/_CodeSignature/CodeSignature</key>
		<data>
		EFkjIdKARPzf179O59/ixU9ABWQ=
		</data>
		<key>IntuneMAMResources.bundle/cs.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			C+9nJ3yVMO/VHn3OpxWcnJzIxb4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/cs.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			yzkplkWYLWCZ+7njle5S23kTnco=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/da.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iRUNdLOYg31SNGxt2eXTPoBaLKE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/da.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			t6182ENYk0G+3r4ga5IdWFG/3Hs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/de.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y2MfDd/XvhagKqE1cIdPBZXP5wU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/de.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0TeRYTAXa90Ppzmq7rcQhefFRcE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/el.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7A3llVZ0cEIcVafaxkAQhwRYHK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/el.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DkA4WQfH52ybGEajRwHg+D4TQJE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/en.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cPzkQM4GDB8tqznPgpIn9TbABCU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/en.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x4jrfzECO4oWlK0pDEBND+SS7QM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/es.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JnD95zsTGIVxllLBtLdISNbl4q0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/es.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZUPfPBUTn6ydX/uWxqn0Zzkue2k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/fi.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FqLdaIFUiO2V7nvNQh38AtSUSno=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/fi.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BSgOxcCzS2lWU2mNLmRWr3CtWV0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/fr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B87ePpsR7UQKjLkCvKPfcbuncqo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/fr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WvE3Uak88HfTp6u3y1fFS+CGCXI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/hr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			98Ach0SGNlHeluHFYE5MziE8oPA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/hr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			P0CFPeNVNYpm8ctwVdhru/QHrtY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/hu.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uAtbzOkCE/oNLgS71VVAs+Roixk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/hu.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			imFUGYjPzg0gq7eB2XvdkZlKdos=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/id.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZN9ndow9wycRIS+H/orvX2ucNqw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/id.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Q44LPOXJYGHGh1p8YTmJVOIWcD8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/it.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jqCX3zkTnenfDbz/IDgb60qusaY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/it.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			h3AT05n02BPYIQCLGoa6b4/YXoU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ja.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TVg+rsmBjE0Z5rNEts3KLYSM8tI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ja.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v0p7Rn00mWb16X/eBZVqPWzwPUg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ko.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SZXBhCXkkIEl1l3E0yUnnVD8TTU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ko.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i1kiN5FySW8mbqyIXHPma1Bh0gk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/mappingTable.plist</key>
		<data>
		Mm6ls60Vr9ZPCNq/J2FiMx7FIIk=
		</data>
		<key>IntuneMAMResources.bundle/nb.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FaM6u57rguhoZ78X6uA5r8nDR5U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/nb.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			h296w7n4yZn7OSV3EsmtVAG8ePc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/nl.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bSOWc0fuYx7jqN0VNGwiVdb0ZxU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/nl.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OpzdXKrklTQJmVIXfuyqUDJzbUE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pl.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			okQGnleivRxrp7qVpmy484O9X2E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pl.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bXaSmW74QxwghjTCneFOTuyvayI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pt-br.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			eqIO0+MNRtn3JftCSP4BROZASzE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pt-br.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8NJjZLLFn/HzzVOtr453fl04brc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pt-pt.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XcHj7U0NM3Ml4x0IN/2OTT2yxaI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pt-pt.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			w/ia/YNFXerXsWplKqL2b0U4Rlc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ro.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			G/9R0ge6fkCAyBuoK9R1btZJCeE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ro.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1z4JLkubWvFLEv8e9JtHJw7GZWM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ru.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oCnWEaFGnnNJBGNf4/Ru2O/LuCw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ru.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			igq3TX13+v2Wtkla6rYeNh41V6U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/sv.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uhIm5ipXBOMXXvpmyX3ZTavDg0Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/sv.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			H9Ms0xZFFnTSe15Gv7SsXgsVhwI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/th.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RHgTrZIybde5GdTEBMRNPvnzsIQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/th.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I74fj2Lx/lIBXOGfBfeV8OGo96w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/tr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DZryto4II2mwKlsx5GR3Xcb2XWg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/tr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1ibHVFLgG8Wa6TMof0w3cpc9edQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/vi.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			spnbCjr7RGQrVw6xfMfhLnvicx4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/vi.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/s7oAjVLJSC7X0aSGG4wqx8RDPg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/zh-hans.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fIdTr3Lbp0rNH4a9cbNvFvvdjKQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/zh-hans.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QWGgRihIO5kwlJv31K6lobEbdaY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/zh-hant.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1coQ3C3XvunyQcEOb81/KC+btp0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/zh-hant.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p80qURpXbqflO7r24Ag0d5PPGiY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Modules/IntuneMAMSwift.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		wjh3IRgWQsUegJmg7wYkpUkImlI=
		</data>
		<key>Modules/IntuneMAMSwift.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		9iXBI69HJnxKDANpTYpSDJ+wD8E=
		</data>
		<key>Modules/IntuneMAMSwift.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		Qf6hFkX40H4C4P19z6vrXmBD5Ik=
		</data>
		<key>Modules/IntuneMAMSwift.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		9iXBI69HJnxKDANpTYpSDJ+wD8E=
		</data>
		<key>Modules/IntuneMAMSwift.swiftmodule/arm64-apple-ios.swiftmodule</key>
		<data>
		ajailvdns6/nnVRz1bWGZF7IE4M=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		qiTCPbknJS42NBuXrJbqizN6Jtg=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		Lm2+4tHYHIolkb0ymeS9JpHh1iM=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/IntuneMAM-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			GqORGCEmQfCbdhb8yN+0Z6D21pMQvxtEGK0LPWNXUj8=
			</data>
		</dict>
		<key>Headers/IntuneMAM.h</key>
		<dict>
			<key>hash2</key>
			<data>
			IyC4eeKcNoxlJqPsZeVsS15P2nW+9RqSXV5wTMJyMz0=
			</data>
		</dict>
		<key>Headers/IntuneMAMAppConfig.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3YVSK8SC17+ShvbPT/p6y9fyJzDuJX7me0k/5CNAlU0=
			</data>
		</dict>
		<key>Headers/IntuneMAMAppConfigManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Nsm677/lFaUN1rEqdiAurBix/qtDrbGYLQMr6N3KaHc=
			</data>
		</dict>
		<key>Headers/IntuneMAMCertificatePinningManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			iFqOKKiRaDJHJjDZl/+rsPYFmMaOQMr1KfGhSZhM+XM=
			</data>
		</dict>
		<key>Headers/IntuneMAMComplianceManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			i8OS0HdPuGpM1kCB1ZrvWQEebI3rBOuMOrEOauOBn+Q=
			</data>
		</dict>
		<key>Headers/IntuneMAMDataProtectionInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6GW4Ab9pbChpplnCF/lPOec4z1Bqa3WG873Vvo7ylR4=
			</data>
		</dict>
		<key>Headers/IntuneMAMDataProtectionManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			mwY//HKkkTyNAbcBhrnAF1Euie4y7wXnrZQC7l9cysA=
			</data>
		</dict>
		<key>Headers/IntuneMAMDefs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DjIoPcs2nBBfpmygeUY/hmzOHWUfLmwtWAxkHFHXnE8=
			</data>
		</dict>
		<key>Headers/IntuneMAMDiagnosticConsole.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Y5YkG4zf4jqCcdHn4dR5p9QQS9vF6F4AMBO3Z4RTUPo=
			</data>
		</dict>
		<key>Headers/IntuneMAMEnrollmentDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6+3hHmbGOcxPvz6StrUwWrot1UG5nbOOasuHYk05Rhw=
			</data>
		</dict>
		<key>Headers/IntuneMAMEnrollmentManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZmR978o5Niht/n0aK2ElBJwIo1XMv2I5y0AUe4uPZjk=
			</data>
		</dict>
		<key>Headers/IntuneMAMEnrollmentStatus.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4khACW0apciExBqdMc4DLPNFIEPdCew4MtQCnAd67Gw=
			</data>
		</dict>
		<key>Headers/IntuneMAMFile.h</key>
		<dict>
			<key>hash2</key>
			<data>
			38bp2yLnL5XVOuk1emaf06V3J7CgdbKngK8a5a2qaQs=
			</data>
		</dict>
		<key>Headers/IntuneMAMFileProtectionInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZfQycftLxxlQo+LWDOHNNHRMyUBkXfX3zeJeRUCDkZg=
			</data>
		</dict>
		<key>Headers/IntuneMAMFileProtectionManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5P3VFnt1D4zs3Ulj3+aCktxBb1ngR1Difviy+Z6vLyM=
			</data>
		</dict>
		<key>Headers/IntuneMAMLogger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CQ2UfAbOHY0ReZwi3awlWUT6dCKeMabJ/Z1wpv/Pwxw=
			</data>
		</dict>
		<key>Headers/IntuneMAMPolicy.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hPmELQoOqNx+t4qN2oSJYp0JGVW6IyOV/67805klHP4=
			</data>
		</dict>
		<key>Headers/IntuneMAMPolicyDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			emd391ty4o6t6/pWn9cI0fWk4517iooOPYd1KoakS8s=
			</data>
		</dict>
		<key>Headers/IntuneMAMPolicyManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			qSjsyE8/hbqnW6uzr/jwlpgTMR1LdL3F4MrUMGA06gg=
			</data>
		</dict>
		<key>Headers/IntuneMAMSettings.h</key>
		<dict>
			<key>hash2</key>
			<data>
			u08GVUCClKeMa4nU9QtMy+MfCje+xyN5lcFvj3n/+WM=
			</data>
		</dict>
		<key>Headers/IntuneMAMSwift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			8hrX71FW7KkD1wlVwZvdqM5+NcDlgDu3uAhd+MwOOJk=
			</data>
		</dict>
		<key>Headers/IntuneMAMTelemetry.h</key>
		<dict>
			<key>hash2</key>
			<data>
			UdAIO3qh6jz9xMMcU2U8GYnQiamwPbaCp+ixu0BTxQM=
			</data>
		</dict>
		<key>Headers/IntuneMAMUIHelper.h</key>
		<dict>
			<key>hash2</key>
			<data>
			xKJSRjNSbxPXH+nxGrIe1r1QFMxDv6C7tNOe7+ulj9I=
			</data>
		</dict>
		<key>Headers/IntuneMAMUserStatus.h</key>
		<dict>
			<key>hash2</key>
			<data>
			esLSOpBCCFql+5sW0c0b5UldoCXSIXBUnU300hRfjLY=
			</data>
		</dict>
		<key>Headers/IntuneMAMUserStatusManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			WQSjNOiKdAZEQ4EHlenoCTifxrT/AbLPuCTBYl1p+3c=
			</data>
		</dict>
		<key>Headers/IntuneMAMVersionInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			024onu9p5n4QHqS1aW7nbUnI8MIVAO6QEo3hweobY3U=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/ActivityAlertViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Zq0EnogUbHHs/Z1W94VfPGit9qWN36+NrpvUpmqy+P4=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			stv5A9KItej0+NEOpy8oSw1GioyLSW4GJFeV4EXv7HM=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/AuthViewController_iPad.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			PjObhaJJZjrbx7nRZdh2GzzsBi1fGpf100xdtjjCcyM=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/AuthViewController_iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			i0hFE0XdTqPtLvIx+edbhb9/FB0aOEz8IQeGt3zeGqE=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/Base.lproj/PinViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			POZai1tL7k81zRrqLfHRF9jbLbTsDABXslFKNgbA4/4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/BlurViewController_iPad.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			7vl/i+9oWvuMDSp5LiOd4jjPYpNfQrz9zcZD5tXV2GM=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/BlurViewController_iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Ep8vPoYEu4scmLHEFZt691e2fi0631GWC3lhbrL1klk=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/DiagnosticAppStatusViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			rxlVBlrVOxhuUcePJ+/6QPR9gT1nUnhGwxT4qgQtN9o=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/DiagnosticUploadViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			2WUuzJFUpZZscfI6TTHuXUqpGwl0wclcSN3Qzzp9BVs=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/DiagnosticViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			O2qJSQaWxMU7CgijZ35S8pNba7A6bDg8UeVsPdBjV/A=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			mWeJ8rD5Q68v4glVQOVKAYpQhB478tL0Qg8HKJCKYjc=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/MTDComplianceViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			YfYYZh+Q4+WeRgeJCYGfWLnVovd9QRNyEKSeBGIJa3A=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/MTDComplianceViewControllerUnlistedMTDApp.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			tWRp2wi9hPoCqQJbzNgg0XRNmbnw7K/9C7fhwXg/G6g=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/MessageViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BvwH0qzYvgWAWvsI79n+fU3l1p/n7o/x8AKpZUXiQH8=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/OpenInExtensionViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			cn8Ciu2bRPqm+zYkjHzH3U+KjZrCo3M2EVRmicSWMN8=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/PolicyViewController_iPad.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			SfRRUFOq728KJqv22z4jq84QTTptziDmEZadItPLY7I=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/PolicyViewController_iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			pf//b08a8UYUun9kSa/B+x/RJa9qur4gdMe7xAsk3XY=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/SplashViewWithAlertController_iPad.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			XpSFpPxNfPJwhG+6Mg64EOzNvLEG6A1rFtQErslkV1M=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/SplashViewWithAlertController_iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ByWRYVKDhcngjhfUc9B6nzkXz+fWNiDE8ff0H0qANkg=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			dxOZWEmOOU87+kYJOtD9r/GbptOLAcP0XIqih743cWk=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			+OkASsntpH7gMG69++BkgTWcNa9sUzq+fXcYY51Jgc8=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			um2nWhHs3Ssnj641sDNtcyhO8Cz1CHZQ3rAfrB6GRpI=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			/QeoO0IiL3NwmNycEc4e8sXYnu6Lxs7CC3OxgFDn+xk=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			N3PNx07OUg+5//y1pV+ucjlb9vgbhHwN6/zb9k1UH/0=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/cs.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RUB9palzH7bxqZ3pnfJOMGLNo0SYzZxH31HV4ZCMuBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/cs.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			I5m+spTF96AF/hLmqingfGYOMJr/A3ufJWgugPULX0I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/da.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			e5U+nVz/F67C9g+2eQy5nUmqj7Yh8rS8JuXKCV9lzuI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/da.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyYhWMd0PBMYbid+Re3dQ+LjJjbSNjQ6TEDk4GySL74=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/de.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ysEzj8gJNeKBZEwv7wYIQU7qkGdUWgZz1JGiyXGlroY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/de.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Qbi4RYxirtAMAaTEugYfxdRxS3H8cdEHX7GOTF8Yojw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/el.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vcHFjqsg4/GRXCnwyVhZpmetpxDdhPHtZAeZCgad1Js=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/el.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0VaRmj0PSWDNz4jpLX4SFtzSPqUOlJ9WZLT+ID5qsRI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/en.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			T9oEM6kHP8QySEo5DHLFBziuEjLNJ7FKpLTKs0h4Gmo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/en.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			PaTzFWu5xOhxy0oGdqmGPx3G+YQfSCLu5eT+ym1tycs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/es.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			d3/4ikiuANo6tKNh+nM0lZEs+uhdtD6CE26jcxseBdo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/es.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ACrkjM87eD0buJAHRQf9qV7MX3HjMp3vmBOro07L/yc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/fi.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SBGKtKWcbFiZWBitWwG+5diWiuDhN5SFkMQqTw9e4hs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/fi.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			lNIkccCjtfTrzNndMSWKI97z7CttioDx98FurEmZG2g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/fr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IGmzaVyLXAmBOq+Olz/ar38nESknQQyDvmjaTOw+hZQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/fr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HEQo+ZS+98Fj865LTe15g3T1jQlkbd/HzXusGQOKVRM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/hr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			PydJWRMPi7yCXbLUcn32eiX92pRFGjyfA7goDINEEE4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/hr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FWdzwwQ2eUb549pvnDGYrfgJB/NWCophNLx88Bwf69I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/hu.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			cKG1fV07p4aJ/Z9dDHqUNFp/LwYOnFei5gBRYSJzPbY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/hu.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UjOjJq4H8NZx/i8Wasg+MyhXgtLiGArL5CQfDdeml10=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/id.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			dEGVvxcGuj6LmyjC8WJ3yCWTxRemXGeoNRC8AE2EqQs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/id.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WxpvP4tuXqYNNMFUa7NLDvZs//jMxmVSB3VDQCAo/gc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/it.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hEIsBFM5M1qTidC5GKsCq6vV3Wj6icHNLNOmzo0Jzxs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/it.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			mM69lLuDuVBcZCRYu+LfVa/Z/VBkuB+a1A8W2QGzVsU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ja.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7n29RhHzX2ouCSIEA++E42SSu+T+l1cSgpZcgpb/MQM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ja.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			b8CeTM1goQHjwo5x0JZMymtyv6d7qJqUgVlQ39iJofI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ko.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			W/1V0J7as7Xfcdt6i3NeC78kpDPDmmWZ14rfWfzkXiQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ko.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nM/Ou7RDCC9tAjeqpnlHE2ndbrj/BeL+/wL8Fb66F48=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/mappingTable.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			nH1Fs0QJT8FTa58iWq1qZWeuQudbhF9d97tYi+5G01Q=
			</data>
		</dict>
		<key>IntuneMAMResources.bundle/nb.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0FbwOtwsyU+pz1WFJjOvuE0vV8Wjap1YOO4f39OxjLQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/nb.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xtn3936UI6aeW2f1JAnvmJSeH4XB2WUO+2IS3W2f2EU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/nl.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			kL6/5yupJY88qtTXPDAGxmHSM3uGD1AC4pZgFgwhcdA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/nl.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			oOk76hEC1zuWFwig/yNKvljxB4SuVBRBIwo+P9CMai4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pl.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			u68tzvxfI3YQB3xOX7sNFe7vJyqP90VJ5wVvhTeD/EE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pl.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			X8I1pd9OyG72dfNdAaa4AwP94Tl+ZMsaXwExczs3MJI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pt-br.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			YMBo7ERxmAZCNnOLMQ4ha+0iVuBn43P0HovrAxGASGo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pt-br.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3HPVjByNq7KTj56KTOZfFgHzo6yIdkzy6cxV2j71hKI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pt-pt.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fGwMQChh0tlTukGSAQSOw6n3KAAFOIncE45PNDa0PbM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/pt-pt.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			MdFrZHO0k8+wZy+sVx9/qsX7eaXWgzPBqgg2fiaQMpU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ro.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Wv/r4lZFjpvQ0xvFAQZrvWtpDJMIkCV4iKRIpmXfA1E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ro.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4dvBfa9Dovh476Tiv1iBFVep35ek+9p4/h68/pZhStw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ru.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			mTl0wSTZhnhXJqRCgVQW8ATX+4OVFBIQeojhiXSnK48=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/ru.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Y8EI7DeuIlIioB9PgeihaQtpL086lEsmwEXa8Nl+604=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/sv.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vQ0N60zqG77KWARA/kXJv14BVWaJhb0o0z+GMs42U7k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/sv.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			upELfvbd6BWbShkFXMH98gKMojvxGOct34FL/1OBTKc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/th.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HHsF1v/YgtvuJY8ARhPeQT4Mc2X6b/VbBByW4WkTf/o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/th.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			kjR85F0jIk+jVemWNNvI9G9opdVclc/h9pENxDUX0jo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/tr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fCFzIelR+8Cs36dDuBT75Cl88UjTJPKZFpwUwUY0RuY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/tr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			sAiTfNdBLpn9lCCvRCRRmcrKf+gigv68CNZnzVs8Wt8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/vi.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			z5wzpH37Lidrvv4tNFuPzXWxfTxHEC5IpNr1o3u3A30=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/vi.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+l2lvaEs8FdDsS3aP03fspJ3vO2qDYIHcnTHv3MwYoI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/zh-hans.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			z8xTfTzWH2BpuHPyPKYjgIafq56OzWBDvozU2BAvSIY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/zh-hans.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8WjmGMnQy/d4U+5dCHzXc/QBeBSBSQXxPeN9pbdAjdY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/zh-hant.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			lKpt3XVrFme3SSG+rNK1uMsrohyxTReDnQd/bpjLy/A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>IntuneMAMResources.bundle/zh-hant.lproj/PinViewController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jKenhUsK//IAgIBfCtfJVvYbqgWK2+9Z9iSUdPSZViI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Modules/IntuneMAMSwift.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			++A+ffXcD5K+t4MGvdwtfzyFZP+QLeQ6t/Q2vuaWguw=
			</data>
		</dict>
		<key>Modules/IntuneMAMSwift.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			4Wx6VCgRfcPf/zO7eH+JOQ8YKyZem6Zm6KHvlxrhkXA=
			</data>
		</dict>
		<key>Modules/IntuneMAMSwift.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			U5Wr0SEnzVik+9hNjSfLYEpIgDaJ31DLFe9LeaCVFJc=
			</data>
		</dict>
		<key>Modules/IntuneMAMSwift.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			4Wx6VCgRfcPf/zO7eH+JOQ8YKyZem6Zm6KHvlxrhkXA=
			</data>
		</dict>
		<key>Modules/IntuneMAMSwift.swiftmodule/arm64-apple-ios.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			GR7eOOWSZfIc+tXAc/iSmspJbDiB29sxixuWluZIR/g=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			AsXah0fSzNN+kKy4ShRMOz7EMdibVd6vrY2uZg6yBHg=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n6FMl+H0O+9Jumh90AdEyez1dmqEk1bV2QcQf5lbeC0=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
