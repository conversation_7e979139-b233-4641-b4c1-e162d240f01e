<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ActivityAlertViewController.nib</key>
		<data>
		lUIatnrgplk0TY+hLb+8VaI76pA=
		</data>
		<key>Assets.car</key>
		<data>
		ZtrNSiLqojOYCkyzxYvGVM7CUvM=
		</data>
		<key>AuthViewController_iPad.nib</key>
		<data>
		zHaH87on91Fv0E/O1U3DWm2oZJ8=
		</data>
		<key>AuthViewController_iPhone.nib</key>
		<data>
		k6mWzqkHg5g2PRrmRCvJAxVp1/s=
		</data>
		<key>Base.lproj/PinViewController.nib</key>
		<data>
		r7lJKE3LyMibdeveQkHJJWL5xLA=
		</data>
		<key>BlurViewController_iPad.nib</key>
		<data>
		wNCFUolqryRC+vudtHn4xLPPoFE=
		</data>
		<key>BlurViewController_iPhone.nib</key>
		<data>
		BH5KGlurn6Bi02NdOCQPPuUA7Xg=
		</data>
		<key>DiagnosticAppStatusViewController.nib</key>
		<data>
		Oz4JYvEPe4VuBVI0HZwOmmLCSKM=
		</data>
		<key>DiagnosticUploadViewController.nib</key>
		<data>
		kgSgO2B4X1CnHxoQj8Xy7s8/peA=
		</data>
		<key>DiagnosticViewController.nib</key>
		<data>
		yNIwVqhM8XHWr2v95gZRffB5uMQ=
		</data>
		<key>MTDComplianceViewController.nib</key>
		<data>
		c4Cnyyd4DRTeEBrnj56SyiA0H+I=
		</data>
		<key>MTDComplianceViewControllerUnlistedMTDApp.nib</key>
		<data>
		e1ScPjuMUZW0WjR4R5cEdVVpZ5s=
		</data>
		<key>MessageViewController.nib</key>
		<data>
		C71L0hsrBIFDQDHuJjUOTlX3XX8=
		</data>
		<key>OpenInExtensionViewController.nib</key>
		<data>
		/X4dmpbULIVEdgMinChjCeSd9I0=
		</data>
		<key>PolicyViewController_iPad.nib</key>
		<data>
		Y4TXGnpysUTWHq5DB/bDkcFjhEg=
		</data>
		<key>PolicyViewController_iPhone.nib</key>
		<data>
		qqpxYnYMWMfzlDN7LvnksOUTGbE=
		</data>
		<key>SplashViewWithAlertController_iPad.nib</key>
		<data>
		5vXnmdyjzTGeRHHmwg3UN8qEFX4=
		</data>
		<key>SplashViewWithAlertController_iPhone.nib</key>
		<data>
		MI9gzWTORVukrvzxZPt3SOXmTnw=
		</data>
		<key>cs.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			C+9nJ3yVMO/VHn3OpxWcnJzIxb4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			yzkplkWYLWCZ+7njle5S23kTnco=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iRUNdLOYg31SNGxt2eXTPoBaLKE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			t6182ENYk0G+3r4ga5IdWFG/3Hs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y2MfDd/XvhagKqE1cIdPBZXP5wU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0TeRYTAXa90Ppzmq7rcQhefFRcE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>el.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7A3llVZ0cEIcVafaxkAQhwRYHK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>el.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DkA4WQfH52ybGEajRwHg+D4TQJE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cPzkQM4GDB8tqznPgpIn9TbABCU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x4jrfzECO4oWlK0pDEBND+SS7QM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JnD95zsTGIVxllLBtLdISNbl4q0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZUPfPBUTn6ydX/uWxqn0Zzkue2k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FqLdaIFUiO2V7nvNQh38AtSUSno=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BSgOxcCzS2lWU2mNLmRWr3CtWV0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B87ePpsR7UQKjLkCvKPfcbuncqo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WvE3Uak88HfTp6u3y1fFS+CGCXI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			98Ach0SGNlHeluHFYE5MziE8oPA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			P0CFPeNVNYpm8ctwVdhru/QHrtY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uAtbzOkCE/oNLgS71VVAs+Roixk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			imFUGYjPzg0gq7eB2XvdkZlKdos=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZN9ndow9wycRIS+H/orvX2ucNqw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Q44LPOXJYGHGh1p8YTmJVOIWcD8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jqCX3zkTnenfDbz/IDgb60qusaY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			h3AT05n02BPYIQCLGoa6b4/YXoU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TVg+rsmBjE0Z5rNEts3KLYSM8tI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v0p7Rn00mWb16X/eBZVqPWzwPUg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SZXBhCXkkIEl1l3E0yUnnVD8TTU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i1kiN5FySW8mbqyIXHPma1Bh0gk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mappingTable.plist</key>
		<data>
		Mm6ls60Vr9ZPCNq/J2FiMx7FIIk=
		</data>
		<key>nb.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FaM6u57rguhoZ78X6uA5r8nDR5U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nb.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			h296w7n4yZn7OSV3EsmtVAG8ePc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bSOWc0fuYx7jqN0VNGwiVdb0ZxU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OpzdXKrklTQJmVIXfuyqUDJzbUE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			okQGnleivRxrp7qVpmy484O9X2E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bXaSmW74QxwghjTCneFOTuyvayI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-br.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			eqIO0+MNRtn3JftCSP4BROZASzE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-br.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8NJjZLLFn/HzzVOtr453fl04brc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-pt.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XcHj7U0NM3Ml4x0IN/2OTT2yxaI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-pt.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			w/ia/YNFXerXsWplKqL2b0U4Rlc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			G/9R0ge6fkCAyBuoK9R1btZJCeE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1z4JLkubWvFLEv8e9JtHJw7GZWM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oCnWEaFGnnNJBGNf4/Ru2O/LuCw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			igq3TX13+v2Wtkla6rYeNh41V6U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sv.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uhIm5ipXBOMXXvpmyX3ZTavDg0Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sv.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			H9Ms0xZFFnTSe15Gv7SsXgsVhwI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>th.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RHgTrZIybde5GdTEBMRNPvnzsIQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>th.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I74fj2Lx/lIBXOGfBfeV8OGo96w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DZryto4II2mwKlsx5GR3Xcb2XWg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1ibHVFLgG8Wa6TMof0w3cpc9edQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			spnbCjr7RGQrVw6xfMfhLnvicx4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/s7oAjVLJSC7X0aSGG4wqx8RDPg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hans.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fIdTr3Lbp0rNH4a9cbNvFvvdjKQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hans.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QWGgRihIO5kwlJv31K6lobEbdaY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hant.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1coQ3C3XvunyQcEOb81/KC+btp0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hant.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p80qURpXbqflO7r24Ag0d5PPGiY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>ActivityAlertViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			lUIatnrgplk0TY+hLb+8VaI76pA=
			</data>
			<key>hash2</key>
			<data>
			Zq0EnogUbHHs/Z1W94VfPGit9qWN36+NrpvUpmqy+P4=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			ZtrNSiLqojOYCkyzxYvGVM7CUvM=
			</data>
			<key>hash2</key>
			<data>
			stv5A9KItej0+NEOpy8oSw1GioyLSW4GJFeV4EXv7HM=
			</data>
		</dict>
		<key>AuthViewController_iPad.nib</key>
		<dict>
			<key>hash</key>
			<data>
			zHaH87on91Fv0E/O1U3DWm2oZJ8=
			</data>
			<key>hash2</key>
			<data>
			PjObhaJJZjrbx7nRZdh2GzzsBi1fGpf100xdtjjCcyM=
			</data>
		</dict>
		<key>AuthViewController_iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			k6mWzqkHg5g2PRrmRCvJAxVp1/s=
			</data>
			<key>hash2</key>
			<data>
			i0hFE0XdTqPtLvIx+edbhb9/FB0aOEz8IQeGt3zeGqE=
			</data>
		</dict>
		<key>Base.lproj/PinViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			r7lJKE3LyMibdeveQkHJJWL5xLA=
			</data>
			<key>hash2</key>
			<data>
			POZai1tL7k81zRrqLfHRF9jbLbTsDABXslFKNgbA4/4=
			</data>
		</dict>
		<key>BlurViewController_iPad.nib</key>
		<dict>
			<key>hash</key>
			<data>
			wNCFUolqryRC+vudtHn4xLPPoFE=
			</data>
			<key>hash2</key>
			<data>
			7vl/i+9oWvuMDSp5LiOd4jjPYpNfQrz9zcZD5tXV2GM=
			</data>
		</dict>
		<key>BlurViewController_iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			BH5KGlurn6Bi02NdOCQPPuUA7Xg=
			</data>
			<key>hash2</key>
			<data>
			Ep8vPoYEu4scmLHEFZt691e2fi0631GWC3lhbrL1klk=
			</data>
		</dict>
		<key>DiagnosticAppStatusViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Oz4JYvEPe4VuBVI0HZwOmmLCSKM=
			</data>
			<key>hash2</key>
			<data>
			rxlVBlrVOxhuUcePJ+/6QPR9gT1nUnhGwxT4qgQtN9o=
			</data>
		</dict>
		<key>DiagnosticUploadViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			kgSgO2B4X1CnHxoQj8Xy7s8/peA=
			</data>
			<key>hash2</key>
			<data>
			2WUuzJFUpZZscfI6TTHuXUqpGwl0wclcSN3Qzzp9BVs=
			</data>
		</dict>
		<key>DiagnosticViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			yNIwVqhM8XHWr2v95gZRffB5uMQ=
			</data>
			<key>hash2</key>
			<data>
			O2qJSQaWxMU7CgijZ35S8pNba7A6bDg8UeVsPdBjV/A=
			</data>
		</dict>
		<key>MTDComplianceViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			c4Cnyyd4DRTeEBrnj56SyiA0H+I=
			</data>
			<key>hash2</key>
			<data>
			YfYYZh+Q4+WeRgeJCYGfWLnVovd9QRNyEKSeBGIJa3A=
			</data>
		</dict>
		<key>MTDComplianceViewControllerUnlistedMTDApp.nib</key>
		<dict>
			<key>hash</key>
			<data>
			e1ScPjuMUZW0WjR4R5cEdVVpZ5s=
			</data>
			<key>hash2</key>
			<data>
			tWRp2wi9hPoCqQJbzNgg0XRNmbnw7K/9C7fhwXg/G6g=
			</data>
		</dict>
		<key>MessageViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			C71L0hsrBIFDQDHuJjUOTlX3XX8=
			</data>
			<key>hash2</key>
			<data>
			BvwH0qzYvgWAWvsI79n+fU3l1p/n7o/x8AKpZUXiQH8=
			</data>
		</dict>
		<key>OpenInExtensionViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			/X4dmpbULIVEdgMinChjCeSd9I0=
			</data>
			<key>hash2</key>
			<data>
			cn8Ciu2bRPqm+zYkjHzH3U+KjZrCo3M2EVRmicSWMN8=
			</data>
		</dict>
		<key>PolicyViewController_iPad.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Y4TXGnpysUTWHq5DB/bDkcFjhEg=
			</data>
			<key>hash2</key>
			<data>
			SfRRUFOq728KJqv22z4jq84QTTptziDmEZadItPLY7I=
			</data>
		</dict>
		<key>PolicyViewController_iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			qqpxYnYMWMfzlDN7LvnksOUTGbE=
			</data>
			<key>hash2</key>
			<data>
			pf//b08a8UYUun9kSa/B+x/RJa9qur4gdMe7xAsk3XY=
			</data>
		</dict>
		<key>SplashViewWithAlertController_iPad.nib</key>
		<dict>
			<key>hash</key>
			<data>
			5vXnmdyjzTGeRHHmwg3UN8qEFX4=
			</data>
			<key>hash2</key>
			<data>
			XpSFpPxNfPJwhG+6Mg64EOzNvLEG6A1rFtQErslkV1M=
			</data>
		</dict>
		<key>SplashViewWithAlertController_iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			MI9gzWTORVukrvzxZPt3SOXmTnw=
			</data>
			<key>hash2</key>
			<data>
			ByWRYVKDhcngjhfUc9B6nzkXz+fWNiDE8ff0H0qANkg=
			</data>
		</dict>
		<key>cs.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			C+9nJ3yVMO/VHn3OpxWcnJzIxb4=
			</data>
			<key>hash2</key>
			<data>
			RUB9palzH7bxqZ3pnfJOMGLNo0SYzZxH31HV4ZCMuBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			yzkplkWYLWCZ+7njle5S23kTnco=
			</data>
			<key>hash2</key>
			<data>
			I5m+spTF96AF/hLmqingfGYOMJr/A3ufJWgugPULX0I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iRUNdLOYg31SNGxt2eXTPoBaLKE=
			</data>
			<key>hash2</key>
			<data>
			e5U+nVz/F67C9g+2eQy5nUmqj7Yh8rS8JuXKCV9lzuI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			t6182ENYk0G+3r4ga5IdWFG/3Hs=
			</data>
			<key>hash2</key>
			<data>
			ZyYhWMd0PBMYbid+Re3dQ+LjJjbSNjQ6TEDk4GySL74=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y2MfDd/XvhagKqE1cIdPBZXP5wU=
			</data>
			<key>hash2</key>
			<data>
			ysEzj8gJNeKBZEwv7wYIQU7qkGdUWgZz1JGiyXGlroY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0TeRYTAXa90Ppzmq7rcQhefFRcE=
			</data>
			<key>hash2</key>
			<data>
			Qbi4RYxirtAMAaTEugYfxdRxS3H8cdEHX7GOTF8Yojw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>el.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7A3llVZ0cEIcVafaxkAQhwRYHK0=
			</data>
			<key>hash2</key>
			<data>
			vcHFjqsg4/GRXCnwyVhZpmetpxDdhPHtZAeZCgad1Js=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>el.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DkA4WQfH52ybGEajRwHg+D4TQJE=
			</data>
			<key>hash2</key>
			<data>
			0VaRmj0PSWDNz4jpLX4SFtzSPqUOlJ9WZLT+ID5qsRI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cPzkQM4GDB8tqznPgpIn9TbABCU=
			</data>
			<key>hash2</key>
			<data>
			T9oEM6kHP8QySEo5DHLFBziuEjLNJ7FKpLTKs0h4Gmo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x4jrfzECO4oWlK0pDEBND+SS7QM=
			</data>
			<key>hash2</key>
			<data>
			PaTzFWu5xOhxy0oGdqmGPx3G+YQfSCLu5eT+ym1tycs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JnD95zsTGIVxllLBtLdISNbl4q0=
			</data>
			<key>hash2</key>
			<data>
			d3/4ikiuANo6tKNh+nM0lZEs+uhdtD6CE26jcxseBdo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZUPfPBUTn6ydX/uWxqn0Zzkue2k=
			</data>
			<key>hash2</key>
			<data>
			ACrkjM87eD0buJAHRQf9qV7MX3HjMp3vmBOro07L/yc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FqLdaIFUiO2V7nvNQh38AtSUSno=
			</data>
			<key>hash2</key>
			<data>
			SBGKtKWcbFiZWBitWwG+5diWiuDhN5SFkMQqTw9e4hs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BSgOxcCzS2lWU2mNLmRWr3CtWV0=
			</data>
			<key>hash2</key>
			<data>
			lNIkccCjtfTrzNndMSWKI97z7CttioDx98FurEmZG2g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B87ePpsR7UQKjLkCvKPfcbuncqo=
			</data>
			<key>hash2</key>
			<data>
			IGmzaVyLXAmBOq+Olz/ar38nESknQQyDvmjaTOw+hZQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WvE3Uak88HfTp6u3y1fFS+CGCXI=
			</data>
			<key>hash2</key>
			<data>
			HEQo+ZS+98Fj865LTe15g3T1jQlkbd/HzXusGQOKVRM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			98Ach0SGNlHeluHFYE5MziE8oPA=
			</data>
			<key>hash2</key>
			<data>
			PydJWRMPi7yCXbLUcn32eiX92pRFGjyfA7goDINEEE4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			P0CFPeNVNYpm8ctwVdhru/QHrtY=
			</data>
			<key>hash2</key>
			<data>
			FWdzwwQ2eUb549pvnDGYrfgJB/NWCophNLx88Bwf69I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uAtbzOkCE/oNLgS71VVAs+Roixk=
			</data>
			<key>hash2</key>
			<data>
			cKG1fV07p4aJ/Z9dDHqUNFp/LwYOnFei5gBRYSJzPbY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			imFUGYjPzg0gq7eB2XvdkZlKdos=
			</data>
			<key>hash2</key>
			<data>
			UjOjJq4H8NZx/i8Wasg+MyhXgtLiGArL5CQfDdeml10=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZN9ndow9wycRIS+H/orvX2ucNqw=
			</data>
			<key>hash2</key>
			<data>
			dEGVvxcGuj6LmyjC8WJ3yCWTxRemXGeoNRC8AE2EqQs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Q44LPOXJYGHGh1p8YTmJVOIWcD8=
			</data>
			<key>hash2</key>
			<data>
			WxpvP4tuXqYNNMFUa7NLDvZs//jMxmVSB3VDQCAo/gc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jqCX3zkTnenfDbz/IDgb60qusaY=
			</data>
			<key>hash2</key>
			<data>
			hEIsBFM5M1qTidC5GKsCq6vV3Wj6icHNLNOmzo0Jzxs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			h3AT05n02BPYIQCLGoa6b4/YXoU=
			</data>
			<key>hash2</key>
			<data>
			mM69lLuDuVBcZCRYu+LfVa/Z/VBkuB+a1A8W2QGzVsU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TVg+rsmBjE0Z5rNEts3KLYSM8tI=
			</data>
			<key>hash2</key>
			<data>
			7n29RhHzX2ouCSIEA++E42SSu+T+l1cSgpZcgpb/MQM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v0p7Rn00mWb16X/eBZVqPWzwPUg=
			</data>
			<key>hash2</key>
			<data>
			b8CeTM1goQHjwo5x0JZMymtyv6d7qJqUgVlQ39iJofI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SZXBhCXkkIEl1l3E0yUnnVD8TTU=
			</data>
			<key>hash2</key>
			<data>
			W/1V0J7as7Xfcdt6i3NeC78kpDPDmmWZ14rfWfzkXiQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i1kiN5FySW8mbqyIXHPma1Bh0gk=
			</data>
			<key>hash2</key>
			<data>
			nM/Ou7RDCC9tAjeqpnlHE2ndbrj/BeL+/wL8Fb66F48=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mappingTable.plist</key>
		<dict>
			<key>hash</key>
			<data>
			Mm6ls60Vr9ZPCNq/J2FiMx7FIIk=
			</data>
			<key>hash2</key>
			<data>
			nH1Fs0QJT8FTa58iWq1qZWeuQudbhF9d97tYi+5G01Q=
			</data>
		</dict>
		<key>nb.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FaM6u57rguhoZ78X6uA5r8nDR5U=
			</data>
			<key>hash2</key>
			<data>
			0FbwOtwsyU+pz1WFJjOvuE0vV8Wjap1YOO4f39OxjLQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nb.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			h296w7n4yZn7OSV3EsmtVAG8ePc=
			</data>
			<key>hash2</key>
			<data>
			xtn3936UI6aeW2f1JAnvmJSeH4XB2WUO+2IS3W2f2EU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bSOWc0fuYx7jqN0VNGwiVdb0ZxU=
			</data>
			<key>hash2</key>
			<data>
			kL6/5yupJY88qtTXPDAGxmHSM3uGD1AC4pZgFgwhcdA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OpzdXKrklTQJmVIXfuyqUDJzbUE=
			</data>
			<key>hash2</key>
			<data>
			oOk76hEC1zuWFwig/yNKvljxB4SuVBRBIwo+P9CMai4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			okQGnleivRxrp7qVpmy484O9X2E=
			</data>
			<key>hash2</key>
			<data>
			u68tzvxfI3YQB3xOX7sNFe7vJyqP90VJ5wVvhTeD/EE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bXaSmW74QxwghjTCneFOTuyvayI=
			</data>
			<key>hash2</key>
			<data>
			X8I1pd9OyG72dfNdAaa4AwP94Tl+ZMsaXwExczs3MJI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-br.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			eqIO0+MNRtn3JftCSP4BROZASzE=
			</data>
			<key>hash2</key>
			<data>
			YMBo7ERxmAZCNnOLMQ4ha+0iVuBn43P0HovrAxGASGo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-br.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8NJjZLLFn/HzzVOtr453fl04brc=
			</data>
			<key>hash2</key>
			<data>
			3HPVjByNq7KTj56KTOZfFgHzo6yIdkzy6cxV2j71hKI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-pt.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XcHj7U0NM3Ml4x0IN/2OTT2yxaI=
			</data>
			<key>hash2</key>
			<data>
			fGwMQChh0tlTukGSAQSOw6n3KAAFOIncE45PNDa0PbM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-pt.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			w/ia/YNFXerXsWplKqL2b0U4Rlc=
			</data>
			<key>hash2</key>
			<data>
			MdFrZHO0k8+wZy+sVx9/qsX7eaXWgzPBqgg2fiaQMpU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			G/9R0ge6fkCAyBuoK9R1btZJCeE=
			</data>
			<key>hash2</key>
			<data>
			Wv/r4lZFjpvQ0xvFAQZrvWtpDJMIkCV4iKRIpmXfA1E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1z4JLkubWvFLEv8e9JtHJw7GZWM=
			</data>
			<key>hash2</key>
			<data>
			4dvBfa9Dovh476Tiv1iBFVep35ek+9p4/h68/pZhStw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oCnWEaFGnnNJBGNf4/Ru2O/LuCw=
			</data>
			<key>hash2</key>
			<data>
			mTl0wSTZhnhXJqRCgVQW8ATX+4OVFBIQeojhiXSnK48=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			igq3TX13+v2Wtkla6rYeNh41V6U=
			</data>
			<key>hash2</key>
			<data>
			Y8EI7DeuIlIioB9PgeihaQtpL086lEsmwEXa8Nl+604=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sv.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uhIm5ipXBOMXXvpmyX3ZTavDg0Y=
			</data>
			<key>hash2</key>
			<data>
			vQ0N60zqG77KWARA/kXJv14BVWaJhb0o0z+GMs42U7k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sv.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			H9Ms0xZFFnTSe15Gv7SsXgsVhwI=
			</data>
			<key>hash2</key>
			<data>
			upELfvbd6BWbShkFXMH98gKMojvxGOct34FL/1OBTKc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>th.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RHgTrZIybde5GdTEBMRNPvnzsIQ=
			</data>
			<key>hash2</key>
			<data>
			HHsF1v/YgtvuJY8ARhPeQT4Mc2X6b/VbBByW4WkTf/o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>th.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I74fj2Lx/lIBXOGfBfeV8OGo96w=
			</data>
			<key>hash2</key>
			<data>
			kjR85F0jIk+jVemWNNvI9G9opdVclc/h9pENxDUX0jo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DZryto4II2mwKlsx5GR3Xcb2XWg=
			</data>
			<key>hash2</key>
			<data>
			fCFzIelR+8Cs36dDuBT75Cl88UjTJPKZFpwUwUY0RuY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1ibHVFLgG8Wa6TMof0w3cpc9edQ=
			</data>
			<key>hash2</key>
			<data>
			sAiTfNdBLpn9lCCvRCRRmcrKf+gigv68CNZnzVs8Wt8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			spnbCjr7RGQrVw6xfMfhLnvicx4=
			</data>
			<key>hash2</key>
			<data>
			z5wzpH37Lidrvv4tNFuPzXWxfTxHEC5IpNr1o3u3A30=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/s7oAjVLJSC7X0aSGG4wqx8RDPg=
			</data>
			<key>hash2</key>
			<data>
			+l2lvaEs8FdDsS3aP03fspJ3vO2qDYIHcnTHv3MwYoI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hans.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fIdTr3Lbp0rNH4a9cbNvFvvdjKQ=
			</data>
			<key>hash2</key>
			<data>
			z8xTfTzWH2BpuHPyPKYjgIafq56OzWBDvozU2BAvSIY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hans.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QWGgRihIO5kwlJv31K6lobEbdaY=
			</data>
			<key>hash2</key>
			<data>
			8WjmGMnQy/d4U+5dCHzXc/QBeBSBSQXxPeN9pbdAjdY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hant.lproj/AppRestrictions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1coQ3C3XvunyQcEOb81/KC+btp0=
			</data>
			<key>hash2</key>
			<data>
			lKpt3XVrFme3SSG+rNK1uMsrohyxTReDnQd/bpjLy/A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hant.lproj/PinViewController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p80qURpXbqflO7r24Ag0d5PPGiY=
			</data>
			<key>hash2</key>
			<data>
			jKenhUsK//IAgIBfCtfJVvYbqgWK2+9Z9iSUdPSZViI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
