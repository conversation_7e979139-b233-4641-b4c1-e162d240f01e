// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 6.0.3 effective-5.10 (swiftlang-6.0.3.1.10 clang-1600.0.30.1)
// swift-module-flags: -target x86_64-apple-ios15.0-simulator -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -enable-bare-slash-regex -module-name IntuneMAMSwift
// swift-module-flags-ignorable: -no-verify-emitted-module-interface
import AppleArchive
import Dispatch
import Foundation
import GroupActivities
@_exported import IntuneMAMSwift
import IntuneMAMSwiftStub
import Swift
import SwiftUI
import System
import UIKit
import VisionKit
import _Concurrency
import _GroupActivities_UIKit
import _StringProcessing
import _SwiftConcurrencyShims
@_hasMissingDesignatedInitializers public class IntuneMAMSwiftContextManager {
  @available(*, deprecated, message: "Use getScopedAccountId instead.")
  public static func getScopedIdentity() -> Swift.String?
  public static func getScopedAccountId() -> Swift.String?
  @available(*, deprecated, message: "Use setAccountId:forScope: instead.")
  public static func setIdentity(_ identity: Swift.String?, forScope scope: @escaping () -> Swift.Void)
  public static func setAccountId(_ accountId: Swift.String?, forScope scope: @escaping () -> Swift.Void)
  @available(*, deprecated, message: "Use setAccountId:forScope: instead.")
  public static func setIdentity(_ identity: Swift.String?, forScope scope: @escaping () throws -> Swift.Void) throws
  public static func setAccountId(_ accountId: Swift.String?, forScope scope: @escaping () throws -> Swift.Void) throws
  @available(iOS 15.0, *)
  @available(*, deprecated, message: "Use setAccountId:forScope: instead.")
  public static func setIdentity(_ identity: Swift.String?, forScope scope: @escaping () async -> Swift.Void) async
  @available(iOS 15.0, *)
  public static func setAccountId(_ accountId: Swift.String?, forScope scope: @escaping () async -> Swift.Void) async
  @available(iOS 15.0, *)
  @available(*, deprecated, message: "Use setAccountId:forScope: instead.")
  public static func setIdentity(_ identity: Swift.String?, forScope scope: @escaping () async throws -> Swift.Void) async throws
  @available(iOS 15.0, *)
  public static func setAccountId(_ accountId: Swift.String?, forScope scope: @escaping () async throws -> Swift.Void) async throws
  @objc deinit
}
public enum IntuneMAMSwiftError : Swift.Error {
  case GroupActivitiesBlocked
  public static func == (a: IntuneMAMSwift.IntuneMAMSwiftError, b: IntuneMAMSwift.IntuneMAMSwiftError) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
extension IntuneMAMSwift.IntuneMAMSwiftError : Swift.Equatable {}
extension IntuneMAMSwift.IntuneMAMSwiftError : Swift.Hashable {}
