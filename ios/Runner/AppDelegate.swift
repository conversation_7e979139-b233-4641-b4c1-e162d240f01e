import UIKit
import Flutter
import IntuneMAMSwift

@main
@objc class AppDelegate: FlutterAppDelegate {
    private let CHANNEL = "com.taqadistribution/intune"
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
        let intuneChannel = FlutterMethodChannel(name: CHANNEL,
                                                binaryMessenger: controller.binaryMessenger)
        
        intuneChannel.setMethodCallHandler({
            [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
            self?.handleMethodCall(call: call, result: result)
        })
        
        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func handleMethodCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "getEnrolledAccount":
            getEnrolledAccount(result: result)
        case "isEnrolled":
            isEnrolled(result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func getEnrolledAccount(result: @escaping FlutterResult) {
        // Get instances to verify they exist
        let enrollmentManager = IntuneMAMEnrollmentManager.instance()
        let policyManager = IntuneMAMPolicyManager.instance()
        
        // Log available methods for debugging
        NSLog("EnrollmentManager type: \(type(of: enrollmentManager))")
        NSLog("PolicyManager type: \(type(of: policyManager))")
        
        // For now, return nil until we find the correct API
        // You can replace this with actual API calls once we identify them
        result(nil)
    }
    
    private func isEnrolled(result: @escaping FlutterResult) {
        // Get instances to verify they exist
        let enrollmentManager = IntuneMAMEnrollmentManager.instance()
        let policyManager = IntuneMAMPolicyManager.instance()
        
        // For now, return false until we find the correct API
        // You can replace this with actual API calls once we identify them
        result(false)
    }
}