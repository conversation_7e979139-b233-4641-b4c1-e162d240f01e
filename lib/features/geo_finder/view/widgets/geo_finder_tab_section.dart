import 'package:addc/features/geo_finder/providers/geo_finder_provider.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/widgets/common_tab_bar_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class GeoFinderTabSection extends StatelessWidget {
  final PageController pageController;

  const GeoFinderTabSection({super.key, required this.pageController});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<GeoFinderProvider>();
    return Container(
      alignment: Alignment.topLeft,
      height: 36.h,
      child: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount: provider.geoFinderTabBars.length,
        itemBuilder: (context, index) {
          return Consumer<GeoFinderProvider>(
            builder: (context, provider, _) {
              bool isSelected = provider.selectedGeoFiderTabIndex == index;

              return CommonTabBarTile(
                title: provider.geoFinderTabBars[index],
                isSelected: isSelected,
                onTap: () {
                  provider.filterOnChanged(index: index);
                  pageController.animateToPage(index,
                      duration: const Duration(milliseconds: 200),
                      curve: Curves.easeIn);
                  provider.getGeoFinders();
                },
              );
            },
          );
        },
        separatorBuilder: (context, index) => W(15),
      ),
    );
  }
}
