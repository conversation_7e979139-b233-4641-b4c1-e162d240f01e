import 'package:addc/features/geo_finder/models/geo_finder_model.dart';
import 'package:addc/features/geo_finder/view/geo_finder_detail_screen.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';

class GeoFinderTile extends StatelessWidget {
  final GeoFinderModel item;
  const GeoFinderTile({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String name = item.officeName ?? '';
    String address = item.address ?? '';
    return InkWell(
      onTap: () => Navigator.pushNamed(context, GeoFinderDetailScreen.route),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          H(15),
          Text(name, style: ts14c000940w4h1),
          H(10),
          Text(address, style: ts12c9D9D9Dw4h1),
          H(15),
        ],
      ),
    );
  }
}
