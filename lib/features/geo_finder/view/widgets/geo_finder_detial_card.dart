import 'package:addc/features/geo_finder/models/geo_finder_detail_model.dart';
import 'package:addc/features/geo_finder/models/geo_finder_model.dart';
import 'package:addc/features/geo_finder/view/widgets/geo_finder_tile.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class GeoFinderDetialCard extends StatelessWidget {
  final GeoFinderDetailModel item;
  const GeoFinderDetialCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String officeName = item.officeName ?? '';
    String address = item.address ?? '';
    String description = item.description ?? '';
    double? latitude = item.coordinates?.latitude;
    double? longitude = item.coordinates?.longitude;
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GeoFinderTile(
          item: GeoFinderModel(officeName: officeName, address: address),
        ),
        Divider(height: 0, color: ColorConstants.colorD9D9D9, thickness: 0.5),
        H(20),
        Text(description, style: ts12c9D9D9Dw4h1),
        H(30),
        Text('COORDINATES', style: ts12c9D9D9Dw4h1),
        H(5),
        Text('$latitude° N, $longitude° E'),
        H(21),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
              textStyle: ts14cFFFFFFw4h1, minimumSize: Size(125.w, 30.h)),
          onPressed: () =>
              openMap(latitude: latitude ?? 0, longitude: longitude ?? 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset('direct-up'.asIconSvg()),
              W(4),
              Text('View on map'),
            ],
          ),
        ),
      ],
    );
  }
}
