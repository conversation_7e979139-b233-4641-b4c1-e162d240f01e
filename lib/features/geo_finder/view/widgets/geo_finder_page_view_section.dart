import 'package:addc/features/geo_finder/models/geo_finder_model.dart';
import 'package:addc/features/geo_finder/providers/geo_finder_provider.dart';
import 'package:addc/features/geo_finder/view/widgets/geo_finder_tile.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_search_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class GeoFinderPageViewSection extends StatelessWidget {
  final PageController pageController;
  const GeoFinderPageViewSection({super.key, required this.pageController});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<GeoFinderProvider>();
    return Flexible(
      child: PageView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: provider.geoFinderTabBars.length,
        controller: pageController,
        itemBuilder: (context, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Container(
                  margin:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 18.h),
                  padding: EdgeInsets.only(left: 12.w, right: 12.w, top: 16.h),
                  decoration: BoxDecoration(
                      color: ColorConstants.colorFFFFFF,
                      borderRadius: BorderRadius.circular(18.r)),
                  child: Consumer<GeoFinderProvider>(
                      builder: (context, provider, _) {
                    if (provider.isLoading) {
                      final data = List.generate(
                          5,
                          (index) => {
                                "office_name": "Ghayati TAMM Center",
                                "address":
                                    "24 - Al Radah St - Ghiyathi - Al Dhafra Region 10868"
                              });
                      List<GeoFinderModel> items =
                          data.map((e) => GeoFinderModel.fromJson(e)).toList();
                      return Skeletonizer(
                        enabled: true,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonSearchTextFormField(
                              // controller: textEditingController,
                              hintText: 'Search...',
                              hintStyle: ts12c434343w4,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 10.w, vertical: 2.h),
                              prefixIcon: Container(
                                height: 20.h,
                                width: 20.w,
                                padding: EdgeInsets.all(10),
                                child: SvgPicture.asset(
                                  'search-lense'.asIconSvg(),
                                  height: 20.h,
                                  width: 20.w,
                                ),
                              ),
                            ),
                            H(25),
                            Text('List of ${provider.geoFinderTabBars[index]}',
                                style: ts16c000940w4h1),
                            Flexible(
                              child: ListView.separated(
                                shrinkWrap: true,
                                padding: EdgeInsets.symmetric(vertical: 16.h),
                                itemCount: items.length,
                                itemBuilder: (context, index) {
                                  GeoFinderModel item = items[index];
                                  return GeoFinderTile(item: item);
                                },
                                separatorBuilder: (context, index) => Divider(
                                    color: ColorConstants.colorD9D9D9,
                                    thickness: 0.5,
                                    height: 0),
                              ),
                            )
                          ],
                        ),
                      );
                    }
                    if (provider.geoFinders.isEmpty && !provider.isLoading) {
                      return Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text('No Data Found'),
                          ],
                        ),
                      );
                    }
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonSearchTextFormField(
                          // controller: textEditingController,
                          hintText: 'Search...',
                          hintStyle: ts12c434343w4,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.w, vertical: 2.h),
                          prefixIcon: Container(
                            height: 20.h,
                            width: 20.w,
                            padding: EdgeInsets.all(10),
                            child: SvgPicture.asset(
                              'search-lense'.asIconSvg(),
                              height: 20.h,
                              width: 20.w,
                            ),
                          ),
                        ),
                        H(25),
                        Text('List of ${provider.geoFinderTabBars[index]}',
                            style: ts16c000940w4h1),
                        Flexible(
                          child: ListView.separated(
                            shrinkWrap: true,
                            padding: EdgeInsets.symmetric(vertical: 16.h),
                            itemCount: provider.geoFinders.length,
                            itemBuilder: (context, index) {
                              GeoFinderModel item = provider.geoFinders[index];
                              return GeoFinderTile(item: item);
                            },
                            separatorBuilder: (context, index) => Divider(
                                color: ColorConstants.colorD9D9D9,
                                thickness: 0.5,
                                height: 0),
                          ),
                        )
                      ],
                    );
                  }),
                ),
              ),
            ],
          );
        },
        onPageChanged: (index) => provider.filterOnChanged(index: index),
      ),
    );
  }
}
