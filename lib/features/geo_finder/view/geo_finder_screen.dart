import 'package:addc/features/geo_finder/providers/geo_finder_provider.dart';
import 'package:addc/features/geo_finder/view/widgets/geo_finder_page_view_section.dart';
import 'package:addc/features/geo_finder/view/widgets/geo_finder_tab_section.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class GeoFinderScreen extends StatefulWidget {
  static const route = '/geo_finder_screen';
  const GeoFinderScreen({super.key});

  @override
  State<GeoFinderScreen> createState() => _GeoFinderScreenState();
}

class _GeoFinderScreenState extends State<GeoFinderScreen> {
  late PageController _pageController;
  late GeoFinderProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<GeoFinderProvider>();
    _pageController = PageController(initialPage: 0, keepPage: false);
    _provider.selectedGeoFiderTabIndex = 0;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _provider.getGeoFinders();
    });
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          AppBar(title: Text('GeoFinder'), leading: const CommonBackButton()),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          H(25),
          GeoFinderTabSection(pageController: _pageController),
          GeoFinderPageViewSection(pageController: _pageController)
        ],
      ),
    );
  }
}
