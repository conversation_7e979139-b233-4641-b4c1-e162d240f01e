import 'package:addc/features/geo_finder/models/geo_finder_detail_model.dart';
import 'package:addc/features/geo_finder/providers/geo_finder_provider.dart';
import 'package:addc/features/geo_finder/view/widgets/geo_finder_detial_card.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class GeoFinderDetailScreen extends StatefulWidget {
  static const route = '/geo_finder_detail_screen';
  const GeoFinderDetailScreen({super.key});

  @override
  State<GeoFinderDetailScreen> createState() => _GeoFinderDetailScreenState();
}

class _GeoFinderDetailScreenState extends State<GeoFinderDetailScreen> {
  late GeoFinderProvider _provider;
  late Future<GeoFinderDetailModel?> _geoFinderFuture;
  @override
  void initState() {
    super.initState();
    _provider = context.read<GeoFinderProvider>();
    _geoFinderFuture = _provider.getGeoFinderDetails();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          AppBar(leading: const CommonBackButton(), title: Text('GeoFinder')),
      body: Column(
        children: [
          H(29),
          Container(
            width: double.infinity,
            margin: EdgeInsets.symmetric(horizontal: 12.w),
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 30.h),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(18.r),
                color: ColorConstants.colorFFFFFF),
            child: FutureBuilder(
              future: _geoFinderFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  final data = {
                    "office_name": "TAQA Distribution HQ Abu Dhabi",
                    "address":
                        "12 - Al Bawakhir St - Al Danah - Abu Dhabi 22219",
                    "description":
                        "Lorem ipsum dolor sit amet consectetur. Dictumst odio nisl quam adipiscing porta.",
                    "coordinates": {"latitude": 24.2232, "longitude": 55.7229}
                  };

                  return Skeletonizer(
                      enabled: true,
                      child: GeoFinderDetialCard(
                          item: GeoFinderDetailModel.fromJson(data)));
                }
                GeoFinderDetailModel? data = snapshot.data;
                if (data == null) {
                  return Center(child: Text('No data available'));
                }
                return GeoFinderDetialCard(item: data);
              },
            ),
          ),
        ],
      ),
    );
  }
}
