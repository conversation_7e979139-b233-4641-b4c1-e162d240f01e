class GeoFinderDetailModel {
  String? officeName;
  String? address;
  String? description;
  Coordinates? coordinates;

  GeoFinderDetailModel(
      {this.officeName, this.address, this.description, this.coordinates});

  GeoFinderDetailModel.fromJson(Map<String, dynamic> json) {
    officeName = json['office_name'];
    address = json['address'];
    description = json['description'];
    coordinates = json['coordinates'] != null
        ? Coordinates.fromJson(json['coordinates'])
        : null;
  }
}

class Coordinates {
  double? latitude;
  double? longitude;

  Coordinates({this.latitude, this.longitude});

  Coordinates.fromJson(Map<String, dynamic> json) {
    latitude = json['latitude'];
    longitude = json['longitude'];
  }
}
