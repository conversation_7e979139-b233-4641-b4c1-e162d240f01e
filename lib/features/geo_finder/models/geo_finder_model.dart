// class GeoFinderModel {
//   int? oBJECTID;
//   int? aNCILLARYROLE;
//   int? eNABLED;
//   String? cREATIONUSER;
//   int? dATECREATED;
//   int? dATEMODIFIED;
//   String? lASTUSER;
//   String? fACILITYID;
//   int? iNSTALLATIONDATE;
//   double? rOTATION;
//   String? lIFECYCLESTATUS;
//   int? sUBTYPE;
//   String? pROJECTNUMBER;
//   int? lIFETIME;
//   int? oWNER;
//   String? cOMMENTS;
//   num? eLEVATION;
//   String? wORKORDERID;
//   String? dEPRECIATIONPERCENTAGE;
//   String? lABELTEXT;
//   String? dATASOURCE;
//   int? wATERTRACEWEIGHT;
//   String? cONTRACTOR;
//   String? cONSULTANT;
//   double? iNLETDIAMETERINCH;
//   int? iNLETDIAMETERMM;
//   int? dISCHARGEDIAMETERMM;
//   int? dISCHARGEDIAMETERINCH;
//   int? rATEDFLOW;
//   String? rATEDPOWER;
//   int? nETPOSITIVESUCTIONHEAD;
//   int? tOTALDYNAMICHEADM;
//   String? fLOWUNIT;
//   int? pUMPTYPE;
//   int? pUMPCURVEID;
//   String? mANUFACTURER;
//   String? mODELNUMBER;
//   String? sERIALNUMBER;
//   int? cOUPLING;
//   int? pUMPINGHOURS;
//   num? pRESSUREUPSTREAM;
//   num? pRESSUREDOWNSTREAM;
//   String? iNSULATIONPROTECTION;
//   String? dRAWINGNUMBER;
//   String? pROJECTTITLE;
//   int? cOMMISSIONINGDATE;
//   String? tOTALASSETVALUE;
//   int? nETPOSITIVESUCTIONHEADM;
//   String? dRYPROTECTIONFIELD;
//   String? mAXIMOTAG;
//   String? dEVELOPERNAME;
//   String? hANDEDOVER;
//   String? hANDOVERDATE;
//   String? oPERATIONMAINTENANCE;
//   String? gLOBALID;
//   String? uID;
//   String? mXASSETNUM;
//   String? mXLOCATION;
//   String? mXSITEID;
//   int? mXCREATIONSTATE;
//   String? rOWSTAMP;
//   String? mXSTATUS;
//   String? pUMPUSAGE;
//   String? aSSETCLASSIFICATION;
//   String? dATACATALOGNO;

//   GeoFinderModel(
//       {this.oBJECTID,
//       this.aNCILLARYROLE,
//       this.eNABLED,
//       this.cREATIONUSER,
//       this.dATECREATED,
//       this.dATEMODIFIED,
//       this.lASTUSER,
//       this.fACILITYID,
//       this.iNSTALLATIONDATE,
//       this.rOTATION,
//       this.lIFECYCLESTATUS,
//       this.sUBTYPE,
//       this.pROJECTNUMBER,
//       this.lIFETIME,
//       this.oWNER,
//       this.cOMMENTS,
//       this.eLEVATION,
//       this.wORKORDERID,
//       this.dEPRECIATIONPERCENTAGE,
//       this.lABELTEXT,
//       this.dATASOURCE,
//       this.wATERTRACEWEIGHT,
//       this.cONTRACTOR,
//       this.cONSULTANT,
//       this.iNLETDIAMETERINCH,
//       this.iNLETDIAMETERMM,
//       this.dISCHARGEDIAMETERMM,
//       this.dISCHARGEDIAMETERINCH,
//       this.rATEDFLOW,
//       this.rATEDPOWER,
//       this.nETPOSITIVESUCTIONHEAD,
//       this.tOTALDYNAMICHEADM,
//       this.fLOWUNIT,
//       this.pUMPTYPE,
//       this.pUMPCURVEID,
//       this.mANUFACTURER,
//       this.mODELNUMBER,
//       this.sERIALNUMBER,
//       this.cOUPLING,
//       this.pUMPINGHOURS,
//       this.pRESSUREUPSTREAM,
//       this.pRESSUREDOWNSTREAM,
//       this.iNSULATIONPROTECTION,
//       this.dRAWINGNUMBER,
//       this.pROJECTTITLE,
//       this.cOMMISSIONINGDATE,
//       this.tOTALASSETVALUE,
//       this.nETPOSITIVESUCTIONHEADM,
//       this.dRYPROTECTIONFIELD,
//       this.mAXIMOTAG,
//       this.dEVELOPERNAME,
//       this.hANDEDOVER,
//       this.hANDOVERDATE,
//       this.oPERATIONMAINTENANCE,
//       this.gLOBALID,
//       this.uID,
//       this.mXASSETNUM,
//       this.mXLOCATION,
//       this.mXSITEID,
//       this.mXCREATIONSTATE,
//       this.rOWSTAMP,
//       this.mXSTATUS,
//       this.pUMPUSAGE,
//       this.aSSETCLASSIFICATION,
//       this.dATACATALOGNO});

//   GeoFinderModel.fromJson(Map<String, dynamic> json) {
//     oBJECTID = json['OBJECTID'];
//     aNCILLARYROLE = json['ANCILLARYROLE'];
//     eNABLED = json['ENABLED'];
//     cREATIONUSER = json['CREATIONUSER'];
//     dATECREATED = json['DATECREATED'];
//     dATEMODIFIED = json['DATEMODIFIED'];
//     lASTUSER = json['LASTUSER'];
//     fACILITYID = json['FACILITYID'];
//     iNSTALLATIONDATE = json['INSTALLATIONDATE'];
//     rOTATION = json['ROTATION'];
//     lIFECYCLESTATUS = json['LIFECYCLESTATUS'];
//     sUBTYPE = json['SUBTYPE'];
//     pROJECTNUMBER = json['PROJECTNUMBER'];
//     lIFETIME = json['LIFETIME'];
//     oWNER = json['OWNER'];
//     cOMMENTS = json['COMMENTS'];
//     eLEVATION = json['ELEVATION'];
//     wORKORDERID = json['WORKORDERID'];
//     dEPRECIATIONPERCENTAGE = json['DEPRECIATIONPERCENTAGE'];
//     lABELTEXT = json['LABELTEXT'];
//     dATASOURCE = json['DATASOURCE'];
//     wATERTRACEWEIGHT = json['WATERTRACEWEIGHT'];
//     cONTRACTOR = json['CONTRACTOR'];
//     cONSULTANT = json['CONSULTANT'];
//     iNLETDIAMETERINCH = json['INLETDIAMETER_INCH'];
//     iNLETDIAMETERMM = json['INLETDIAMETER_MM'];
//     dISCHARGEDIAMETERMM = json['DISCHARGEDIAMETER_MM'];
//     dISCHARGEDIAMETERINCH = json['DISCHARGEDIAMETER_INCH'];
//     rATEDFLOW = json['RATEDFLOW'];
//     rATEDPOWER = json['RATEDPOWER'];
//     nETPOSITIVESUCTIONHEAD = json['NETPOSITIVESUCTIONHEAD'];
//     tOTALDYNAMICHEADM = json['TOTALDYNAMICHEAD_M'];
//     fLOWUNIT = json['FLOWUNIT'];
//     pUMPTYPE = json['PUMPTYPE'];
//     pUMPCURVEID = json['PUMPCURVEID'];
//     mANUFACTURER = json['MANUFACTURER'];
//     mODELNUMBER = json['MODELNUMBER'];
//     sERIALNUMBER = json['SERIALNUMBER'];
//     cOUPLING = json['COUPLING'];
//     pUMPINGHOURS = json['PUMPINGHOURS'];
//     pRESSUREUPSTREAM = json['PRESSUREUPSTREAM'];
//     pRESSUREDOWNSTREAM = json['PRESSUREDOWNSTREAM'];
//     iNSULATIONPROTECTION = json['INSULATIONPROTECTION'];
//     dRAWINGNUMBER = json['DRAWINGNUMBER'];
//     pROJECTTITLE = json['PROJECTTITLE'];
//     cOMMISSIONINGDATE = json['COMMISSIONINGDATE'];
//     tOTALASSETVALUE = json['TOTALASSETVALUE'];
//     nETPOSITIVESUCTIONHEADM = json['NETPOSITIVESUCTIONHEAD_M'];
//     dRYPROTECTIONFIELD = json['DRYPROTECTIONFIELD'];
//     mAXIMOTAG = json['MAXIMOTAG'];
//     dEVELOPERNAME = json['DEVELOPERNAME'];
//     hANDEDOVER = json['HANDEDOVER'];
//     hANDOVERDATE = json['HANDOVERDATE'];
//     oPERATIONMAINTENANCE = json['OPERATION_MAINTENANCE'];
//     gLOBALID = json['GLOBALID'];
//     uID = json['U_ID'];
//     mXASSETNUM = json['MXASSETNUM'];
//     mXLOCATION = json['MXLOCATION'];
//     mXSITEID = json['MXSITEID'];
//     mXCREATIONSTATE = json['MXCREATIONSTATE'];
//     rOWSTAMP = json['ROWSTAMP'];
//     mXSTATUS = json['MXSTATUS'];
//     pUMPUSAGE = json['PUMP_USAGE'];
//     aSSETCLASSIFICATION = json['ASSETCLASSIFICATION'];
//     dATACATALOGNO = json['DATACATALOGNO'];
//   }
// }
class GeoFinderModel {
  String? id;
  String? officeName;
  String? address;

  GeoFinderModel({this.id, this.officeName, this.address});

  GeoFinderModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    officeName = json['office_name'];
    address = json['address'];
  }
}
