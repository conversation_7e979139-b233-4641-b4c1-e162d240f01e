import 'package:addc/features/widgets/gap.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_bottom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../feeds/view/widgets/past_event_tile.dart';

class PastEventsScreen extends StatelessWidget {
  static const route = '/past_events_screen';
  const PastEventsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Past Events'),
        leading: const CommonBackButton(),
      ),
      body: ListView.separated(
        shrinkWrap: true,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 25.h),
        itemCount: 10,
        itemBuilder: (context, index) {
          return PastEventTile();
        },
        separatorBuilder: (context, index) => H(10),
      ),
      bottomNavigationBar: const CommonBottomAppbar(),
    );
  }
}
