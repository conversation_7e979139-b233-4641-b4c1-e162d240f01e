import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_bottom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../home/<USER>/models/home_upcoming_event_model.dart';
import '../../home/<USER>/upcoming_event_card.dart';

class UpcomingEventsScreen extends StatelessWidget {
  static const route = '/upcoming_events_screen.dart';
  const UpcomingEventsScreen({super.key});

  @override
  //UpcomingEventCard
  Widget build(BuildContext context) {
    final List<HomeUpcomingEventModel> items = [
      HomeUpcomingEventModel(
          date: 'Jan 20, 2025',
          backgroundImage: 'upcoming_event_1',
          title: 'Annual Day Celebration 2025',
          time: '09:00 AM - 10:00 PM'),
      HomeUpcomingEventModel(
          date: 'Jan 20, 2025',
          backgroundImage: 'upcoming_event_2',
          title: 'Annual Day Celebration 2025',
          time: '09:00 AM - 10:00 PM'),
      HomeUpcomingEventModel(
          date: 'Jan 20, 2025',
          backgroundImage: 'event_detail',
          title: 'Annual Day Celebration 2025',
          time: '09:00 AM - 10:00 PM'),
      HomeUpcomingEventModel(
          date: 'Jan 20, 2025',
          backgroundImage: 'past_event_1',
          title: 'Annual Day Celebration 2025',
          time: '09:00 AM - 10:00 PM'),
      HomeUpcomingEventModel(
          date: 'Jan 20, 2025',
          backgroundImage: 'past_event_2',
          title: 'Annual Day Celebration 2025',
          time: '09:00 AM - 10:00 PM'),
      HomeUpcomingEventModel(
          date: 'Jan 20, 2025',
          backgroundImage: 'past_event_1',
          title: 'Annual Day Celebration 2025',
          time: '09:00 AM - 10:00 PM'),
      HomeUpcomingEventModel(
          date: 'Jan 20, 2025',
          backgroundImage: 'event_detail',
          title: 'Annual Day Celebration 2025',
          time: '09:00 AM - 10:00 PM'),
      HomeUpcomingEventModel(
          date: 'Jan 20, 2025',
          backgroundImage: 'upcoming_event_2',
          title: 'Annual Day Celebration 2025',
          time: '09:00 AM - 10:00 PM'),
    ];
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upcoming Events'),
        leading: const CommonBackButton(),
      ),
      body: GridView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 25.h),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 9.w,
          mainAxisSpacing: 10.h,
          childAspectRatio: 180.w / 198.h,
        ),
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          return UpcomingEventCard(item: item);
        },
      ),
      bottomNavigationBar: const CommonBottomAppbar(),
    );
  }
}
