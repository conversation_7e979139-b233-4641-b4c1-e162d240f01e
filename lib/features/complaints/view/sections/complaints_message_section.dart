import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../../widgets/common_text_form_field.dart';
import '../../../widgets/gap.dart';

class ComplaintsMessageSection extends StatelessWidget {
  const ComplaintsMessageSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        H(25),
        Text('Message', style: ts14c000940w4h1),
        H(15),
        CommonTextFormField(
          hintText: 'Message here...',
          border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(20.r),
              borderSide: BorderSide(color: ColorConstants.colorF6F6F6)),
          contentPadding:
              EdgeInsets.symmetric(horizontal: 17.w, vertical: 12.5.h),
          maxLines: 7,
        ),
      ],
    );
  }
}
