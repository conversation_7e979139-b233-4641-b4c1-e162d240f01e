import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/common_dropdown_field.dart';
import '../../../widgets/gap.dart';
import '../../providers/complaint_provider.dart';

class ComplaintsToSection extends StatelessWidget {
  const ComplaintsToSection({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<ComplaintProvider>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Complaint To', style: ts14c000940w4h1),
        H(15),
        CommonDropdownFormField(
          hintText: 'Select',
          value: provider.selectedComplatintsTo,
          validator: (val) {
            if (val == null) {
              return 'This filed is required';
            }
            return null;
          },
          onChanged: (val) {
            provider.selectedComplatintsTo = val;
          },
          items: provider.complaintsToItems
              .map((e) => DropdownMenuItem(
                    enabled: true,
                    value: e,
                    child: Text(e),
                  ))
              .toList(),
        ),
      ],
    );
  }
}
