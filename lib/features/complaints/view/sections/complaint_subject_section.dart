import 'package:addc/features/widgets/standard_text_form_field.dart';
import 'package:flutter/cupertino.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';

class ComplaintSubjectSection extends StatelessWidget {
  const ComplaintSubjectSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        H(25),
        Text('Subject', style: ts14c000940w4h1),
        H(15),
        StandartTextForm<PERSON>ield(hintText: 'Enter subject'),
      ],
    );
  }
}
