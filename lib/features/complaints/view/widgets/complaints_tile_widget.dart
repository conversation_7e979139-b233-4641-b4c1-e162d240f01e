import 'package:addc/features/complaints/model/complaints_model.dart';
import 'package:flutter/material.dart';

import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/common_status_color_widget.dart';
import '../../../widgets/gap.dart';

class ComplaintsTileWidget extends StatelessWidget {
  final ComplaintsModel item;
  const ComplaintsTileWidget({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String complaint = item.complaint ?? '';
    String date = item.date ?? '';
    String status = item.status ?? '';
    String title = item.title ?? '';
    Color color = ColorConstants.colorFFBC47;
    switch (status.toLowerCase()) {
      case 'pending':
        color = ColorConstants.colorFFBC47;
        break;
      case 'resolved':
        color = ColorConstants.color0C902F;
        break;
    }
    return Column(
      children: [
        H(10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title, style: ts14c000940w4),
            CommonStatusColorWidget(color: color, status: status),
          ],
        ),
        H(8),
        Text(
          complaint,
          style: ts15c9D9D9Dw4h1,
        ),
        H(8),
        Align(
            alignment: Alignment.bottomRight,
            child: Text(date, style: ts13c9D9D9Dw4h1)),
        H(10),
      ],
    );
  }
}
