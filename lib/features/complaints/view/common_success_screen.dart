import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CommonSuccessScreen extends StatelessWidget {
  static const route = '/complaint_raised_success_screen';

  final String? imageInSVG;
  final String title;
  final String description;
  final VoidCallback onDonePressed;
  const CommonSuccessScreen(
      {super.key,
      required this.description,
      this.imageInSVG,
      required this.onDonePressed,
      required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.colorFFFFFF,
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            H(30 + kBottomNavigationBarHeight),
            SvgPicture.asset((imageInSVG ?? 'tick-circle_big').asIconSvg()),
            H(14),
            Text(
              title,
              style: ts32c000940w4h1,
              textAlign: TextAlign.center,
            ),
            H(15),
            Text(
              description,
              style: ts14c9D9D9Dw4h1,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        color: ColorConstants.colorFFFFFF,
        child: ElevatedButton(onPressed: onDonePressed, child: Text('Done')),
      ),
    );
  }
}
