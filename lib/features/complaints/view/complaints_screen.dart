import 'package:addc/features/complaints/model/complaints_model.dart';
import 'package:addc/features/complaints/view/raise_a_complaint_screen.dart';
import 'package:addc/features/complaints/view/widgets/complaints_tile_widget.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_bottom_appbar.dart';
import 'package:addc/widgets/common_search_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class ComplaintsScreen extends StatelessWidget {
  static const route = '/complaints_screen';
  const ComplaintsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final List<ComplaintsModel> items = [
      ComplaintsModel(
          title: 'Voice of HR',
          complaint:
              'I am unable to apply for leave for the dates 15/12/24 to 16/12/24 through the HRMS system. application seems...',
          status: 'Pending',
          date: '12 Dec 2024'),
      ComplaintsModel(
          title: 'Onsite Observation',
          complaint:
              'I have noticed an error in my attendance record for June. I was present, but it shows as absent...',
          status: 'Resolved',
          date: '05 Dec 2024'),
      ComplaintsModel(
          title: 'Voice of HR',
          complaint:
              'I have noticed an error in my attendance record for June. I was present, but it shows as absent...',
          status: 'Resolved',
          date: '22 Nov 2024'),
      ComplaintsModel(
          title: 'On Office Observation',
          complaint:
              'Frequent internet slowdowns are affecting productivity during peak hours.',
          status: 'Resolved',
          date: '12 Nov 2024'),
      ComplaintsModel(
          title: 'On Office Observation',
          complaint:
              'Frequent internet slowdowns are affecting productivity during peak hours.',
          status: 'Resolved',
          date: '12 Nov 2024'),
      ComplaintsModel(
          title: 'On Office Observation',
          complaint:
              'Frequent internet slowdowns are affecting productivity during peak hours.',
          status: 'Resolved',
          date: '12 Nov 2024'),
      ComplaintsModel(
          title: 'On Office Observation',
          complaint:
              'Frequent internet slowdowns are affecting productivity during peak hours.',
          status: 'Resolved',
          date: '12 Nov 2024'),
      ComplaintsModel(
          title: 'On Office Observation',
          complaint:
              'Frequent internet slowdowns are affecting productivity during peak hours.',
          status: 'Resolved',
          date: '12 Nov 2024'),
    ];
    return Scaffold(
      appBar:
          AppBar(title: Text('Complaints'), leading: const CommonBackButton()),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          H(25),
          Expanded(
            child: Column(
              children: [
                Flexible(
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 12.w),
                    padding:
                        EdgeInsets.only(left: 12.w, right: 12.w, top: 20.h),
                    decoration: BoxDecoration(
                        color: ColorConstants.colorFFFFFF,
                        borderRadius: BorderRadius.circular(18.r)),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonSearchTextFormField(
                          hintText: 'Search...',
                          hintStyle: ts12c434343w4,
                          prefixIcon: Padding(
                            padding: EdgeInsets.only(
                                left: 9.35.w,
                                right: 6.95.w,
                                top: 10.h,
                                bottom: 10.h),
                            child: SvgPicture.asset('search-thin'.asIconSvg()),
                          ),
                        ),
                        H(30),
                        Text('Complaints List', style: ts16c000940w4h1),
                        H(10),
                        //
                        Flexible(
                          child: ListView.separated(
                            shrinkWrap: true,
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            itemCount: items.length,
                            itemBuilder: (context, index) {
                              return ComplaintsTileWidget(item: items[index]);
                            },
                            separatorBuilder: (context, index) => Container(
                              height: 0.5,
                              color: ColorConstants.colorD9D9D9
                                  .withValues(alpha: 0.5),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // H(78),
          // const Spacer(),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            child: ElevatedButton(
                onPressed: () =>
                    Navigator.pushNamed(context, RaiseAComplaintScreen.route),
                child: Text('Add New')),
          ),
          H(8),
        ],
      ),
      bottomNavigationBar: const CommonBottomAppbar(),
    );
  }
}
