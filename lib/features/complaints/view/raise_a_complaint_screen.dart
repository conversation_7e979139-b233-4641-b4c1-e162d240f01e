import 'package:addc/features/complaints/providers/complaint_provider.dart';
import 'package:addc/features/complaints/view/common_success_screen.dart';
import 'package:addc/features/complaints/view/complaints_screen.dart';
import 'package:addc/features/complaints/view/sections/complaint_subject_section.dart';
import 'package:addc/features/complaints/view/sections/complaints_message_section.dart';
import 'package:addc/features/complaints/view/sections/complaints_to_section.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_bottom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class RaiseAComplaintScreen extends StatefulWidget {
  static const route = '/raise_a_complaint_screen';
  const RaiseAComplaintScreen({super.key});

  @override
  State<RaiseAComplaintScreen> createState() => _RaiseAComplaintScreenState();
}

class _RaiseAComplaintScreenState extends State<RaiseAComplaintScreen> {
  late ComplaintProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<ComplaintProvider>();
    _provider.selectedComplatintsTo = null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: Text('Raise a Complaint'), leading: const CommonBackButton()),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Column(
          children: [
            H(25),
            Container(
              width: double.infinity,
              margin: EdgeInsets.symmetric(horizontal: 12.w),
              padding: EdgeInsets.only(
                  left: 12.w, right: 12.w, top: 30.h, bottom: 15.h),
              decoration: BoxDecoration(
                  color: ColorConstants.colorFFFFFF,
                  borderRadius: BorderRadius.circular(18.r)),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const ComplaintsToSection(),
                    const ComplaintSubjectSection(),
                    const ComplaintsMessageSection(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 20),
        child: ElevatedButton(
            onPressed: () =>
                Navigator.pushNamed(context, CommonSuccessScreen.route,
                    arguments: CommonSuccessScreen(
                      description:
                          'Thank you for reaching out. Our team is\nreviewing your concern and will get back to\nyou shortly.',
                      imageInSVG: 'tick-circle_big',
                      onDonePressed: () => Navigator.popUntil(
                          context, ModalRoute.withName(ComplaintsScreen.route)),
                      title: 'Complaint Raised\nSuccessfully',
                    )),
            child: Text('Raise Complaint')),
      ),
      bottomNavigationBar: const CommonBottomAppbar(),
    );
  }
}
