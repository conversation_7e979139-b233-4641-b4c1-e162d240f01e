import 'package:addc/features/prayer_time/models/prayer_time_model.dart';
import 'package:addc/features/prayer_time/providers/prayer_time_provider.dart';
import 'package:addc/features/prayer_time/view/widgets/prayer_tile.dart';
import 'package:addc/features/prayer_time/view/widgets/prayer_time_card_widget.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class PrayerTimeScreen extends StatefulWidget {
  static const route = '/prayer_time_screen';
  const PrayerTimeScreen({super.key});

  @override
  State<PrayerTimeScreen> createState() => _PrayerTimeScreenState();
}

class _PrayerTimeScreenState extends State<PrayerTimeScreen> {
  late PrayerTimeProvider _provider;
  late Future<List<PrayerTimeModel>> _futureProvider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<PrayerTimeProvider>();
    _futureProvider = _provider.getPrayerTime();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          AppBar(title: Text('Prayer Time'), leading: const CommonBackButton()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: SingleChildScrollView(
          child: RefreshIndicator(
            onRefresh: () {
              _futureProvider = _provider.getPrayerTime();
              setState(() {});
              return _futureProvider;
            },
            child: Column(
              children: [
                H(22),
                const PrayerTimeCardWidget(),
                FutureBuilder(
                  future: _futureProvider,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return _prayerListWidget(isEnabled: true);
                    }
                    if (snapshot.connectionState == ConnectionState.done &&
                        snapshot.hasData) {
                      final List<PrayerTimeModel> items = snapshot.data ?? [];
                      return _prayerListWidget(isEnabled: false, items: items);
                    }
                    return const SizedBox.shrink();
                  },
                )
                // FutureBuilder(
                //   future: _futureProvider,
                //   builder: (context, snapshot) {
                //     if (snapshot.connectionState == ConnectionState.waiting) {
                //       return _listView(items: [], isEnabled: true);
                //     }
                //     if (snapshot.connectionState == ConnectionState.done &&
                //         snapshot.hasData) {
                //       final List<PrayerTimeModel> items = snapshot.data ?? [];
                //       log('items -- ${items.length}');
                //       return _listView(items: items, isEnabled: false);
                //     }
                //     return const SizedBox();
                //   },
                // ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _prayerListWidget(
      {required bool isEnabled, List<PrayerTimeModel>? items}) {
    List<PrayerTimeModel> data = [];
    for (PrayerTimeModel item in (items ?? [])) {
      String title = item.title?.trim().toLowerCase() ?? '';
      if (title == 'fajr' ||
          title == 'dhuhr' ||
          title == 'asr' ||
          title == 'maghrib' ||
          title == 'isha') {
        data.add(item);
      }
    }
    if (items == null) {
      data.addAll(List.generate(
          5,
          (index) =>
              PrayerTimeModel(isNext: false, time: '19:16', title: 'Sunset')));
    }
    return Skeletonizer(
      enabled: isEnabled,
      child: ListView.separated(
        shrinkWrap: true,
        padding: EdgeInsets.symmetric(vertical: 20.h),
        itemCount: data.length,
        itemBuilder: (context, index) {
          PrayerTimeModel? item = data[index];
          return PrayerTile(item: item);
        },
        separatorBuilder: (context, index) => H(12),
      ),
    );
  }

  // Widget _listView(
  //     {required List<PrayerTimeModel> items, required bool isEnabled}) {
  //   return Skeletonizer(
  //     enabled: isEnabled,
  //     child: SingleChildScrollView(
  //       child: Column(
  //         children: [
  //           Consumer<PrayerTimeProvider>(
  //             builder: (context, provider, _) {
  //               if (provider.specialPrayerTimes.isEmpty) {
  //                 return const SizedBox.shrink();
  //               }

  //               return Padding(
  //                 padding: EdgeInsets.symmetric(horizontal: 20.0.w),
  //                 child: Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     H(20),
  //                     Text('Today', style: TextStyle(fontSize: 20)),
  //                     H(10),
  //                     Container(
  //                       padding: EdgeInsets.symmetric(
  //                           horizontal: 16.w, vertical: 12.h),
  //                       decoration: BoxDecoration(
  //                         color: ColorConstants.colorFFFFFF,
  //                         borderRadius: BorderRadius.circular(18.r),
  //                       ),
  //                       child: Row(
  //                         spacing: 10,
  //                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                         children: provider.specialPrayerTimes.map((e) {
  //                           int index = provider.specialPrayerTimes.indexOf(e);
  //                           final item = provider.specialPrayerTimes[index];
  //                           String time = item.time ?? '';
  //                           if (time.isNotEmpty) {
  //                             time = convertTo12Hour(time);
  //                           }
  //                           return Text('${item.title ?? ''} : $time');
  //                         }).toList(),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               );
  //             },
  //           ),
  //           // Consumer<PrayerTimeProvider>(
  //           //   builder: (context, provider, _) {
  //           //     if (provider.upComingPrayerTime.isEmpty) {
  //           //       return const SizedBox.shrink();
  //           //     }
  //           //     return Padding(
  //           //       padding: EdgeInsets.symmetric(horizontal: 20.0.w),
  //           //       child: Column(
  //           //         crossAxisAlignment: CrossAxisAlignment.start,
  //           //         children: [
  //           //           H(20),
  //           //           Text('Upcoming', style: TextStyle(fontSize: 20)),
  //           //           H(10),
  //           //           PrayerTimeTile(item: provider.upComingPrayerTime.first)
  //           //         ],
  //           //       ),
  //           //     );
  //           //   },
  //           // ),
  //           Padding(
  //             padding: EdgeInsets.only(left: 16.w, top: 20),
  //             child: Align(
  //                 alignment: Alignment.centerLeft,
  //                 child: Text('Prayer Times', style: TextStyle(fontSize: 20))),
  //           ),
  //           ListView.separated(
  //             shrinkWrap: true,
  //             physics: const ClampingScrollPhysics(),
  //             padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
  //             itemCount: isEnabled ? 10 : items.length,
  //             itemBuilder: (context, index) {
  //               if (!isEnabled) {
  //                 final item = items[index];
  //                 return PrayerTimeTile(item: item);
  //               }
  //               return PrayerTimeTile(
  //                   item: PrayerTimeModel(title: 'ASR', time: '13:25'));
  //             },
  //             separatorBuilder: (context, index) => H(12),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }
}
