import 'package:addc/features/prayer_time/models/prayer_time_model.dart';
import 'package:addc/features/prayer_time/providers/prayer_time_provider.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:blur/blur.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class PrayerTimeCardWidget extends StatelessWidget {
  const PrayerTimeCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    List<Shadow> shadows = [
      Shadow(
        offset: Offset(0, 2), // X, Y offset like in Figma
        blurRadius: 4, // Adjust to match Figma blur
        color: Color.fromRGBO(0, 0, 0, 0.3), // #000000 with 30% opacity
      ),
    ];
    return ClipRRect(
      borderRadius: BorderRadiusGeometry.circular(16.r),
      child: SizedBox(
        height: 170.h,
        width: double.infinity,
        child: Consumer<PrayerTimeProvider>(
          builder: (context, provider, _) {
            bool isLoading = false;
            if (provider.isLoading && provider.upComingPrayerTime == null) {
              isLoading = true;
            }
            PrayerTimeModel sunRiseItem =
                provider.sunRisePrayerTime ?? PrayerTimeModel();
            PrayerTimeModel item =
                provider.upComingPrayerTime ?? PrayerTimeModel();
            String title = item.title ?? '';
            String time = item.time ?? '';
            String sunRiseTime = sunRiseItem.time ?? '';
            String bgImage = 'fajr_bg_image';
            if (time.isNotEmpty) {
              time = convertTo12Hour(time);
            }
            if (sunRiseTime.isNotEmpty) {
              sunRiseTime = convertTo12Hour(sunRiseTime);
            }
            switch (title.trim().toLowerCase()) {
              case 'fajr':
                bgImage = 'fajr_bg_image';
                break;
              case 'dhuhr':
                bgImage = 'dhuhr_bg_image';
                break;
              case 'asr':
                bgImage = 'asr_bg_image';
                break;
              case 'maghrib':
                bgImage = 'maghrib_bg_image';
                break;
              case 'isha':
                bgImage = 'isha_bg_image';
                break;
            }

            return Skeletonizer(
              enabled: isLoading,
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Image.asset(
                    bgImage.asImagePng(),
                    fit: BoxFit.cover,
                    height: 1200.h,
                    width: double.infinity,
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                        top: 20.h, bottom: 20.h, left: 16.w, right: 16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Text('23 June 2025, Monday', style: ts14cFFFFFFw4h1),
                        Text(
                          DateFormatter.formatDateTime(
                              dateTime: DateTime.now(),
                              outputFormat: 'EEEE, dd MMMM yyyy'),
                          style: TextStyle(
                              fontSize: 14,
                              height: 1,
                              color: Colors.white,
                              fontWeight: FontWeight.w400, // or any fill color
                              shadows: shadows),
                        ),
                        // H(35),
                        const Spacer(flex: 4),
                        // Text('Upcoming', style: ts14cFFFFFFw4h1,),
                        Text(
                          'Upcoming',
                          style: TextStyle(
                              fontSize: 14,
                              height: 1,
                              color: Colors.white,
                              fontWeight: FontWeight.w400, // or any fill color
                              shadows: shadows),
                        ),
                        const Spacer(flex: 2),
                        // H(16),
                        // Text(title, style: ts28cFFFFFFw4),
                        Text(
                          title,
                          style: TextStyle(
                              fontSize: 28,
                              height: 1,
                              color: Colors.white,
                              fontWeight: FontWeight.w400, // or any fill color
                              shadows: shadows),
                        ),
                        const Spacer(),
                        // H(14),
                        Row(
                          children: [
                            Text(
                              time,
                              style: TextStyle(
                                  fontSize: 28,
                                  height: 1,
                                  color: Colors.white,
                                  fontWeight:
                                      FontWeight.w400, // or any fill color
                                  shadows: shadows),
                            ),
                            const Spacer(),
                            if (!isLoading)
                              SvgPicture.asset('sun_rise'.asIconSvg()),
                            W(4),
                            Text(
                              'Sunrise $sunRiseTime',
                              style: TextStyle(
                                  fontSize: 14,
                                  height: 1,
                                  color: Colors.white,
                                  fontWeight:
                                      FontWeight.w400, // or any fill color
                                  shadows: shadows),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
