import 'dart:developer';

import 'package:addc/features/prayer_time/models/prayer_time_model.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../../utils/general_functions.dart';

class PrayerTile extends StatelessWidget {
  final PrayerTimeModel item;
  const PrayerTile({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String title = item.title ?? '';
    String time = item.time ?? '';
    bool isNext = item.isNext ?? false;
    String icon = 'fajr';
    String iconSelected = 'fajr_selected';
    if (time.isNotEmpty) {
      time = convertTo12Hour(time);
    }
    switch (title.trim().toLowerCase()) {
      case 'fajr':
        icon = 'fajr';
        iconSelected = 'fajr_selected';
        break;
      case 'dhuhr':
        icon = 'dhuhr';
        iconSelected = 'dhuhr_selected';
        break;
      case 'asr':
        icon = 'asr';
        iconSelected = 'asr_selected';
        break;
      case 'maghrib':
        icon = 'maghrib';
        iconSelected = 'maghrib_selected';
        break;
      case 'isha':
        icon = 'isha';
        iconSelected = 'isha_selected';
        break;
    }
    log('isNext $isNext - $iconSelected');
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        border: isNext ? Border.all(color: ColorConstants.primaryColor) : null,
        color: ColorConstants.colorFFFFFF,
        borderRadius: BorderRadius.circular(18.r),
      ),
      child: Row(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: ts16c000940w4h1),
              H(4),
              Text(time, style: ts14c9D9D9Dw4h1),
            ],
          ),
          const Spacer(),
          SvgPicture.asset((isNext ? iconSelected : icon).asIconSvg()),
        ],
      ),
    );
  }
}
