import 'package:addc/features/prayer_time/models/prayer_time_model.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../widgets/gap.dart';

class PrayerTimeTile extends StatelessWidget {
  final PrayerTimeModel item;
  const PrayerTimeTile({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String time = item.time ?? '';
    String title = item.title ?? '';
    bool isNext = item.isNext ?? false;
    if (time.isNotEmpty) {
      time = convertTo12Hour(time);
    }
    return Material(
      elevation: isNext ? 6 : 0,
      borderRadius: BorderRadius.circular(18.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
            boxShadow: isNext
                ? [
                    BoxShadow(
                        color: Colors.blueGrey.shade100,
                        blurRadius: 8,
                        spreadRadius: 0)
                  ]
                : [],
            color: ColorConstants.colorFFFFFF,
            borderRadius: BorderRadius.circular(18.r)),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Prayer'),
                  H(4),
                  Text('Prayer Time'),
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: Column(
                children: [
                  Text(':'),
                  H(4),
                  Text(':'),
                ],
              ),
            ),
            Expanded(
              flex: 8,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title),
                  H(4),
                  Text(time),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
