import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';

class PrayerTimeServices {
  Dio dio = Dio();
  CancelToken? _cancelToken;
  Future<Response> getPrayerTime() async {
    DateTime now = DateTime.now();
    String today =
        '${now.day.toString().padLeft(2, '0')}-${now.month.toString().padLeft(2, '0')}-${now.year}';
    if (_cancelToken != null && !_cancelToken!.isCancelled) {
      _cancelToken!.cancel("Cancelled previous request");
    }
    _cancelToken = CancelToken();
    final String url =
        'http://api.aladhan.com/v1/timingsByCity/$today?city=Abu+Dhabi&country=United+Arab+Emirates';
    Response? response = await dio.get(url, cancelToken: _cancelToken);
    log('response -> ${jsonEncode(response.data)} -> $today');

    return response;
  }
}
