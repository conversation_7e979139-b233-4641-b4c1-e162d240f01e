import 'package:addc/features/prayer_time/models/prayer_time_model.dart';
import 'package:addc/features/prayer_time/services/prayer_time_services.dart';
import 'package:flutter/material.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

class PrayerTimeProvider extends ChangeNotifier {
  bool isLoading = false;
  List<PrayerTimeModel> _prayerTimes = [];
  List<PrayerTimeModel> _specialPrayerTimes = [];
  PrayerTimeModel? upComingPrayerTime;
  PrayerTimeModel? sunRisePrayerTime;

  List<PrayerTimeModel> get prayerTimes => _prayerTimes;
  List<PrayerTimeModel> get specialPrayerTimes => _specialPrayerTimes;

  Future<List<PrayerTimeModel>> getPrayerTime() async {
    try {
      upComingPrayerTime = null;
      isLoading = true;
      final response = await PrayerTimeServices().getPrayerTime();
      isLoading = false;
      if (response.statusCode == 200) {
        Map<String, dynamic> json = response.data;
        if (json['status'] == 'OK' && json.containsKey('data')) {
          Map<String, dynamic> data = json['data'];
          if (data.containsKey('timings')) {
            Map<String, dynamic> timings = data['timings'];

            // Parse timings into PrayerTimeModel list
            List<PrayerTimeModel> allTimes = timings.entries.map((e) {
              return PrayerTimeModel(
                  title: e.key, time: e.value, isNext: false);
            }).toList();

            // Separate sunrise and Asr
            _specialPrayerTimes = allTimes
                .where((e) =>
                    e.title?.toLowerCase() == 'sunrise' ||
                    e.title?.toLowerCase() == 'sunset')
                .toList();
            _prayerTimes = allTimes.where((e) => e.time != null).toList();
            sunRisePrayerTime = allTimes.firstWhere(
              (e) => e.title?.toLowerCase() == 'sunrise',
              orElse: () => PrayerTimeModel(),
            );
            // Sort times to ensure correct order
            _prayerTimes.sort((a, b) => a.time!.compareTo(b.time!));
            _specialPrayerTimes.sort((a, b) => a.time!.compareTo(b.time!));

            // Mark next prayer
            _markNextPrayer();
            upComingPrayerTime = _prayerTimes.firstWhere(
                (e) => e.isNext == true,
                orElse: () => PrayerTimeModel());
            notifyListeners();
            return _prayerTimes;
          }
        }
      }
    } catch (e) {
      debugPrint("Prayer Time Error: ${e.toString()}");
    } finally {
      isLoading = false;
    }
    return [];
  }

  void _markNextPrayer() {
    // Initialize timezone only once
    tz.initializeTimeZones();
    final dubai = tz.getLocation('Asia/Dubai');
    final now = tz.TZDateTime.now(dubai);
    debugPrint('Current time (Asia/Dubai): ' + now.toString());
    bool found = false;

    for (var prayer in _prayerTimes) {
      // Reset all first
      prayer.isNext = false;

      debugPrint('Checking prayer: ${prayer.title} at time: ${prayer.time}');
      final parts = prayer.time?.split(':') ?? [];
      if (parts.length != 2) {
        debugPrint(
            'Skipping prayer due to invalid time format: ${prayer.time}');
        continue;
      }

      final hour = int.tryParse(parts[0]) ?? 0;
      final minute = int.tryParse(parts[1]) ?? 0;
      final prayerTime = tz.TZDateTime(
          now.location, now.year, now.month, now.day, hour, minute);
      debugPrint('Parsed prayerTime (Asia/Dubai): ' + prayerTime.toString());

      if (!found && prayerTime.isAfter(now)) {
        prayer.isNext = true;
        found = true;
        debugPrint('Set isNext = true for: ${prayer.title}');
      }
    }

    // If none is after now, mark first one (for next day)
    if (!found && _prayerTimes.isNotEmpty) {
      _prayerTimes[0].isNext = true;
      debugPrint(
          'No upcoming prayer found, set isNext = true for first prayer: ${_prayerTimes[0].title}');
    }

    // Extra debug: print all prayer times and their isNext values
    debugPrint('--- Final prayer times and isNext status ---');
    for (var prayer in _prayerTimes) {
      debugPrint(
          'Prayer: ${prayer.title}, Time: ${prayer.time}, isNext: ${prayer.isNext}');
    }
  }
}
