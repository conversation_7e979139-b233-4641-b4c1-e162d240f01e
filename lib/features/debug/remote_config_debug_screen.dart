import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/firebase_remote_config_service.dart';
import '../authentication/view/intune_test_screen.dart';

class RemoteConfigDebugScreen extends StatefulWidget {
  static const String route = '/remote-config-debug';
  const RemoteConfigDebugScreen({super.key});

  @override
  State<RemoteConfigDebugScreen> createState() =>
      _RemoteConfigDebugScreenState();
}

class _RemoteConfigDebugScreenState extends State<RemoteConfigDebugScreen> {
  Map<String, dynamic>? debugInfo;
  Map<String, String>? configValues;
  bool isLoading = false;
  String? lastAction;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    setState(() {
      isLoading = true;
    });

    try {
      final service = FirebaseRemoteConfigService.instance;
      debugInfo = service.getDebugInfo();
      configValues = service.getAllValues();
    } catch (e) {
      log('Error loading debug info: $e');
    }

    setState(() {
      isLoading = false;
    });
  }

  Future<void> _normalFetch() async {
    setState(() {
      isLoading = true;
      lastAction = 'Normal Fetch';
    });

    try {
      final updated =
          await FirebaseRemoteConfigService.instance.fetchAndActivate();
      log('Normal fetch result: $updated');
      await _loadDebugInfo();
    } catch (e) {
      log('Error during normal fetch: $e');
    }

    setState(() {
      isLoading = false;
    });
  }

  Future<void> _forceFetch() async {
    setState(() {
      isLoading = true;
      lastAction = 'Force Fetch';
    });

    try {
      final updated = await FirebaseRemoteConfigService.instance.forceFetch();
      log('Force fetch result: $updated');
      await _loadDebugInfo();
    } catch (e) {
      log('Error during force fetch: $e');
    }

    setState(() {
      isLoading = false;
    });
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Remote Config Debug'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _normalFetch,
                          child: const Text('Normal Fetch'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _forceFetch,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                          ),
                          child: const Text('Force Fetch'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Intune Test Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(context, IntuneTestScreen.route);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Test Intune SDK Integration'),
                    ),
                  ),
                  const SizedBox(height: 16),

                  if (lastAction != null) ...[
                    Text(
                      'Last Action: $lastAction',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Current URLs
                  _buildSection(
                    'Current URLs',
                    configValues != null
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: configValues!.entries
                                .map((e) => _buildKeyValue(e.key, e.value))
                                .toList(),
                          )
                        : const Text('No data'),
                  ),

                  const SizedBox(height: 16),

                  // Debug Information
                  _buildSection(
                    'Debug Information',
                    debugInfo != null
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildKeyValue('Initialized',
                                  debugInfo!['initialized'].toString()),
                              if (debugInfo!['initialized'] == true) ...[
                                _buildKeyValue('Last Fetch Time',
                                    debugInfo!['lastFetchTime']),
                                _buildKeyValue('Last Fetch Status',
                                    debugInfo!['lastFetchStatus']),
                                const SizedBox(height: 8),
                                const Text('Values:',
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold)),
                                ...debugInfo!['values']
                                    .entries
                                    .map<Widget>((e) =>
                                        _buildKeyValue('  ${e.key}', e.value))
                                    .toList(),
                                const SizedBox(height: 8),
                                const Text('Sources:',
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold)),
                                ...debugInfo!['sources']
                                    .entries
                                    .map<Widget>((e) =>
                                        _buildKeyValue('  ${e.key}', e.value))
                                    .toList(),
                              ],
                            ],
                          )
                        : const Text('No debug info'),
                  ),

                  const SizedBox(height: 16),

                  // Raw JSON
                  _buildSection(
                    'Raw Debug JSON',
                    GestureDetector(
                      onTap: () => _copyToClipboard(
                        const JsonEncoder.withIndent('  ').convert(debugInfo),
                      ),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Text(
                          debugInfo != null
                              ? const JsonEncoder.withIndent('  ')
                                  .convert(debugInfo)
                              : 'No data',
                          style: const TextStyle(
                              fontFamily: 'monospace', fontSize: 12),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Instructions
                  _buildSection(
                    'Instructions',
                    const Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            '1. Normal Fetch: Respects the 1-hour minimum fetch interval'),
                        Text(
                            '2. Force Fetch: Bypasses the minimum fetch interval for testing'),
                        Text(
                            '3. Check "Sources" to see if values come from "remote" or "default"'),
                        Text('4. Tap the Raw JSON to copy it to clipboard'),
                        Text(
                            '5. If Source shows "default", the remote values haven\'t been fetched yet'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSection(String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        content,
      ],
    );
  }

  Widget _buildKeyValue(String key, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              '$key:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ],
      ),
    );
  }
}
