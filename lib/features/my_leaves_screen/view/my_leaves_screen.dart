import 'package:addc/features/home/<USER>/models/my_leaves_model.dart';
import 'package:addc/features/home/<USER>/providers/my_leaves_provider.dart';
import 'package:addc/features/home/<USER>/my_leaves_tile_widget.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_divider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class MyLeavesScreen extends StatefulWidget {
  static const route = '/my_leaves_screen';
  const MyLeavesScreen({super.key});

  @override
  State<MyLeavesScreen> createState() => _MyLeavesScreenState();
}

class _MyLeavesScreenState extends State<MyLeavesScreen> {
  late MyLeavesProvider _provider;

  @override
  void initState() {
    super.initState();
    _provider = context.read<MyLeavesProvider>();
    _provider.initMyLeavesPagination();
  }

  @override
  void dispose() {
    super.dispose();
    _provider.myLeavesPagingController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          AppBar(leading: const CommonBackButton(), title: Text('My Leaves')),
      body: Container(
        decoration: BoxDecoration(
            color: ColorConstants.colorFFFFFF,
            borderRadius: BorderRadius.circular(18.r)),
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20),
        child: PagedListView.separated(
          shrinkWrap: true,
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 20),
          pagingController: _provider.myLeavesPagingController,
          builderDelegate: PagedChildBuilderDelegate<MyLeavesModel>(
              animateTransitions: true,
              firstPageErrorIndicatorBuilder: (context) => Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height / 3),
                  child: Text('Something went wrong')),
              firstPageProgressIndicatorBuilder: (context) =>
                  _skeletonizer(itemCount: 20),
              newPageErrorIndicatorBuilder: (context) => Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height / 3),
                  child: Text('Something went wrong')),
              newPageProgressIndicatorBuilder: (context) =>
                  _skeletonizer(itemCount: 1),
              noItemsFoundIndicatorBuilder: (context) => Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height / 3),
                  child: Center(child: Text('There is no data found'))),
              itemBuilder: (context, item, index) =>
                  MyLeavesTileWidget(item: item)),
          separatorBuilder: (context, index) => CommonDivider(),
        ),
      ),
    );
  }

  Widget _skeletonizer({required int itemCount}) {
    final item = MyLeavesModel(
        startDate: '2023-12-10',
        endDate: '2023-12-10',
        absenceType: 'Approved',
        approvalStatusCd: 'Approved');
    return Skeletonizer(
      enabled: true,
      child: ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        shrinkWrap: true,
        itemCount: itemCount,
        itemBuilder: (context, index) => MyLeavesTileWidget(item: item),
        separatorBuilder: (context, index) => CommonDivider(),
      ),
    );
  }
}
