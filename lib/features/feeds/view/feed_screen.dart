import 'package:addc/features/feeds/providers/feeds_provider.dart';
import 'package:addc/features/feeds/view/sections/feed_event_tab_view.dart';
import 'package:addc/features/feeds/view/sections/feed_majilisna_tab_view.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../master_screen/view/widgets/master_appbar.dart';
import 'sections/feeds_announcement_tab_view.dart';
import 'sections/feeds_tab_section.dart';

class FeedScreen extends StatefulWidget {
  const FeedScreen({super.key});

  @override
  State<FeedScreen> createState() => _FeedScreenState();
}

class _FeedScreenState extends State<FeedScreen> {
  late PageController _pageController;
  late FeedsProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<FeedsProvider>();
    _pageController = PageController(initialPage: 0, keepPage: false);
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        _provider.selectedTabBarIndex = 0;
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MasterAppBar(
          textEditingController: TextEditingController(), context: context),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FeedsTabSection(pageController: _pageController),
          Expanded(
            child: PageView.builder(
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _provider.feedTabBars.length,
              controller: _pageController,
              itemBuilder: (context, index) {
                switch (_provider.selectedTabBarIndex) {
                  case 0:
                    return const FeedEventTabView();
                  case 1:
                    return const FeedMajilisnaTabView();
                  case 2:
                    return const FeedsAnnouncementTabView();
                }
                return const SizedBox();
              },
              onPageChanged: (index) => _provider.selectedTabBarIndex = index,
            ),
          ),
        ],
      ),
    );
  }
}
