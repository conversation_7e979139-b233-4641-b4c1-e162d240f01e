import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';

import '../../../../shared/constants/text_styles.dart';
import '../../../../shared/helper/image_picker_provider.dart';
import '../../../../widgets/common_text_form_field.dart';

class PostBottomSheet extends StatelessWidget {
  const PostBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<ImagePickerProvider>();

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton(
                style: TextButton.styleFrom(
                    textStyle: ts18cB3B6C6w4h1,
                    foregroundColor: ColorConstants.colorB3B6C6),
                onPressed: () => Navigator.pop(context),
                child: Text('Cancel')),
            TextButton(
                style: TextButton.styleFrom(
                    textStyle: ts18c0AB3A1w4h1,
                    foregroundColor: ColorConstants.color0AB3A1),
                onPressed: () => Navigator.pop(context),
                child: Text('Post')),
          ],
        ),
        H(10),
        CommonTextFormField(
          fillColor: ColorConstants.colorF6F6F6,
          hintText: 'What’s on your mind?',
          contentPadding:
              EdgeInsets.symmetric(horizontal: 18.w, vertical: 14.h),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              color: ColorConstants.colorF6F6F6,
              width: 0.5,
            ),
            borderRadius: BorderRadius.circular(20.r),
          ),
          maxLines: 13,
        ),
        H(5),
        Row(
          children: [
            IconButton(
                onPressed: () async {
                  await provider.imagePicker(source: ImageSource.camera);
                },
                icon: SvgPicture.asset('camera'.asIconSvg())),
            IconButton(
                onPressed: () async {
                  await provider.imagePicker(source: ImageSource.gallery);
                },
                icon: SvgPicture.asset('gallery'.asIconSvg())),
          ],
        ),
        H(10),
      ],
    );
  }
}
