import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../event_detail/view/event_detalis_screen.dart';
import '../../../widgets/gap.dart';

class PastEventTile extends StatelessWidget {
  const PastEventTile({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => Navigator.pushNamed(context, EventDetalisScreen.route),
      child: Container(
        // height: 110.h,
        width: double.infinity,
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(18.r),
            color: ColorConstants.colorFFFFFF),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: Stack(
                children: [
                  Image.asset(
                    width: 130.h,
                    height: 98.h,
                    fit: BoxFit.cover,
                    'past_event_1'.asDummyPng(),
                  ),
                  Container(
                    margin: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        border: Border.all(
                            width: 0.5,
                            color: ColorConstants.colorFFFFFF
                                .withValues(alpha: .01)),
                        color:
                            ColorConstants.color0AB3A1.withValues(alpha: 0.5)),
                    padding:
                        EdgeInsets.symmetric(vertical: 5.5.h, horizontal: 6.w),
                    child: Text('Dec 20, 2024', style: ts10cFFFFFFw4h1),
                  )
                ],
              ),
            ),
            W(15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Work Anniversary\nCelebrations', style: ts14c000940w4h1),
                  Text('09:00 AM - 10:00 PM', style: ts12c9D9D9Dw4),
                  H(8),
                  Row(
                    children: [
                      SvgPicture.asset('location_small'.asIconSvg()),
                      W(2),
                      Text('Work Anniversary Celebrations', style: ts12c9D9D9Dw4,overflow: TextOverflow.ellipsis,),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
