import 'package:addc/features/feeds/view/model/announcement_model.dart';
import 'package:flutter/material.dart';

import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';

class FeedAnnouncementTile extends StatelessWidget {
  final AnnouncementModel item;
  const FeedAnnouncementTile({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String title = item.title ?? '';
    String announcement = item.announcement ?? '';
    String time = item.time ?? '';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        H(20),
        Text(title, style: ts16c000940w4h1),
        H(6),
        Text(announcement, style: ts14c9D9D9Dw4h1),
        H(14),
        Align(
            alignment: Alignment.bottomRight,
            child: Text(time, style: ts12c9D9D9Dw4h1)),
        H(10),
      ],
    );
  }
}
