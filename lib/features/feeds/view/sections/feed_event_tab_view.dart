import 'package:flutter/material.dart';
import '../../../home/<USER>/sections/home_upcoming_event_section.dart';
import 'feeds_past_event_section.dart';

class FeedEventTabView extends StatelessWidget {
  const FeedEventTabView({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HomeUpcomingEventSection(titleGap: 5, bottomGap: 10),
          UpcomingPastEventSection(),
        ],
      ),
    );
  }
}
