import 'package:addc/features/feeds/view/model/announcement_model.dart';
import 'package:addc/features/feeds/view/widgets/feed_announcement_tile.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_divider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FeedsAnnouncementTabView extends StatelessWidget {
  const FeedsAnnouncementTabView({super.key});

  @override
  Widget build(BuildContext context) {
    final List<AnnouncementModel> items = [
      AnnouncementModel(
          title: 'Office Closure Notice',
          announcement:
              'Dear Team, please be informed that the office will remain closed on 01 Jan 2024 in observance of New Year. ',
          time: 'Just now'),
      AnnouncementModel(
          title: 'Payroll Update',
          announcement:
              'Salaries for the month of dEC 2024 have been processed. Please check your account for details.',
          time: '15 Dec, 2024'),
      AnnouncementModel(
          title: 'Employee of the Month',
          announcement:
              'Congratulations to <PERSON><PERSON><PERSON> <PERSON> for being selected as the Employee of the Month! ',
          time: '10 Dec, 2024'),
    ];
    return Column(
      children: [
        H(20),
        Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              child: Text('Announcements', style: ts20c000940w4),
            )),
        H(10),
        Container(
            margin: EdgeInsets.symmetric(horizontal: 12.w),
            decoration: BoxDecoration(
                color: ColorConstants.colorFFFFFF,
                borderRadius: BorderRadius.circular(18.r)),
            child: ListView.separated(
              shrinkWrap: true,
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              itemCount: items.length,
              itemBuilder: (context, index) {
                return FeedAnnouncementTile(item: items[index]);
              },
              separatorBuilder: (context, index) => CommonDivider(
                  thickness: 0.5,
                  height: 0,
                  color: ColorConstants.color9D9D9D.withValues(alpha: 0.5)),
            )),
      ],
    );
  }
}
