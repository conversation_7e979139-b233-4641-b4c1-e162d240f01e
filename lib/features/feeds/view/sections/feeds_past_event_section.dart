import 'package:addc/features/feeds/view/widgets/past_event_tile.dart';
import 'package:addc/features/past_events/view/past_events_screen.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../widgets/common_section_title.dart';

class UpcomingPastEventSection extends StatelessWidget {
  const UpcomingPastEventSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(left: 12.w),
          child: CommonSectionTitle(
              title: 'Past Events',
              onViewAllPressed: () =>
                  Navigator.pushNamed(context, PastEventsScreen.route)),
        ),
        ListView.separated(
          shrinkWrap: true,
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 4,
          itemBuilder: (context, index) {
            return PastEventTile();
          },
          separatorBuilder: (context, index) => H(10),
        ),
        H(MediaQuery.paddingOf(context).bottom + kBottomNavigationBarHeight),
      ],
    );
  }
}
