import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../shared/constants/color_constants.dart';
import '../../../../widgets/common_bottom_sheet.dart';
import '../../../../widgets/common_circle_avatar.dart';
import '../../../../widgets/common_text_form_field.dart';
import '../widgets/post_bottom_sheet.dart';

class WhatsOnYourMindSection extends StatelessWidget {
  const WhatsOnYourMindSection({super.key});

  @override
  Widget build(BuildContext context) {
    return CommonTextFormField(
      hintText: 'What’s on your mind?',
      contentPadding: EdgeInsets.symmetric(horizontal: 9.w, vertical: 9.59.h),
      fillColor: ColorConstants.colorFFFFFF,
      border: OutlineInputBorder(
        borderSide: BorderSide(
          color: ColorConstants.colorF6F6F6,
          width: 0.5,
        ),
        borderRadius: BorderRadius.circular(14.r),
      ),
      prefixIcon: Padding(
        padding: EdgeInsets.only(top: 9.h, bottom: 9.h, left: 8.w, right: 10.w),
        child: CommonCircleAvatar(image: 'john'),
      ),
      readOnly: true,
      onTap: () => callBottomSheet(ctx: context, body: const PostBottomSheet()),
    );
  }
}
