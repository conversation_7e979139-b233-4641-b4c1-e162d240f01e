import 'package:addc/features/feeds/view/sections/whats_on_your_mind_section.dart';
import 'package:addc/features/home/<USER>/latest_feed_card_widget.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FeedMajilisnaTabView extends StatelessWidget {
  const FeedMajilisnaTabView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: SingleChildScrollView(
        child: Column(
          children: [
            H(20),
            const WhatsOnYourMindSection(),
            ListView.separated(
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.symmetric(vertical: 20.h),
              shrinkWrap: true,
              itemCount: 2,
              itemBuilder: (context, index) {
                return const LatestFeedCardWidget();
              },
              separatorBuilder: (context, index) => H(14),
            )
          ],
        ),
      ),
    );
  }
}
