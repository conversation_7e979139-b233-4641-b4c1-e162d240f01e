import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/feeds_provider.dart';
import 'feed_event_tab_view.dart';

class FeedsTabView extends StatelessWidget {
  const FeedsTabView({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<FeedsProvider>();
    switch (provider.selectedTabBarIndex) {
      case 0:
        return const FeedEventTabView();
      case 1:
        return Container(
          height: 30,
          width: 30,
          color: Colors.red,
        );
    }
    return const Placeholder();
  }
}
