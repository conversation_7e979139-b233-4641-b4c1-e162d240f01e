import 'package:addc/widgets/common_tab_bar_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../widgets/gap.dart';
import '../../providers/feeds_provider.dart';

class FeedsTabSection extends StatelessWidget {
  final PageController pageController;
  const FeedsTabSection({super.key, required this.pageController});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<FeedsProvider>();
    return Column(
      children: [
        H(25),
        SizedBox(
          height: 36.h,
          child: ListView.separated(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            itemCount: provider.feedTabBars.length,
            itemBuilder: (context, index) {
              return Consumer<FeedsProvider>(
                builder: (context, provider, _) {
                  bool isSelected = provider.selectedTabBarIndex == index;
                  return CommonTabBarTile(
                    title: provider.feedTabBars[index],
                    isSelected: isSelected,
                    onTap: () {
                      provider.onFeedsTabBarChanaged(index: index);
                      pageController.animateToPage(index,
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.easeIn);
                    },
                  );
                },
              );
            },
            separatorBuilder: (context, index) => W(15),
          ),
        ),
      ],
    );
  }
}
