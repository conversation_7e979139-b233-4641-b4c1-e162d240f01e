import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';

import '../../../../shared/constants/color_constants.dart';

class SplashBackground extends StatelessWidget {
  final double? opacity;
  const SplashBackground({super.key, this.opacity});

  @override
  Widget build(BuildContext context) {
    return Stack(
      fit: StackFit.passthrough,
      children: [
        Container(
          decoration: BoxDecoration(color: ColorConstants.color000940),
        ),
        <PERSON>gn(
          alignment: Alignment.bottomCenter,
          child: Container(
            height: MediaQuery.of(context).size.height / 2.3,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.bottomCenter,
                radius: 1,
                colors: [
                  ColorConstants.color071986,
                  ColorConstants.color071986.withAlpha(0)
                ],
              ),
            ),
          ),
        ),
        Opacity(
            opacity: opacity ?? 1,
            child: Image.asset('splash_pattern'.asImagePng()))
      ],
    );
  }
}
