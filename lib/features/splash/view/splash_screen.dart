import 'dart:developer';

import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/authentication/providers/login_provider.dart';
import 'package:addc/features/authentication/view/login_screen.dart';
import 'package:addc/features/master_screen/providers/master_provider.dart';
import 'package:addc/features/master_screen/view/master_screen.dart';
import 'package:addc/features/more/providers/more_provider.dart';
import 'package:addc/features/onbaording_screens/onbaording_screens.dart';
import 'package:addc/features/splash/view/widget/splash_background.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/services/firebase_remote_config_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'package:provider/provider.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:addc/features/authentication/services/biometric_auth_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SplashScreen extends StatefulWidget {
  static const String route = '/splash';
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  late MasterProvider _masterProvider;
  late LogInProvider _loginProvider;
  late MoreProvider _moreProvider;
  final _secureStorage = const FlutterSecureStorage();
  final BiometricAuthService _bioServises = BiometricAuthService();
  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        fit: StackFit.passthrough,
        children: [
          SplashBackground(),
          Column(
            children: [
              Image.asset(
                'splash_pattern'.asImagePng(),
                width: double.infinity,
              ),
              H(19.h),
              SvgPicture.asset(
                'splash_logo'.asIconSvg(),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 20.0.h),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(
                    'assets/lottie/splash-loader.gif',
                    height: 80.h,
                    width: 80.h,
                  ),
                  // Text(
                  //   'A TAQA GROUP\nCOMPANY',
                  //   textAlign: TextAlign.right,
                  //   style: TextStyle(
                  //     fontSize: 13.sp,
                  //     fontWeight: FontWeight.w900,
                  //     color: Colors.white,
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _init() async {
    _masterProvider = context.read<MasterProvider>();
    _loginProvider = context.read<LogInProvider>();
    _moreProvider = context.read<MoreProvider>();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _masterProvider.masterIndex = 0;
    });
    // await Future.delayed(Duration(days: 2));
    // Initialize and fetch Firebase Remote Config
    try {
      log('Initializing Firebase Remote Config...');
      await FirebaseRemoteConfigService.instance.initialize();

      // Get debug info before fetch
      final debugInfoBefore =
          FirebaseRemoteConfigService.instance.getDebugInfo();
      log('Debug Info Before Fetch: $debugInfoBefore');

      // Try normal fetch first
      bool updated =
          await FirebaseRemoteConfigService.instance.fetchAndActivate();
      log('Normal fetch result: $updated');

      // If you want to force fetch for testing (bypasses 1-hour limit), uncomment below:
      // bool forceFetchResult = await FirebaseRemoteConfigService.instance.forceFetch();
      // log('Force fetch result: $forceFetchResult');

      // Get debug info after fetch
      final debugInfoAfter =
          FirebaseRemoteConfigService.instance.getDebugInfo();
      log('Debug Info After Fetch: $debugInfoAfter');

      // Log the fetched URLs for debugging
      final configValues = FirebaseRemoteConfigService.instance.getAllValues();
      log('Remote Config Values: $configValues');
      log('Oracle Base URL: ${FirebaseRemoteConfigService.instance.oracleBaseUrl}');
      log('WebTA Base URL: ${FirebaseRemoteConfigService.instance.webtaBaseUrl}');
      log('Taqa Maximo Base URL: ${FirebaseRemoteConfigService.instance.taqaMaximoBaseUrl}');
    } catch (e) {
      log('Error initializing Remote Config: $e');
      // Continue with default values if Remote Config fails
    }

    _bioServises.supportState = await _bioServises.isDeviceSupported();
    String? accessToken = await _secureStorage.read(key: 'access_token');

    Future.delayed(
      Duration(milliseconds: 5100),
      () async {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        _moreProvider.switchValue =
            prefs.getBool('is_biometrics_enabled') ?? false;
        log('_moreProvider.switchValue - ${_moreProvider.switchValue}');
        if (accessToken == null) {
          // Clear any existing data if token is invalid or expired
          // await LoginedUser.clearData();
          if (!mounted) return;
          Navigator.pushReplacementNamed(context, OnbaordingScreens.route);
          return;
        } else {
          if (_moreProvider.switchValue) {
            await LoginedUser.getUser();
            await _loginProvider.getProfileImage();
            if (_bioServises.supportState == SupportState.supported) {
              if (await _bioServises.checkBiometrics()) {
                final avaliableMetrics =
                    await _bioServises.getAvailableBiometrics();
                if (avaliableMetrics.isNotEmpty) {
                  BiometricStatus biometricStatus =
                      await _bioServises.authenticate();
                  log('_bioServides.isAuthorized -> ${biometricStatus}');
                  // await _authenticateWithBiometrics();
                  if (biometricStatus == BiometricStatus.authorized) {
                    await Future.delayed(Duration(seconds: 2));
                    if (!mounted) return;
                    Navigator.pushReplacementNamed(context, MasterScreen.route);
                    return;
                  } else {
                    if (!mounted) return;
                    Navigator.pushReplacementNamed(context, LoginScreen.route);
                    return;
                  }
                }
              } else {
                if (!mounted) return;
                Navigator.pushReplacementNamed(context, MasterScreen.route);
                return;
              }
            } else if (_bioServises.supportState == SupportState.unknown ||
                _bioServises.supportState == SupportState.unsupported) {
              if (!mounted) return;
              Navigator.pushReplacementNamed(context, MasterScreen.route);
              return;
            }
            // await LoginedUser.getUser();
            // await _loginProvider.getProfileImage();
            if (!mounted) return;
            Navigator.pushReplacementNamed(context, MasterScreen.route);
            return;
          } else {
            await LoginedUser.getUser();
            await _loginProvider.getProfileImage();
            if (!mounted) return;
            Navigator.pushReplacementNamed(context, MasterScreen.route);
          }
        }
      },
    );
  }
}
