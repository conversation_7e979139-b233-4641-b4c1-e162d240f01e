import 'package:addc/features/inbox/widgets/cc_and_b_details_card.dart';
import 'package:addc/features/inbox/widgets/permission_request_modal.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/features/widgets/secondary_elevated_button.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CcAndBDetailsScreen extends StatelessWidget {
  static const route = '/cc_and-b-details-screen';

  const CcAndBDetailsScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          'CC&B',
          style: ts20c000940w4,
        ),
        leading: const CommonBackButton(),
      ),
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 12.w, top: 12.h, right: 12.w),
            child: Column(
              children: [
                H(25),
                Container(
                  decoration: BoxDecoration(
                    color: ColorConstants.colorFFFFFF,
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: CcAndBDetailsCard(
                    idno: 12344,
                    applicationFor: 'temporary water connection ',
                    caseStatus: "Connection cleared",
                    accIdno: 78966,
                    caseIdNo: 5424115,
                    name: 'Mansour Al-Juaid',
                    buttoncolor: ColorConstants.colorFFBC47,
                    buttontext: 'In Progress',
                    idNumber: 35656,
                    statusdate: '04 Dec, 2024',
                    entryIdno: 65676,
                  ),
                ),
                H(15),
              ],
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                      child: SecondaryElevatedButton(
                    text: 'Reject',
                  )),
                  W(15),
                  Expanded(
                    child: ElevatedButton(
                        onPressed: () {
                          showModalBottomSheet(
                            backgroundColor: Colors.transparent,
                            isScrollControlled: true,
                            context: context,
                            builder: (builder) {
                              return PermissionRequestModal(
                                modalmessage:
                                    "Permission request\napproved successfully",
                                onPressed: () {},
                              );
                            },
                          );
                        },
                        child: Text("Approve")),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
