import 'package:addc/features/home/<USER>/providers/my_leaves_provider.dart';
import 'package:addc/features/inbox/providers/inbox_provider.dart';
import 'package:addc/features/inbox/providers/webta_provider.dart';
import 'package:addc/features/inbox/widgets/tabbar.dart';
import 'package:addc/features/inbox/widgets/time_and_attendace_container.dart';
import 'package:addc/features/leaves/view/leaves_screen.dart';
import 'package:addc/features/master_screen/view/widgets/master_appbar.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class InboxScreen extends StatefulWidget {
  static const route = '/inbox-screen';

  final bool isShowMasterAppBar;
  const InboxScreen({super.key, required this.isShowMasterAppBar});

  @override
  State<InboxScreen> createState() => _InboxScreenState();
}

class _InboxScreenState extends State<InboxScreen> {
  final TextEditingController searchController = TextEditingController();
  late MyLeavesProvider _provider;

  @override
  void initState() {
    super.initState();
    _provider = context.read<MyLeavesProvider>();
    _provider.initInBoxLeavesPagination();
    initWebTA();
  }

  initWebTA() async {
    final provider = context.read<WebTAProvider>();
    await provider.getEmpPendingPermission();
  }

  @override
  void dispose() {
    super.dispose();
    _provider.inBoxLeavesPagingController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: !widget.isShowMasterAppBar
          ? AppBar(leading: const CommonBackButton(), title: Text('Inbox'))
          : MasterAppBar(
              textEditingController: TextEditingController(), context: context),
      body: Padding(
        padding: EdgeInsets.only(left: 12.w, top: 12.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            H(10.h),
            InboxTabBar(searchController: searchController),
            H(20),
            Expanded(
              child: Consumer<InboxProvider>(
                builder: (context, inboxOracleProvider, _) {
                  Widget child;
                  switch (inboxOracleProvider.selectedCategoryIndex) {
                    // case 0:
                    //   child = OracleEbsContainer();
                    //   break;
                    case 0:
                      child = TimeAndIndexContainer();
                      break;
                    case 1:
                      child = InBoxLeaveHistoryModule();
                      break;
                    // case 3:
                    //   child = CmtContainer();
                    //   break;
                    // case 4:
                    //   child = CcAndBContainer();
                    //   break;
                    default:
                      child = SizedBox.shrink();
                  }
                  return Padding(
                      padding: EdgeInsets.only(right: 12.w), child: child);
                },
              ),
            ),
            H(20),
          ],
        ),
      ),
    );
  }
}
