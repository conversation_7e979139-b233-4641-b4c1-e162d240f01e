// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:addc/features/inbox/models/webta_model.dart';
import 'package:addc/features/inbox/providers/webta_provider.dart';
import 'package:addc/features/inbox/widgets/reason_card.dart';
import 'package:addc/features/inbox/widgets/time_and_attendance_details_card.dart';
import 'package:addc/features/master_screen/view/master_screen.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/common_success_bottom_sheet.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_confirmation_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class TimeAndAttendancedetailsScreen extends StatelessWidget {
  static const route = '/time-and-attendance-screen';
  final WebTAModel item;
  const TimeAndAttendancedetailsScreen({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<WebTAProvider>();
    String appId = item.appId ?? '';
    String requestDescription = item.requestDescription ?? '';

    return Scaffold(
      appBar: AppBar(
          centerTitle: true,
          title: Text('Time & Attendance', style: ts20c000940w4),
          leading: const CommonBackButton()),
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 12.w, top: 12.h, right: 12.w),
            child: Column(
              children: [
                H(25),
                Container(
                  decoration: BoxDecoration(
                    color: ColorConstants.colorFFFFFF,
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: TimeAndAttendanceDetailsCard(item: item),
                ),
                if (requestDescription.isNotEmpty) ...[
                  H(15),
                  ReasonCard(remarksExplanation: requestDescription),
                ]
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(
            left: 16.0, right: 16.w, bottom: Platform.isAndroid ? 20 : 30),
        child: Row(
          spacing: 15.w,
          children: [
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: ColorConstants.color8B8D97),
                onPressed: () async {
                  showModalBottomSheet(
                      backgroundColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.all(Radius.circular(30.r))),
                      context: context,
                      builder: (builder) => CommonConfirmationDialog(
                            title: 'Time & Attendace Rejection',
                            content: 'Are you sure do you want to Reject ?',
                            onConfirm: () async {
                              bool? isSuccess =
                                  await provider.postWebTAPermissionApproval(
                                      appID: appId, appStatus: 3); //reject
                              Navigator.pop(context);
                              if (isSuccess == true) {
                                successBottomsheet(
                                    context: context,
                                    title:
                                        'Permission request\nrejected successfully',
                                    status: BottomSheetStatus.success,
                                    onPressed: () => Navigator.of(context)
                                        .popUntil(ModalRoute.withName(
                                            MasterScreen.route)));
                              }
                            },
                          ));
                },
                child: Text("Reject"),
              ),
            ),
            Expanded(
              child: ElevatedButton(
                child: Text("Approve"),
                onPressed: () async {
                  showModalBottomSheet(
                      backgroundColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.all(Radius.circular(30.r))),
                      context: context,
                      builder: (builder) => CommonConfirmationDialog(
                            title: 'Time & Attendace Approval',
                            content: 'Are you sure do you want to approve ?',
                            onConfirm: () async {
                              bool? isSuccess =
                                  await provider.postWebTAPermissionApproval(
                                      appID: appId, appStatus: 2); //Approve
                              Navigator.pop(context);
                              if (isSuccess == true) {
                                successBottomsheet(
                                    context: context,
                                    title:
                                        'Permission request\napproved successfully',
                                    status: BottomSheetStatus.success,
                                    onPressed: () => Navigator.of(context)
                                        .popUntil(ModalRoute.withName(
                                            MasterScreen.route)));
                              }
                            },
                          ));
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
