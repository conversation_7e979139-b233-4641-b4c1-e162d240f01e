import 'package:addc/features/inbox/widgets/cmt_details_card.dart';
import 'package:addc/features/inbox/widgets/cmt_remarks_card.dart';
import 'package:addc/features/inbox/widgets/permission_request_modal.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/features/widgets/secondary_elevated_button.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CmtDetailsScreen extends StatelessWidget {
  static const route = '/cmt-details-screen';
  
  const CmtDetailsScreen({super.key, });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          'CMT',
          style: ts20c000940w4,
        ),
        leading: const CommonBackButton(),
      ),
      body: Stack(
        children: [
          Padding(
             padding: EdgeInsets.only(left: 12.w, top: 12.h, right: 12.w),
            child: Column(
             
              children: [
                H(25),
                Container(
                  decoration: BoxDecoration(
                    color: ColorConstants.colorFFFFFF,
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: CmtDetailsCard(
                    idno: 54356,
                    name: 'Mansour Al-Juaid',
                    buttoncolor: ColorConstants.colorFFBC47,
                    buttontext: 'In Progress',
                    idNumber: 35656,
                    department: 'Connection Department  ',
                    forSignature: 'For Signature',
                    statusdate: '04 Dec, 2024',
                    documentReferenceNo: 65676,
                    
                  ),
                ),
                H(15),
                Row(
                  children: [
                    Expanded(child: RemarksCard(reasonexplanation: 'No remarks',)),
                  ],
                )
              ],
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                      child: SecondaryElevatedButton(
                    text: 'Reject',
                  )),
                  W(15),
                  Expanded(
                    child: ElevatedButton(
                        onPressed: () {
                          showModalBottomSheet(
                            backgroundColor: Colors.transparent,
                            isScrollControlled: true,
                            context: context,builder: (builder) {
                            return PermissionRequestModal(modalmessage: "Permission request\napproved successfully",onPressed: () {
                              
                            },);
                          },);
                        }, child: Text("Approve")),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
