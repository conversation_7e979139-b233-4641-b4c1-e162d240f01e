import 'package:addc/features/inbox/models/maximo_model.dart';
import 'package:addc/features/inbox/widgets/status_button.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

class MaximoCard extends StatelessWidget {
  final MaximoModel item;
  const MaximoCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String orderID = item.oWNERRECORID ?? '';
    String description = item.dESCRIPTION ?? '';
    String date = item.dUEDATE ?? '';
    if (date.isNotEmpty) {
      DateTime dateTime = DateTime.parse(date);
      date = DateFormat('dd MMM, yyyy').format(dateTime);
    }

    String status = 'Pending';
    Color statusButtonColor = ColorConstants.colorFFBC47;
    switch (status) {
      case 'Pending':
        statusButtonColor = ColorConstants.colorFFBC47;
        break;
      case 'Approved':
        statusButtonColor = ColorConstants.color0C902F;
        break;
    }
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          SizedBox(
            width: MediaQuery.of(context).size.width - 48.w,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text('Work Order - ', style: ts12c9D9D9Dw4),
                          Text(orderID, style: ts12c000940w4),
                        ],
                      ),
                      Text(
                        description,
                        style: ts14c000940w4,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // StatusButton(
                    //     color: statusButtonColor,
                    //     text: status,
                    //     onPressed: () {}),
                    // H(15),
                    Text(date, style: ts12c9D9D9Dw4),
                  ],
                )
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.only(
                        left: 27.w, right: 25.w, top: 9.h, bottom: 9.h),
                    width: 74.w,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: ColorConstants.colorFFCFCD,
                    ),
                    child: SvgPicture.asset('close-circle'.asIconSvg()),
                  ),
                ),
                H(2),
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.only(
                        left: 27.w, right: 25.w, top: 9.h, bottom: 9.h),
                    width: 74.w,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: ColorConstants.colorCDEAD4,
                    ),
                    child: SvgPicture.asset('tick-circle'.asIconSvg()),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
