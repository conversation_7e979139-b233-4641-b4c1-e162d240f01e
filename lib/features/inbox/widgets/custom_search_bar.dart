import 'package:addc/features/utils/extensions.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CustomSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final void Function(String)? onFieldSubmitted;
  final void Function(String)? onChanged;
  const CustomSearchBar(
      {super.key,
      required this.controller,
      this.onFieldSubmitted,
      this.onChanged});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      onFieldSubmitted: onFieldSubmitted,
      onChanged: onChanged,
      decoration: InputDecoration(
        hintText: 'Search...',
        hintStyle: ts12c434343w4,
        prefixIcon: Padding(
          padding: EdgeInsets.only(left: 10.w, top: 14.h, bottom: 14.h),
          child: SvgPicture.asset(
            'search-normal'.asIconSvg(),
          ),
        ),
        fillColor: ColorConstants.colorF6F6F6,
        filled: true,
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.transparent),
          borderRadius: BorderRadius.circular(25.7),
        ),
      ),
    );
  }
}
