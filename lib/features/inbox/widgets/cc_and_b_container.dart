import 'package:addc/features/inbox/models/buttons.dart';
import 'package:addc/features/inbox/widgets/cc_and_b_card.dart';
import 'package:addc/features/inbox/widgets/custom_search_bar.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class CcAndBContainer extends StatelessWidget {
  
  const CcAndBContainer({super.key});
  

  @override
  Widget build(BuildContext context) {
     final List<Buttons> button = [
      Buttons(buttonColor: ColorConstants.color0C902F, buttonText:"Approved" ),
      Buttons(buttonColor: ColorConstants.colorFFBC47, buttonText:"Pending" ),
      Buttons(buttonColor: ColorConstants.color0C902F, buttonText:"Approved" ),
      Buttons(buttonColor: ColorConstants.color0C902F, buttonText:"Approved" ),
      But<PERSON>(buttonColor: ColorConstants.color0C902F, buttonText:"Approved" ),
      But<PERSON>(buttonColor: ColorConstants.color0C902F, buttonText:"Approved" )
    ];
     TextEditingController searchController =TextEditingController();
    return Container(
            decoration: BoxDecoration(
              color: ColorConstants.colorFFFFFF,
              borderRadius: BorderRadius.circular(18),
            ),
            child: Padding(
              padding: EdgeInsets.only(top: 14.h, left: 12.w, right: 12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomSearchBar(controller: searchController,),
                  H(20),
                  Text(
                    "CC&B",
                    style: ts16c000940w4,
                  ),
              
                  Expanded(
                    child: ListView.separated(
                      separatorBuilder: (context, index) {
                return Container(
                  height: 1,
                  width: MediaQuery.of(context).size.width * 0.9,
                  color: ColorConstants.color9D9D9D.withValues(alpha: 0.1),
                );
              },
                      itemCount: button.length,
                      itemBuilder: (context, index) {
                        return CcAndBCard(
                          statusdate: '01 Jan, 2025',
                          applicationFor: 'Application for temporary water connection ',
                          contractType: 'General Contracting',
                          buttoncolor: button[index].buttonColor??Colors.transparent,
                          buttontext: button[index].buttonText.toString(),
                          ontapforStatusButton: () {},
                        );
                      },
                    ),
                  )
                ],
              ),
            ),
          );
  }
}