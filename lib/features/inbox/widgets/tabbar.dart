import 'package:addc/features/home/<USER>/providers/my_leaves_provider.dart';
import 'package:addc/features/inbox/providers/inbox_provider.dart';
import 'package:addc/features/inbox/providers/webta_provider.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class InboxTabBar extends StatelessWidget {
  final TextEditingController searchController;
  const InboxTabBar({super.key, required this.searchController});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          Consumer<InboxProvider>(
            builder: (context, provider, _) {
              return Container(
                decoration: BoxDecoration(),
                height: 36,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: provider.categories.length,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () async {
                        searchController.clear();
                        provider.onCategoryTap(index);
                        _onTap(index: index, context: context);
                      },
                      child: Container(
                        margin: EdgeInsets.only(right: 10),
                        padding: EdgeInsets.symmetric(
                          horizontal: 10,
                        ),
                        decoration: BoxDecoration(
                          color: provider.selectedCategoryIndex == index
                              ? ColorConstants.color000940
                              : Colors.white,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          provider.categories[index],
                          style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: provider.selectedCategoryIndex == index
                                  ? Colors.white
                                  : ColorConstants.color111111),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  _onTap({required int index, required BuildContext context}) async {
    switch (index) {
      // case 0:
      //   final provider = context.read<MaximoProvider>();
      //   await provider.getMaximoList();
      //   break;
      case 0:
        final provider = context.read<WebTAProvider>();
        await provider.getEmpPendingPermission();
      case 1:
        final provider = context.read<MyLeavesProvider>();
        provider.onInBoxCategoryChanged(index: 0);
        // provider.selectedCategoryIndex = 0;
        // provider.inBoxLeavesCurrentPage = 0;
        // provider.inBoxLeavesPagingController.refresh();
        break;
    }
  }
}
