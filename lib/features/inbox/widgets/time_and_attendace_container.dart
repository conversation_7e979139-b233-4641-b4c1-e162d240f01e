import 'package:addc/features/inbox/models/webta_model.dart';
import 'package:addc/features/inbox/providers/webta_provider.dart';
import 'package:addc/features/inbox/widgets/custom_search_bar.dart';
import 'package:addc/features/inbox/widgets/time_and_attendance_card.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

// ignore: must_be_immutable
class TimeAndIndexContainer extends StatefulWidget {
  const TimeAndIndexContainer({super.key});

  @override
  State<TimeAndIndexContainer> createState() => _TimeAndIndexContainerState();
}

class _TimeAndIndexContainerState extends State<TimeAndIndexContainer> {
  late WebTAProvider _provider;
  final TextEditingController searchController = TextEditingController();
  // final List<Buttons> button = [
  List<WebTAModel> filteredPermissions = [];

  @override
  void initState() {
    super.initState();
    _provider = context.read<WebTAProvider>();
    // Initially, show all
    _provider.filteredPermissions = _provider.pendingPermissions;
    searchController.addListener(() =>
        _provider.onSearchChanged(query: searchController.text.toLowerCase()));
  }

  @override
  void dispose() {
    searchController.removeListener(() =>
        _provider.onSearchChanged(query: searchController.text.toLowerCase()));
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<WebTAProvider>();
    return Container(
      decoration: BoxDecoration(
        color: ColorConstants.colorFFFFFF,
        borderRadius: BorderRadius.circular(18),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 14.h, left: 12.w, right: 12.w),
            child: CustomSearchBar(controller: searchController),
          ),
          H(20),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Text("WebTA", style: ts16c000940w4),
          ),
          Expanded(
            child: Builder(
              builder: (context) {
                if (provider.isListLoading) {
                  return Skeletonizer(
                    enabled: true,
                    child: ListView.separated(
                        padding: EdgeInsets.symmetric(horizontal: 12.w),
                        itemBuilder: (context, index) {
                          WebTAModel item = WebTAModel(
                              appDate: '21/12/2023',
                              appEnd: '21/12/2023 12:49:00',
                              appStart: '21/12/2023 12:49:00',
                              appStatusDesc: 'Test',
                              appTypeDesc: 'Official',
                              name: 'ABDULRAHMAN HASAN',
                              violationMins: '01:34',
                              statusDesc: 'Success');
                          return TimeAndAttendanceCard(
                              item: item, index: index);
                        },
                        separatorBuilder: (context, index) => _divider(),
                        itemCount: 10),
                  );
                }

                // Handle error state
                if (provider.hasError) {
                  return Center(
                    child: Padding(
                      padding: EdgeInsets.all(20.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48.w,
                            color: Colors.red,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            provider.errorMessage ?? 'An error occurred',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.red,
                            ),
                          ),
                          SizedBox(height: 16.h),
                          ElevatedButton(
                            onPressed: () {
                              provider.clearError();
                              provider.getEmpPendingPermission();
                            },
                            child: Text('Retry'),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                if (provider.filteredPermissions.isNotEmpty &&
                    !provider.isListLoading) {
                  return ListView.separated(
                      shrinkWrap: true,
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                      itemCount: provider.filteredPermissions.length,
                      itemBuilder: (context, index) {
                        WebTAModel item = provider.filteredPermissions[index];
                        return TimeAndAttendanceCard(item: item, index: index);
                      },
                      separatorBuilder: (context, index) => _divider());
                }
                return Center(child: Text('No Data Found'));
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _divider() {
    return Container(
      height: 1,
      width: MediaQuery.of(context).size.width * 0.9,
      color: ColorConstants.color9D9D9D.withValues(alpha: 0.1),
    );
  }
}
