import 'package:addc/features/inbox/models/maximo_model.dart';
import 'package:addc/features/inbox/providers/maximo_provider.dart';
import 'package:addc/features/inbox/widgets/custom_search_bar.dart';
import 'package:addc/features/inbox/widgets/maximo_card.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class MaximoContainer extends StatelessWidget {
  final TextEditingController searchController;
  const MaximoContainer({super.key, required this.searchController});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorConstants.colorFFFFFF,
        borderRadius: BorderRadius.circular(18),
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 14.h, left: 12.w, right: 12.w),
        child: Consumer<MaximoProvider>(
          builder: (context, provider, _) {
            if (provider.isLoading) {
              return ListView.separated(
                shrinkWrap: true,
                physics: const ClampingScrollPhysics(),
                itemCount: 10, // Shows 4 loading items
                itemBuilder: (context, index) {
                  // Create a dummy MaximoModel for each loading item
                  // You can make these values more dynamic if needed, e.g., 'DummyID_$index'
                  MaximoModel item = MaximoModel(
                    wFASSIGNMENTID: 61939881,
                    cANINTERFACE: 'BOTH',
                    dESCRIPTION:
                        'Loading Item Description', // Placeholder description
                    dUEDATE: '2025-02-09T18:59:47+04:00', // Placeholder date
                    oWNERDESCRIPTION: 'Loading Owner Description',
                    oWNERRECORID: 'DW-265333',
                    oWNERTABLE: 'SR',
                  );
                  return Skeletonizer(
                      enabled: true, child: MaximoCard(item: item));
                },
                separatorBuilder: (context, index) => Container(
                  height: 1,
                  width: double.infinity,
                  color: ColorConstants.color9D9D9D.withValues(alpha: 0.2),
                ),
              );
            }
            if (!provider.isLoading && provider.maximos.isEmpty) {
              return Center(child: Text('No data found'));
            }
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomSearchBar(controller: searchController),
                H(20),
                Text("Maximo", style: ts16c000940w4),
                Expanded(
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const ClampingScrollPhysics(),
                    itemCount: provider.maximos.length,
                    itemBuilder: (context, index) {
                      MaximoModel item = provider.maximos[index];
                      return MaximoCard(item: item);
                    },
                    separatorBuilder: (context, index) => Container(
                      height: 1,
                      width: double.infinity,
                      color: ColorConstants.color9D9D9D.withValues(alpha: 0.2),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
