import 'package:addc/features/inbox/views/cc_and_b_details_screen.dart';
import 'package:addc/features/inbox/widgets/status_button.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CcAndBCard extends StatelessWidget {
  final Color buttoncolor;
  final String buttontext;
  final String contractType;
  final String applicationFor;
  final VoidCallback? ontapforStatusButton;
  final VoidCallback? tickOntap;
  final VoidCallback? closeOntap;
  final String? statusdate;
  const CcAndBCard(
      {super.key,
      required this.buttoncolor,
      required this.buttontext,
      required this.contractType,
      this.ontapforStatusButton,
      this.tickOntap,
      required this.statusdate,
      required this.applicationFor,
      this.closeOntap});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () {
            Navigator.pushNamed(context, CcAndBDetailsScreen.route);
          },
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width - 48.w,
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                H(5),
                                Text(
                                  contractType,
                                  style: ts12c9D9D9Dw4,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  applicationFor,
                                  style: ts14c000940w4,
                                ),
                              ],
                            ),
                          ),
                          StatusButton(
                            color: buttoncolor,
                            text: buttontext,
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text(
                              statusdate ?? '',
                              style: ts12c9D9D9Dw4,
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 10.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: closeOntap,
                        child: Container(
                          padding: EdgeInsets.only(
                              left: 27.w, right: 25.w, top: 9.h, bottom: 9.h),
                          width: 74.w,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            color: ColorConstants.colorFFCFCD,
                          ),
                          child: SvgPicture.asset(
                            'close-circle'.asIconSvg(),
                          ),
                        ),
                      ),
                      H(2),
                      InkWell(
                        onTap: tickOntap,
                        child: Container(
                          padding: EdgeInsets.only(
                              left: 27.w, right: 25.w, top: 9.h, bottom: 9.h),
                          width: 74.w,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            color: ColorConstants.colorCDEAD4,
                          ),
                          child: SvgPicture.asset(
                            'tick-circle'.asIconSvg(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
