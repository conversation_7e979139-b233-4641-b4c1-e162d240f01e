import 'package:addc/features/inbox/models/maximo_model.dart';
import 'package:addc/features/inbox/widgets/status_button.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class OracleEbsCard extends StatelessWidget {
  final MaximoModel item;
  const OracleEbsCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String ownerTable = item.oWNERTABLE ?? '';
    String dueDate = item.dUEDATE ?? '';
    String description = item.dESCRIPTION ?? '';
    if (dueDate.isNotEmpty) {
      final date = DateTime.parse(dueDate);
      dueDate = DateFormatter.formatDateTime(
          dateTime: date, outputFormat: 'MMM dd, yyyy');
    }
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.867,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        H(10),
                        Text(ownerTable, style: ts12c9D9D9Dw4),
                        Row(
                          children: [
                            Text(dueDate, style: ts14c000940w4),
                            // W(2.5),
                            // Container(
                            //   height: 3.h,
                            //   width: 3.w,
                            //   decoration: BoxDecoration(
                            //       shape: BoxShape.circle,
                            //       color: ColorConstants.color000940),
                            // ),
                            // W(2.5),
                            // Text(days, style: ts14c000940w4),
                          ],
                        ),
                      ],
                    ),
                    // StatusButton(color: buttoncolor, text: buttontext)
                  ],
                ),
                H(15),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Text(
                        description,
                        style: ts12c000940w4,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ),
                    // Row(
                    //   children: [
                    //     Image.asset(
                    //       height: 17.h,
                    //       width: 17.w,
                    //       'user-image'.asImagePng(),
                    //     ),
                    //     W(5),
                    //     Text(
                    //       name,
                    //       style: ts12c000940w4,
                    //     ),
                    //   ],
                    // ),
                    // Text(
                    //   statusdate,
                    //   style: ts12c9D9D9Dw4,
                    // ),
                  ],
                ),
                H(10),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.only(
                        left: 27.w, right: 25.w, top: 9.h, bottom: 9.h),
                    width: 74.w,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: ColorConstants.colorFFCFCD,
                    ),
                    child: SvgPicture.asset(
                      'close-circle'.asIconSvg(),
                    ),
                  ),
                ),
                H(2),
                InkWell(
                  onTap: () {},
                  child: Container(
                    padding: EdgeInsets.only(
                        left: 27.w, right: 25.w, top: 9.h, bottom: 9.h),
                    width: 74.w,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: ColorConstants.colorCDEAD4,
                    ),
                    child: SvgPicture.asset(
                      'tick-circle'.asIconSvg(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
