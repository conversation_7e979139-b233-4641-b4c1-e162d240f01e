import 'package:addc/features/inbox/models/maximo_model.dart';
import 'package:addc/features/inbox/providers/maximo_provider.dart';
import 'package:addc/features/inbox/widgets/custom_search_bar.dart';
import 'package:addc/features/inbox/widgets/oracle_ebs_card.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

// ignore: must_be_immutable
class OracleEbsContainer extends StatelessWidget {
  OracleEbsContainer({super.key});

  final TextEditingController searchController = TextEditingController();
  final MaximoModel item = MaximoModel(
      dESCRIPTION:
          'Work permit for SCMS- MBZPRY5 for OT Cybersecurity installation.',
      dUEDATE: '2025-02-27T13:24:06+04:00',
      oWNERTABLE: 'OWNERTABLE');
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: ColorConstants.colorFFFFFF,
          borderRadius: BorderRadius.circular(18)),
      child: Padding(
        padding: EdgeInsets.only(top: 14.h, left: 12.w, right: 12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomSearchBar(
              controller: searchController,
              onChanged: (v) {
                context.read<MaximoProvider>().filterByDescription(v);
              },
            ),
            Expanded(
              child: Consumer<MaximoProvider>(
                builder: (context, provider, _) {
                  if (provider.isLoading) {
                    return _maximoListWidget(provider: provider);
                  }
                  if (provider.maximos.isEmpty && !provider.isLoading) {
                    return Center(child: Text('No Maximos Found'));
                  }
                  return _maximoListWidget(provider: provider);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _maximoListWidget({required MaximoProvider provider}) {
    return Skeletonizer(
      enabled: provider.isLoading,
      child: ListView.separated(
        padding: EdgeInsets.only(top: 20.h, bottom: 20.h),
        separatorBuilder: (context, index) {
          return Container(
            height: 1,
            width: MediaQuery.of(context).size.width * 0.9,
            color: ColorConstants.color9D9D9D.withValues(alpha: 0.1),
          );
        },
        itemCount: provider.isLoading ? 10 : provider.maximos.length,
        itemBuilder: (context, index) {
          if (provider.isLoading) {
            return OracleEbsCard(item: item);
          }
          return OracleEbsCard(item: provider.maximos[index]);
        },
      ),
    );
  }
}
