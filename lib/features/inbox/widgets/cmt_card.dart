import 'package:addc/features/inbox/views/cmt_details_screen.dart';
import 'package:addc/features/inbox/widgets/status_button.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CmtCard extends StatelessWidget {
  final Color buttoncolor;
  final String buttontext;
  final int? idNumber;
  final String department;
  final String forSignature;
  final String statusdate;
  final VoidCallback? ontapforStatusButton;
  final VoidCallback? tickOntap;
  final VoidCallback? closeOntap;
  const CmtCard(
      {super.key,
      required this.buttoncolor,
      required this.buttontext,
      required this.idNumber,
      required this.department,
      required this.forSignature,
      required this.statusdate,
      this.ontapforStatusButton,
      this.tickOntap,
      this.closeOntap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, CmtDetailsScreen.route);
      },
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.867,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          H(10),
                          Row(
                            children: [
                              Text(
                                "ID - ",
                                style: ts12c9D9D9Dw4,
                              ),
                              Text(
                                "$idNumber",
                                style: ts12c000940w4,
                              ),
                            ],
                          ),
                          Text(
                            department,
                            style: ts14c000940w4,
                          ),
                        ],
                      ),
                      StatusButton(
                        color: buttoncolor,
                        text: buttontext,
                      )
                    ],
                  ),
                  H(5),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            forSignature,
                            style: ts12c000940w4,
                          ),
                        ],
                      ),
                      Text(
                        statusdate,
                        style: ts12c9D9D9Dw4,
                      ),
                    ],
                  ),
                  H(10),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InkWell(
                    onTap: closeOntap,
                    child: Container(
                      padding: EdgeInsets.only(
                          left: 27.w, right: 25.w, top: 9.h, bottom: 9.h),
                      width: 74.w,
                      height: 40,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: ColorConstants.colorFFCFCD,
                      ),
                      child: SvgPicture.asset(
                        'close-circle'.asIconSvg(),
                      ),
                    ),
                  ),
                  H(2),
                  InkWell(
                    onTap: tickOntap,
                    child: Container(
                      padding: EdgeInsets.only(
                          left: 27.w, right: 25.w, top: 9.h, bottom: 9.h),
                      width: 74.w,
                      height: 40,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: ColorConstants.colorCDEAD4,
                      ),
                      child: SvgPicture.asset(
                        'tick-circle'.asIconSvg(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
