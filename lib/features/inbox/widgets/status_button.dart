import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StatusButton extends StatelessWidget {
  final Color color;
  final String text;
  final VoidCallback? onPressed;
  const StatusButton(
      {super.key, required this.color, required this.text, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: 80.w,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 4),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20), color: color),
        child: Text(text,
            style: ts12cFFFFFFw4, overflow: TextOverflow.ellipsis, maxLines: 1),
      ),
    );
  }
}
