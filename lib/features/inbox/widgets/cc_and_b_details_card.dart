import 'dart:developer';

import 'package:addc/features/inbox/widgets/status_button.dart';
import 'package:addc/features/utils/extensions.dart';

import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CcAndBDetailsCard extends StatelessWidget {
  final int? idNumber;
  final String? name;

  final Color? buttoncolor;
  final String? buttontext;

  final String? statusdate;
  final int? entryIdno;

  final int? index;
  final String? applicationFor;
  final int? caseIdNo;
  final int accIdno;
  final String? caseStatus;
  final int? idno;
  const CcAndBDetailsCard(
      {super.key,
      this.idNumber,
      this.buttoncolor,
      this.buttontext,
      this.statusdate,
      this.entryIdno,
      this.index,
      required this.applicationFor,
      required this.caseStatus,
      required this.name,
      required this.caseIdNo,
      required this.accIdno,
      required this.idno});

  @override
  Widget build(BuildContext context) {
    log('$index');
    return Padding(
      padding:
          EdgeInsets.only(left: 12.w, right: 12.w, top: 25.h, bottom: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              "General Contracting",
                              style: ts12c9D9D9Dw4,
                            ),
                          ],
                        ),
                        H(5),
                        Text(
                          applicationFor ?? '',
                          style: ts14c000940w4,
                        ),
                      ],
                    ),
                    StatusButton(
                      color: buttoncolor ?? Colors.transparent,
                      text: buttontext ?? '',
                    )
                  ],
                ),
              ),
            ],
          ),
          H(15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [],
              ),
              Text(
                statusdate ?? '',
                style: ts12c9D9D9Dw4,
              ),
            ],
          ),
          H(13),
          Container(
            height: 1,
            width: MediaQuery.of(context).size.width * 0.9,
            color: ColorConstants.color9D9D9D.withValues(alpha: 0.2),
          ),
          H(20),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "ENTRY ID",
                    style: ts12c9D9D9Dw4,
                  ),
                  Text(
                    '$entryIdno',
                    style: ts14c000940w4,
                  ),
                  H(20),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("CASE ID", style: ts12c9D9D9Dw4),
                      Text('$caseIdNo', style: ts14c000940w4)
                    ],
                  ),
                ],
              ),
              W(100),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    children: [
                      Text(
                        "ACC. ID",
                        style: ts12c9D9D9Dw4,
                      ),
                      Text('$accIdno', style: ts14c000940w4)
                    ],
                  ),
                  H(20),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("CASE STATUS", style: ts12c9D9D9Dw4),
                      Text(caseStatus ?? '', style: ts14c000940w4)
                    ],
                  ),
                ],
              )
            ],
          ),
          H(25),
          Row(
            children: [
              Text(
                "ASSIGNED TO",
                style: ts12c9D9D9Dw4,
              ),
              W(5),
              Row(
                children: [
                  Image.asset(
                    height: 17.h,
                    width: 17.w,
                    'user-image'.asImagePng(),
                  ),
                  W(5),
                  Text(
                    name ?? '',
                    style: ts12c000940w4,
                  ),
                  Text("ID"),
                ],
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(left: 90),
            child: Row(
              children: [
                Text(
                  "ID- ",
                  style: ts12c9D9D9Dw4,
                ),
                Text(
                  '$idno',
                  style: ts12c9D9D9Dw4,
                ),
              ],
            ),
          ),
          H(32),
          Row(
            children: [
              Text(
                "ASSIGNED TO",
                style: ts12c9D9D9Dw4,
              ),
              W(5),
              Row(
                children: [
                  Image.asset(
                    height: 17.h,
                    width: 17.w,
                    'user-image'.asImagePng(),
                  ),
                  W(5),
                  Text(
                    name ?? '',
                    style: ts12c000940w4,
                  ),
                  Text("ID"),
                ],
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(left: 90),
            child: Row(
              children: [
                Text(
                  "ID- ",
                  style: ts12c9D9D9Dw4,
                ),
                Text(
                  '$idno',
                  style: ts12c9D9D9Dw4,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
