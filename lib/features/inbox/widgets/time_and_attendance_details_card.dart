import 'package:addc/features/inbox/models/webta_model.dart';
import 'package:addc/features/inbox/widgets/status_button.dart';
import 'package:addc/features/profile/view/sections/profile_avatar_section.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TimeAndAttendanceDetailsCard extends StatelessWidget {
  final WebTAModel item;
  const TimeAndAttendanceDetailsCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String appEnd = item.appEnd ?? '';
    String appStart = item.appStart ?? '';
    String appTypeDesc = item.appTypeDesc ?? '';
    String name = (item.name ?? '').trim();
    // String personId = item.personId ?? '';
    // String statusDesc = item.statusDesc ?? '';
    // String vTypeDesc = item.vTypeDesc ?? '';
    String violationMins = item.violationMins ?? '';
    String appStatusDesc = item.appStatusDesc ?? '';
    String appDate = item.appDate ?? '';
    if (appStart.isNotEmpty) {
      //04/01/2024 07:00:00
      appStart = DateFormatter.formatStringDate(
          date: appStart,
          inputFormat: 'dd/MM/yyyy hh:mm:ss',
          outputFormat: 'hh:mm:ss');
    }
    if (appEnd.isNotEmpty) {
      //04/01/2024 07:00:00
      appEnd = DateFormatter.formatStringDate(
          date: appEnd,
          inputFormat: 'dd/MM/yyyy hh:mm:ss',
          outputFormat: 'hh:mm:ss');
    }
    if (appDate.isNotEmpty) {
      appDate = DateFormatter.formatStringDate(
          date: appDate,
          inputFormat: 'dd/MM/yyyy',
          outputFormat: 'dd MMM, yyyy');
    }

    return Padding(
      padding:
          EdgeInsets.only(left: 12.w, right: 12.w, top: 25.h, bottom: 20.h),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            '$appStatusDesc ${appStatusDesc.isNotEmpty && appTypeDesc.isNotEmpty ? ' | ' : ''} $appTypeDesc',
                            style: ts12c9D9D9Dw4),
                        H(5),
                        Row(
                          children: [
                            Text(
                                '$appStart ${appStart.isNotEmpty && appEnd.isNotEmpty ? ' - ' : ''}  $appEnd',
                                style: ts14c000940w4),
                            W(2.5),
                            Container(
                              height: 3.h,
                              width: 3.w,
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: ColorConstants.color000940),
                            ),
                            W(2.5),
                            Text('$violationMins mins', style: ts14c000940w4),
                          ],
                        ),
                      ],
                    ),
                    StatusButton(
                        color: ColorConstants.colorFFBC47, text: 'Pending')
                  ],
                ),
              ),
            ],
          ),
          // if (index != null && index == 4)
          // Container(
          //   decoration: BoxDecoration(
          //       borderRadius: BorderRadius.circular(4),
          //       color: ColorConstants.colorF9F9FF),
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     children: [
          //       Column(
          //         crossAxisAlignment: CrossAxisAlignment.start,
          //         children: [
          //           Text(
          //             'Official',
          //             style: ts10c9D9D9Dw4,
          //           ),
          //           Text('officialmin' ?? '30')
          //         ],
          //       ),
          //       Padding(
          //         padding: EdgeInsets.only(right: 100.w),
          //         child: Column(
          //           crossAxisAlignment: CrossAxisAlignment.start,
          //           children: [
          //             Text(
          //               'Personal',
          //               style: ts10c9D9D9Dw4,
          //             ),
          //             Text('personalmin' ?? '30')
          //           ],
          //         ),
          //       )
          //     ],
          //   ),
          // ),
          H(15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Row(
                  children: [
                    CustomAvatar(
                      fontSize: 12,
                      borderWidth: 0.1,
                      name: name,
                      radius: 17.h / 2,
                    ),
                    W(5),
                    Expanded(child: Text(name, style: ts12c000940w4)),
                  ],
                ),
              ),
              Text(appDate, style: ts12c9D9D9Dw4),
            ],
          )
        ],
      ),
    );
  }
}
