import 'dart:developer';

import 'package:addc/features/inbox/models/webta_model.dart';
import 'package:addc/features/inbox/providers/webta_provider.dart';
import 'package:addc/features/inbox/widgets/status_button.dart';
import 'package:addc/features/profile/view/sections/profile_avatar_section.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/common_success_bottom_sheet.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_confirmation_dialog.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

import '../views/time_and_attendance_detais_screen.dart';

class TimeAndAttendanceCard extends StatelessWidget {
  final WebTAModel item;
  final int index;
  const TimeAndAttendanceCard(
      {super.key, required this.item, required this.index});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<WebTAProvider>();
    String appEnd = item.appEnd ?? '';
    String appId = item.appId ?? '';
    String appStart = item.appStart ?? '';
    String appTypeDesc = item.appTypeDesc ?? '';
    String name = (item.name ?? '').trim();
    // String personId = item.personId ?? '';
    // String statusDesc = item.statusDesc ?? '';
    // String vTypeDesc = item.vTypeDesc ?? '';
    String violationMins = item.violationMins ?? '';
    String appStatusDesc = item.appStatusDesc ?? '';
    String appDate = item.appDate ?? '';
    if (appStart.isNotEmpty) {
      //04/01/2024 07:00:00
      appStart = DateFormatter.formatStringDate(
          date: appStart,
          inputFormat: 'dd/MM/yyyy hh:mm:ss',
          outputFormat: 'hh:mm:ss');
    }
    if (appEnd.isNotEmpty) {
      //04/01/2024 07:00:00
      appEnd = DateFormatter.formatStringDate(
          date: appEnd,
          inputFormat: 'dd/MM/yyyy hh:mm:ss',
          outputFormat: 'hh:mm:ss');
    }
    if (appDate.isNotEmpty) {
      log('appDate - $appDate');
      appDate = DateFormatter.formatStringDate(
          date: appDate,
          inputFormat: 'dd/MM/yyyy',
          outputFormat: 'dd MMM, yyyy');
    }
    // dd MMM, yyyy
    return InkWell(
        onTap: () => Navigator.pushNamed(
            context, TimeAndAttendancedetailsScreen.route,
            arguments: TimeAndAttendancedetailsScreen(item: item)),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width - 56.5.w,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            H(10),
                            Text(
                                '$appStatusDesc ${appStatusDesc.isNotEmpty && appTypeDesc.isNotEmpty ? ' | ' : ''} $appTypeDesc',
                                style: ts12c9D9D9Dw4),
                            Row(
                              children: [
                                Text(
                                    '$appStart ${appStart.isNotEmpty && appEnd.isNotEmpty ? ' - ' : ''}  $appEnd',
                                    style: ts14c000940w4),
                                W(2.5),
                                Container(
                                  height: 3.h,
                                  width: 3.w,
                                  decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: ColorConstants.color000940),
                                ),
                                W(2.5),
                                Text('$violationMins mins',
                                    style: ts14c000940w4),
                              ],
                            ),
                          ],
                        ),
                        StatusButton(
                            color: ColorConstants.colorFFBC47, text: 'Pending')
                      ],
                    ),
                    H(15),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              W(2),
                              CustomAvatar(
                                  name: name,
                                  fontSize: 10,
                                  radius: (20.w) / 2,
                                  borderWidth: 0.1),
                              W(5),
                              Flexible(child: Text(name, style: ts12c000940w4)),
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 8.0),
                          child: Text(appDate, style: ts12c9D9D9Dw4),
                        ),
                      ],
                    ),
                    H(10),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 10.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InkWell(
                      onTap: () async {
                        showModalBottomSheet(
                            backgroundColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(30.r))),
                            context: context,
                            builder: (builder) => CommonConfirmationDialog(
                                  title: 'Time & Attendace Rejection',
                                  content:
                                      'Are you sure do you want to Reject ?',
                                  onConfirm: () async {
                                    bool? isSuccess = await provider
                                        .postWebTAPermissionApproval(
                                            appID: appId,
                                            appStatus: 3); //reject
                                    Navigator.pop(context);
                                    if (isSuccess == true) {
                                      successBottomsheet(
                                          context: context,
                                          title:
                                              'Permission request\nrejected successfully',
                                          status: BottomSheetStatus.success,
                                          onPressed: () =>
                                              Navigator.pop(context));
                                    }
                                  },
                                ));
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                            left: 27.w, right: 25.w, top: 9.h, bottom: 9.h),
                        width: 74.w,
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: ColorConstants.colorFFCFCD,
                        ),
                        child: SvgPicture.asset(
                          'close-circle'.asIconSvg(),
                        ),
                      ),
                    ),
                    H(2),
                    InkWell(
                      onTap: () async {
                        // await provider.postWebTAPermissionApproval(
                        //     appID: appId,
                        //     appStatus: 2,
                        //     index: index); // approve
                        showModalBottomSheet(
                            backgroundColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(30.r))),
                            context: context,
                            builder: (builder) => CommonConfirmationDialog(
                                  title: 'Time & Attendace Approval',
                                  content:
                                      'Are you sure do you want to approve ?',
                                  onConfirm: () async {
                                    bool? isSuccess = await provider
                                        .postWebTAPermissionApproval(
                                            appID: appId,
                                            appStatus: 2); //Approve
                                    Navigator.pop(context);
                                    if (isSuccess == true) {
                                      successBottomsheet(
                                          context: context,
                                          title:
                                              'Permission request\napproved successfully',
                                          status: BottomSheetStatus.success,
                                          onPressed: () =>
                                              Navigator.pop(context));
                                    }
                                  },
                                ));
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                            left: 27.w, right: 25.w, top: 9.h, bottom: 9.h),
                        width: 74.w,
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: ColorConstants.colorCDEAD4,
                        ),
                        child: SvgPicture.asset(
                          'tick-circle'.asIconSvg(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }
}
