import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RemarksCard extends StatelessWidget {
  
  final String reasonexplanation;
  const RemarksCard({super.key,required this.reasonexplanation});

  @override
  Widget build(BuildContext context) {
    return Container(
      // width: MediaQuery.of(context).s,
      height: 129.h,
      padding: EdgeInsets.only(left:13.w ,right:15.w ,bottom:23 ,top: 16.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18),
        color: ColorConstants.colorFFFFFF
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("REMARKS",style: ts12c9D9D9Dw4,),
          Text(reasonexplanation,style: ts14c000940w4,)
        ],
      ),
    );
  }
}