import 'package:addc/features/inbox/models/buttons.dart';
import 'package:addc/features/inbox/widgets/cmt_card.dart';
import 'package:addc/features/inbox/widgets/custom_search_bar.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// ignore: must_be_immutable
class CmtContainer extends StatelessWidget {
  CmtContainer({super.key});

  final List<Buttons> button = [
    Buttons(buttonColor: ColorConstants.colorFFBC47, buttonText: "Pending"),
    Buttons(buttonColor: ColorConstants.colorFFBC47, buttonText: "Pending"),
    Buttons(buttonColor: ColorConstants.colorFFBC47, buttonText: "Pending"),
    But<PERSON>(buttonColor: ColorConstants.color0C902F, buttonText: "Approved"),
    Buttons(buttonColor: ColorConstants.color0C902F, buttonText: "Approved"),
    Buttons(buttonColor: ColorConstants.color0C902F, buttonText: "Approved"),
  ];
   TextEditingController searchController =TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorConstants.colorFFFFFF,
        borderRadius: BorderRadius.circular(18),
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 14.h, left: 12.w, right: 12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomSearchBar(controller: searchController,),
            H(20),
            Text(
              "CMT",
              style: ts16c000940w4,
            ),
           
            Expanded(
              child: ListView.separated(
                separatorBuilder: (context, index) {
                return Container(
                  height: 1,
                  width: MediaQuery.of(context).size.width * 0.9,
                  color: ColorConstants.color9D9D9D.withValues(alpha: 0.1),
                );
              },
                itemCount: button.length,
                itemBuilder: (context, index) {
                  final data = button[index];
                  return CmtCard(
                    buttoncolor: data.buttonColor ?? Colors.transparent,
                    idNumber: 345555,
                    department: 'Connection Department',
                    forSignature: "For Signature",
                    buttontext: button[index].buttonText.toString(),
                    statusdate: '01 Jan, 2025',
                    ontapforStatusButton: () {},
                  );
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
