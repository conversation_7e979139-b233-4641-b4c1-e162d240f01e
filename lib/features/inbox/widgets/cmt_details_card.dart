import 'dart:developer';

import 'package:addc/features/inbox/widgets/status_button.dart';
import 'package:addc/features/utils/extensions.dart';

import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CmtDetailsCard extends StatelessWidget {
  final int? idNumber;
  final String? name;
  final String? department;
  final Color? buttoncolor;
  final String? buttontext;
  final String? forSignature;
  final String? statusdate;
  final int? documentReferenceNo;
  final String? personalmin;
  final int? index;
  final int? idno;
  const CmtDetailsCard(
      {super.key,
      this.idNumber,
      this.department,
      this.buttoncolor,
      this.buttontext,
      this.forSignature,
      this.statusdate,
      this.documentReferenceNo,
      this.index,
      this.personalmin,
      required this.idno,
      required this.name});

  @override
  Widget build(BuildContext context) {
    log('$index');
    return Padding(
      padding:
          EdgeInsets.only(left: 12.w, right: 12.w, top: 25.h, bottom: 20.h),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              "ID - ",
                              style: ts12c9D9D9Dw4,
                            ),
                            Text(
                              "$idNumber",
                              style: ts12c000940w4,
                            ),
                          ],
                        ),
                        H(5),
                        Text(
                          department ?? '',
                          style: ts14c000940w4,
                        ),
                      ],
                    ),
                    StatusButton(
                      color: buttoncolor ?? Colors.transparent,
                      text: buttontext ?? '',
                    )
                  ],
                ),
              ),
            ],
          ),
          // if (index != null && index == 4)
          //   Container(
          //     decoration: BoxDecoration(
          //         borderRadius: BorderRadius.circular(4),
          //         color: ColorConstants.colorF9F9FF),
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //       children: [
          //         Column(
          //           crossAxisAlignment: CrossAxisAlignment.start,
          //           children: [
          //             Text(
          //               'Official',
          //               style: ts10c9D9D9Dw4,
          //             ),
          //             Text(officialmin ?? '30')
          //           ],
          //         ),
          //         Padding(
          //           padding: EdgeInsets.only(right: 100.w),
          //           child: Column(
          //             crossAxisAlignment: CrossAxisAlignment.start,
          //             children: [
          //               Text(
          //                 'Personal',
          //                 style: ts10c9D9D9Dw4,
          //               ),
          //               Text(personalmin ?? '30')
          //             ],
          //           ),
          //         )
          //       ],
          //     ),
          //   ),
          H(10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  W(5),
                  Text(
                    forSignature ?? '',
                    style: ts12c000940w4,
                  ),
                ],
              ),
              Text(
                statusdate ?? '',
                style: ts12c9D9D9Dw4,
              ),
            ],
          ),
          H(13),
          Container(
            height: 1,
            width: MediaQuery.of(context).size.width * 0.9,
            color: ColorConstants.color9D9D9D.withValues(alpha: 0.2),
          ),
          H(20),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "DOCUMENT REF NO",
                    style: ts12c9D9D9Dw4,
                  ),
                  Text('$documentReferenceNo')
                ],
              ),
              W(70),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "ASSIGNED TO",
                    style: ts12c9D9D9Dw4,
                  ),
                  Row(
                    children: [
                      Image.asset(
                        height: 17.h,
                        width: 17.w,
                        'user-image'.asImagePng(),
                      ),
                      W(5),
                      Text(
                        name ?? '',
                        style: ts12c000940w4,
                      ),
                      Text("ID"),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        "ID- ",
                        style: ts12c9D9D9Dw4,
                      ),
                      Text(
                        '$idno',
                        style: ts12c9D9D9Dw4,
                      ),
                    ],
                  )
                ],
              )
            ],
          ),
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("SUBJECT", style: ts12c9D9D9Dw4),
                  Text("Connection Department", style: ts14c000940w4)
                ],
              )
            ],
          )
        ],
      ),
    );
  }
}
