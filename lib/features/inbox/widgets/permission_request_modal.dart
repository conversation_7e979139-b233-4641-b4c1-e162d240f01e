import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class PermissionRequestModal extends StatelessWidget {
  final String? modalmessage;
  final VoidCallback? onPressed;
  const PermissionRequestModal(
      {super.key, required this.modalmessage, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: SizedBox(
        height: 266,
        width: MediaQuery.of(context).size.width * 0.97,
        child: <PERSON>ack(
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                padding: EdgeInsets.only(top: 60),
                height: 223.0,
                width: 369,
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(30))),
                child: Column(
                  children: [
                    Text(
                      modalmessage ?? '',
                      textAlign: TextAlign.center,
                      style: ts26c000940w4,
                    ),
                    H(21),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 107.w),
                      child: ElevatedButton(
                          onPressed: onPressed, child: Text("Okay")),
                    )
                  ],
                ),
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 170,
              child: Container(
                decoration: BoxDecoration(
                    shape: BoxShape.circle, color: ColorConstants.colorFFFFFF),
                height: 95,
                width: 95,
                child: SvgPicture.asset(
                  'tick-circle'.asIconSvg(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
