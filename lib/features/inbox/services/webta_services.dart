import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';

class WebTAServices {
  final dio = Dio();
  Future<Response> getEmpPendingPermission() async {
    // LoginedUser.employeeId
    final String xmlBody =
        '''<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:GetEmpPendingPermissions>
         <!--Optional:-->
         <tem:pPersonId>${LoginedUser.employeeId}</tem:pPersonId>
      </tem:GetEmpPendingPermissions>
   </soapenv:Body>
</soapenv:Envelope>''';

    Response response = await dio.post(
      ApiConstants.endPointWebTAUrl,
      data: xmlBody,
      options: Options(
        headers: {
          'Content-Type': 'text/xml; charset=utf-8',
          'SOAPAction': '${ApiConstants.soapActionUrl}GetEmpPendingPermissions',
        },
      ),
    );
    return response;
  }

  Future<Response> postWebTAPermissionApproval(
      {required String appID, required int appStatus}) async {
    final String xmlBody =
        '''<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:PostPermissionApproval>
         <!--Optional:-->
         <tem:pManagersId>${LoginedUser.employeeId}</tem:pManagersId>
         <tem:pAppID>$appID</tem:pAppID>
         <tem:pAppStatus>$appStatus</tem:pAppStatus>
         <!--Optional:-->
         <tem:pMGR_Remarks>?</tem:pMGR_Remarks>
      </tem:PostPermissionApproval>
   </soapenv:Body>
</soapenv:Envelope>''';
    Response response = await dio.post(
      ApiConstants.endPointWebTAUrl,
      data: xmlBody,
      options: Options(
        headers: {
          'Content-Type': 'text/xml; charset=utf-8',
          'SOAPAction': '${ApiConstants.soapActionUrl}PostPermissionApproval',
        },
      ),
    );
    return response;
  }
}
