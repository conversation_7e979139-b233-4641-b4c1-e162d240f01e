import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class MaximoServices {
  CancelToken? _cancelToken;
  final Dio _dio = Dio();
  Future<Response> getMaximoList() async {
    try {
      Map<String, dynamic> data = {
        "ASSIGNCODE": "DMEFGMOHAMMED",
        "ASSIGNSTATUS": "ACTIVE",
        "STARTPOINT": 0
      };
      if (_cancelToken != null && !_cancelToken!.isCancelled) {
        _cancelToken!.cancel("Cancelled previous request");
      }
      _cancelToken = CancelToken();
      final response = await _dio.post(
          data: data,
          cancelToken: _cancelToken,
          '${ApiConstants.taqaBaseUrl}/gateway/ADDCMOB_REST_NOTIFICATION_LIST/1.0/REST_NOTIFICATION_LIST');
      return response;
    } on DioException catch (e) {
      debugPrint('Dio Error: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Error: $e');
      rethrow;
    }
  }
}
