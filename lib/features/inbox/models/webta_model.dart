import 'package:xml/xml.dart';

class WebTAModel {
  String? appId;
  String? appDate;
  String? personId;
  String? name;
  String? appStart;
  String? appEnd;
  String? violationMins;
  String? vTypeDesc;
  String? appTypeDesc;
  String? statusDesc;
  String? appStatusDesc;
  String? requestDescription;
  String? status;
  WebTAModel({
    this.appId,
    this.appDate,
    this.personId,
    this.name,
    this.appStart,
    this.appEnd,
    this.violationMins,
    this.vTypeDesc,
    this.appTypeDesc,
    this.statusDesc,
    this.appStatusDesc,
    this.requestDescription,
    this.status,
  });

  factory WebTAModel.fromXml(XmlElement element) {
    return WebTAModel(
      appId: element.getElement('APP_ID')?.innerText ?? '',
      appDate: element.getElement('APP_DT')?.innerText ?? '',
      personId: element.getElement('PERSON_ID')?.innerText ?? '',
      name: element.getElement('NAME')?.innerText ?? '',
      appStart: element.getElement('APP_START')?.innerText ?? '',
      appEnd: element.getElement('APP_END')?.innerText ?? '',
      violationMins: element.getElement('VIOLATION_MINS')?.innerText ?? '',
      vTypeDesc: element.getElement('V_TYPE_DESC')?.innerText ?? '',
      appTypeDesc: element.getElement('APP_TYPE_DESC')?.innerText ?? '',
      statusDesc: element.getElement('STATUS_DESC')?.innerText ?? '',
      appStatusDesc: element.getElement('APP_STATUS_DESC')?.innerText ?? '',
      requestDescription: element.getElement('REQ_DESC')?.innerText ?? '',
      status: element.getElement('STATUS')?.innerText.trim() ?? '',
    );
  }
}
