class MaximoModel {
  int? wFASSIGNMENTID;
  String? oWNERDESCRIPTION;
  String? oWNERRECORID;
  String? dESCRIPTION;
  String? cANINTERFACE;
  String? oWNERTABLE;
  String? dUEDATE;

  MaximoModel(
      {this.wFASSIGNMENTID,
      this.oWNERDESCRIPTION,
      this.oWNERRECORID,
      this.dESCRIPTION,
      this.cANINTERFACE,
      this.oWNERTABLE,
      this.dUEDATE});

  MaximoModel.fromJson(Map<String, dynamic> json) {
    wFASSIGNMENTID = json['WFASSIGNMENTID'];
    oWNERDESCRIPTION = json['OWNERDESCRIPTION'];
    oWNERRECORID = json['OWNER_RECORID'];
    dESCRIPTION = json['DESCRIPTION'];
    cANINTERFACE = json['CAN_INTERFACE'];
    oWNERTABLE = json['OWNERTABLE'];
    dUEDATE = json['DUEDATE'];
  }
}
