import 'dart:convert';
import 'dart:developer';

import 'package:addc/features/inbox/models/maximo_model.dart';
import 'package:addc/features/inbox/services/maximo_services.dart';
import 'package:flutter/foundation.dart';

class MaximoProvider extends ChangeNotifier {
  bool isLoading = false;
  List<MaximoModel> _maximos = [];
  List<MaximoModel> _filteredMaximos = [];
  String _searchQuery = '';

  List<MaximoModel> get maximos =>
      _searchQuery.isEmpty ? _maximos : _filteredMaximos;

  Future<void> getMaximoList() async {
    try {
      if (isLoading) return; // Prevent multiple requests
      isLoading = true;
      final response = await MaximoServices().getMaximoList();
      isLoading = false;
      if (kDebugMode) {
        log('getMaximoList - ${response.data}---${response.statusCode}');
      }

      if (response.statusCode == 200) {
        _maximos.clear();
        final List<dynamic> data = jsonDecode(response.data);
        _maximos = data.map((e) => MaximoModel.fromJson(e)).toList();
        // Reset filter on new data
        filterByDescription(_searchQuery);
      } else {
        _maximos.clear();
        _filteredMaximos.clear();
      }
    } catch (e) {
      debugPrint('Error in getMaximoList: $e');
      _maximos.clear();
      _filteredMaximos.clear();
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  void filterByDescription(String query) {
    _searchQuery = query;
    if (query.isEmpty) {
      _filteredMaximos = [];
    } else {
      _filteredMaximos = _maximos
          .where((item) => (item.dESCRIPTION ?? '')
              .toLowerCase()
              .contains(query.toLowerCase()))
          .toList();
    }
    notifyListeners();
  }
}
