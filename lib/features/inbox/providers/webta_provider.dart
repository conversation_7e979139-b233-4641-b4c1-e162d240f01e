import 'dart:developer';

import 'package:addc/features/inbox/models/webta_model.dart';
import 'package:addc/features/inbox/services/webta_services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:xml/xml.dart';

class WebTAProvider extends ChangeNotifier {
  bool isLoading = false;
  String? errorMessage;
  bool hasError = false;
  void onSearchChanged({required String query}) {
    if (query.isEmpty) {
      filteredPermissions = pendingPermissions;
    } else {
      filteredPermissions = pendingPermissions
          .where((item) => (item.name ?? '').toLowerCase().contains(query))
          .toList();
    }
    notifyListeners();
  }

  void clearError() {
    hasError = false;
    errorMessage = null;
    notifyListeners();
  }

  final WebTAServices _webTAServices = WebTAServices();
  List<WebTAModel> pendingPermissions = [];
  List<WebTAModel> filteredPermissions = [];
  bool isListLoading = false;
  Future<List<WebTAModel>> getEmpPendingPermission() async {
    try {
      if (isListLoading) return pendingPermissions;
      pendingPermissions.clear();
      hasError = false;
      errorMessage = null;
      isListLoading = true;
      final response = await _webTAServices.getEmpPendingPermission();
      isListLoading = false;
      if (kDebugMode) {
        log('getEmpPendingPermission -${response.realUri} ${response.data} - ${response.realUri}');
      }
      if (response.statusCode == 200) {
        final document = XmlDocument.parse(response.data);

        // Check for error status first
        final pendingPermissionsElements =
            document.findAllElements('PendingPermissions');

        if (pendingPermissionsElements.isNotEmpty) {
          final firstElement = pendingPermissionsElements.first;
          final statusElement = firstElement.getElement('STATUS');
          final statusDescElement = firstElement.getElement('STATUS_DESC');

          // Check if this is an error response
          if (statusElement != null && statusDescElement != null) {
            final status = statusElement.innerText.trim();
            final statusDesc = statusDescElement.innerText.trim();

            // Handle specific error statuses
            if (status == '4' && statusDesc == 'No Manager Privilege') {
              log('Error: No Manager Privilege - User does not have permission to view pending permissions');
              hasError = true;
              errorMessage =
                  'You do not have manager privileges to view pending permissions';
              pendingPermissions = [];
              filteredPermissions = [];
              return [];
            }

            // Handle other error statuses if needed
            if (status == '0' || status == '2') {
              log('Error Status: $status - $statusDesc');
              hasError = true;
              errorMessage = statusDesc.isNotEmpty
                  ? statusDesc
                  : 'An error occurred while fetching permissions';
              pendingPermissions = [];
              filteredPermissions = [];
              return [];
            }
          }
        }

        // Process normal data (when no error status is present)
        final data = document
            .findAllElements('PendingPermissions')
            .map((e) => WebTAModel.fromXml(e))
            .where((model) => model.status != '3') // Filter out status == 3
            .where((model) =>
                model.appId != null &&
                model.appId!.isNotEmpty) // Filter out error responses
            .toList();

        pendingPermissions = data.reversed.toList();
        filteredPermissions = pendingPermissions;

        log('data.reversed - ${data.reversed.length}');
        log('filteredPermissions - ${filteredPermissions.length}---------${pendingPermissions.length}');

        return data;
      }
    } catch (e) {
      isListLoading = false;
      debugPrint('Error in getEmpPendingPermission: ${e.toString()}');
    } finally {
      isListLoading = false;
      notifyListeners();
    }
    return [];
  }

  Future<bool?> postWebTAPermissionApproval(
      {required String appID, required int appStatus, int? index}) async {
    try {
      if (isLoading) return null;
      isLoading = true;
      EasyLoading.show();
      final response = await _webTAServices.postWebTAPermissionApproval(
          appID: appID, appStatus: appStatus);
      EasyLoading.dismiss();
      isLoading = false;
      log('postWebTAPermissionApproval - ${response.data} - ${response.statusCode}');
      if (response.statusCode == 200) {
        if (index != null) {
          filteredPermissions.removeAt(index);
          notifyListeners();
        } else {
          await getEmpPendingPermission();
        }
        return true;
      }
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      EasyLoading.dismiss();
      isLoading = false;
    }
    return false;
  }
}
