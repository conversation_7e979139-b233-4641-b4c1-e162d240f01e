import 'package:flutter/material.dart';

class InboxProvider with ChangeNotifier {
  final List<String> categories = ['WebTA', 'Fusion']; // Removed maximo
  // final List<String> categories = ['Maximo', 'WebTA', 'Fusion'];
  // : [
  //     "Fusion", "WebTA", "Maximo",
  //     // "CMT",
  //     "CC&B"
  //   ];
  int selectedCategoryIndex = 0;
  void onCategoryTap(int index) {
    selectedCategoryIndex = index;
    updateSelectedCategory(index);
    notifyListeners();
  }

  void updateSelectedCategory(int index) {}
}
