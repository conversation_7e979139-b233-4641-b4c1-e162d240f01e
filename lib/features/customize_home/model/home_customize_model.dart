class HomeCustomizeModel {
  int? index;
  String? icon;
  String? title;
  String? count;
  bool? isSelected;

  HomeCustomizeModel(
      {this.index, this.icon, this.title, this.count, this.isSelected});

  HomeCustomizeModel.fromJson(Map<String, dynamic> json) {
    index = json['index'];
    icon = json['icon'];
    title = json['title'];
    count = json['count'];
    isSelected = json['isSelected'];
  }

  HomeCustomizeModel copyWith({
    int? index,
    String? icon,
    String? title,
    String? count,
    bool? isSelected,
  }) {
    return HomeCustomizeModel(
      index: index ?? this.index,
      icon: icon ?? this.icon,
      title: title ?? this.title,
      count: count ?? this.count,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
