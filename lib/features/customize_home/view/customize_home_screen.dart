import 'dart:developer';

import 'package:addc/features/customize_home/view/widgets/home_customize_card.dart';
import 'package:addc/features/profile/providers/profile_provider.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/common_success_bottom_sheet.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../model/home_customize_model.dart';

class CustomizeHomeScreen extends StatefulWidget {
  static const route = '/customize_home';
  const CustomizeHomeScreen({super.key});

  @override
  State<CustomizeHomeScreen> createState() => _CustomizeHomeScreenState();
}

class _CustomizeHomeScreenState extends State<CustomizeHomeScreen> {
  late ProfileProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<ProfileProvider>();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await _provider.getHomeCustomisations(context: context);
      _provider.homeCustomizationsTemp = _provider.homeCustomizations
          .map((e) => HomeCustomizeModel(
              count: e.count,
              icon: e.icon,
              index: e.index,
              isSelected: e.isSelected,
              title: e.title))
          .toList();

      setState(() {});
      log(' test ${_provider.homeCustomizationsTemp.length}');
    });
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.read<ProfileProvider>();
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customize Home Screen'),
        leading: const CommonBackButton(),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 25.h),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 9.w,
                mainAxisSpacing: 10.h,
                childAspectRatio: 1,
              ),
              itemCount: provider.homeCustomizationsTemp.length,
              itemBuilder: (context, index) {
                HomeCustomizeModel item =
                    provider.homeCustomizationsTemp[index];
                return HomeCustomizeCard(item: item, index: index);
              },
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 20.h),
        child: ElevatedButton(
            onPressed: () async {
              await provider.saveHomeCustomizations();
              if (!context.mounted) return;
              // await provider.getHomeCustomisations(context: context);
              // showToast('Customized successfully');
              // Show success dialog
              successBottomsheet(
                  context: context,
                  title: 'Customized successfully',
                  description: 'Your home\nsuccessfully Customized.',
                  status: BottomSheetStatus.success,
                  onPressed: () => Navigator.pop(context) // Close the dialog
                  );
            },
            child: Text('Save Changes')),
      ),
    );
  }
}
