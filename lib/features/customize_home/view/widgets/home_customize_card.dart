import 'package:addc/features/profile/providers/profile_provider.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import '../../model/home_customize_model.dart';

class HomeCustomizeCard extends StatelessWidget {
  final HomeCustomizeModel item;
  final int index;
  const HomeCustomizeCard({super.key, required this.item, required this.index});

  @override
  Widget build(BuildContext context) {
    String icon = item.icon ?? '';
    String title = item.title ?? '';
    return InkResponse(
      onTap: () => _onTap(context: context, item: item),
      child: Container(
        height: 180.h,
        width: 180.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18.r),
          border: Border.all(color: ColorConstants.colorEDEFFF, width: 1),
          color: ColorConstants.colorFFFFFF,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(18.r),
          child: Stack(
            children: [
              Align(
                  alignment: Alignment.bottomRight,
                  child: SvgPicture.asset('summary_graphics'.asIconSvg())),
              Padding(
                padding: EdgeInsets.only(
                    top: 15.h, bottom: 6.h, left: 12.w, right: 12.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 42.h,
                          width: 42.h,
                          padding: const EdgeInsets.all(11),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorConstants.colorC1C1C1
                                .withValues(alpha: 0.1),
                          ),
                          child: SvgPicture.asset(icon.asIconSvg()),
                        ),
                        Container(
                          height: 20.h,
                          width: 20.h,
                          margin: EdgeInsets.only(top: 5.h),
                          // padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorConstants.colorFFFFFF,
                            border: Border.all(
                              color: ColorConstants.colorD7D7D7,
                              width: 1,
                            ),
                          ),
                          child: Consumer<ProfileProvider>(
                            builder: (context, provider, _) {
                              bool isSelected = item.isSelected ?? false;
                              if (!isSelected) {
                                return const SizedBox.shrink();
                              }
                              return SizedBox(
                                height: 18.h,
                                width: 18.h,
                                child: Icon(Icons.check_circle, size: 18),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Text(title, style: ts18c1E1E1Ew4h1),
                    H(15)
                    // H(6),
                    // Text.rich(
                    //   TextSpan(
                    //     children: [
                    //       TextSpan(text: '$count ', style: ts46c1E1E1Ew4h1),
                    //       TextSpan(text: countType, style: ts16c1E1E1Ew4),
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _onTap({required HomeCustomizeModel item, required BuildContext context}) {
    final provider = context.read<ProfileProvider>();
    provider.homeCustomizationOnChanged(item: item);
  }
}
