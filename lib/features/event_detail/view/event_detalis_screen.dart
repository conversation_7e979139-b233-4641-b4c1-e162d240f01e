import 'package:addc/features/event_detail/view/sections/event_detail_section.dart';
import 'package:addc/features/event_detail/view/sections/event_gallary_section.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../widgets/common_bottom_appbar.dart';

class EventDetalisScreen extends StatelessWidget {
  static const route = '/event_detalis_screen';
  const EventDetalisScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Event Details'),
        leading: const CommonBackButton(),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            H(25),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 12.w),
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(18.r),
                  color: ColorConstants.colorFFFFFF),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const EventDetailSection(),
                  const EventGallarySection(),
                ],
              ),
            ),
            H(51)
          ],
        ),
      ),
      bottomNavigationBar: const CommonBottomAppbar(),
    );
  }
}
