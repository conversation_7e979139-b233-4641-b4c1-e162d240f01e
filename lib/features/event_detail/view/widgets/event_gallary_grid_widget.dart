import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../shared/constants/color_constants.dart';

class EventGallaryGridWidget extends StatelessWidget {
  final int index;
  const EventGallaryGridWidget({super.key, required this.index});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              fit: BoxFit.cover,
              image: AssetImage(
                'past_event_1'.asDummyPng(),
              ),
            ),
          ),
        ),
        if (index == 10 || index == 9)
          Container(
            height: double.infinity,
            width: double.infinity,
            decoration: BoxDecoration(
                color: ColorConstants.color000000.withValues(alpha: 0.6)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: 30.h,
                  width: 30.h,
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: ColorConstants.colorFFFFFF.withValues(alpha: 0.2)),
                  child: Icon(
                    Icons.play_arrow,
                    color: ColorConstants.colorFFFFFF,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
