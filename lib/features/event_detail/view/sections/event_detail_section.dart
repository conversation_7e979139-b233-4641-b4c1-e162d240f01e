import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../shared/constants/text_styles.dart';
import '../../../../widgets/common_divider.dart';
import '../../../widgets/gap.dart';

class EventDetailSection extends StatelessWidget {
  const EventDetailSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
            borderRadius: BorderRadius.circular(13.r),
            child: Image.asset('event_detail'.asDummyPng())),
        H(25),
        Row(
          children: [
            Text('Jan 20, 2025', style: ts14c9D9D9Dw4),
            W(12),
            Text('|', style: ts14c9D9D9Dw4),
            W(12),
            Text('09:00 AM - 10:00 PM', style: ts14c9D9D9Dw4),
          ],
        ),
        Text('Annual Day Celebration\n2025', style: ts24c000940w4h1),
        H(18),
        Text(
            'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa...',
            style: ts14c9D9D9Dw4h1),
        H(22),
        Row(
          children: [
            SvgPicture.asset(
              'location_small'.asIconSvg(),
              height: 22.h,
              width: 22.w,
            ),
            W(4),
            Text('Community Hall - Entrance A1', style: ts14c9D9D9Dw4h1),
          ],
        ),
        H(22),
        const CommonDivider(),
      ],
    );
  }
}
