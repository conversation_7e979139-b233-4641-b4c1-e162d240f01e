import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';
import '../widgets/event_gallary_grid_widget.dart';

class EventGallarySection extends StatelessWidget {
  const EventGallarySection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Event Gallery', style: ts16c1E1E1Ew6),
        H(2),
        Text('12 Photos & 2 Videos', style: ts13c9D9D9Dw4),
        H(15),
        ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 2,
              mainAxisSpacing: 2,
              childAspectRatio: 1,
            ),
            itemCount: 11,
            itemBuilder: (context, index) {
              return EventGallaryGridWidget(index: index);
            },
          ),
        )
      ],
    );
  }
}
