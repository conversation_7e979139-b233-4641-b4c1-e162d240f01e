import 'package:addc/features/apps_hub/widgets/apps_hub_cards.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../master_screen/view/widgets/master_appbar.dart';
import '../models/app_hub_model.dart';

class AppsHubScreen extends StatelessWidget {
  const AppsHubScreen({super.key});

  @override
  Widget build(BuildContext context) {
    List<AppsHubModel> items = [
      AppsHubModel(
          image: 'outlook',
          title: 'Outlook',
          description:
              'Securely manage and organise your company email, calendar, and files.',
          appStoreLink:
              'https://apps.apple.com/us/app/microsoft-outlook/id951937596',
          playStoreLink:
              'https://play.google.com/store/apps/details?id=com.microsoft.office.outlook',
          iosUrlScheme: 'ms-outlook://'),
      AppsHubModel(
          image: 'teams',
          title: 'Teams',
          description:
              'Chat, meet and collaborate with all your colleagues in the organisation.',
          appStoreLink:
              'https://apps.apple.com/us/app/microsoft-teams/id1113153706',
          playStoreLink:
              'https://play.google.com/store/apps/details?id=com.microsoft.teams',
          iosUrlScheme: 'msteams://'),
      AppsHubModel(
          image: 'oracle',
          title: 'Oracle Fusion',
          description:
              'Access all the Human Capital services. Soon it will include Financials and Supply Chain services.',
          appStoreLink:
              'https://apps.apple.com/us/app/oracle-fusion-applications/id1361813677',
          playStoreLink:
              'https://play.google.com/store/apps/details?id=com.oracle.cloud.hcm.mobile',
          iosUrlScheme: 'oraclefusion://'),
      AppsHubModel(
          image: 'sharepoint',
          title: 'SharePoint',
          description:
              'Browse the company intranet, find, and manage content on internal sites, and search for files and people.',
          appStoreLink:
              'https://apps.apple.com/us/app/microsoft-sharepoint/id1091505266',
          playStoreLink:
              'https://play.google.com/store/apps/details?id=com.microsoft.sharepoint',
          iosUrlScheme: 'ms-sharepoint://'),
      AppsHubModel(
          image: 'service_now',
          title: 'ServiceNow',
          description:
              'Report tech incidents and raise requests for the IT and Legal teams.',
          appStoreLink: 'https://apps.apple.com/us/app/now-mobile/id1469616608',
          playStoreLink:
              'https://play.google.com/store/apps/details?id=com.servicenow.requestor',
          iosUrlScheme: 'sn://'),
      AppsHubModel(
          image: 'powerbi',
          title: 'Power BI',
          description:
              'Monitor and access business data through powerful visualisations, charts, graphs, and dashboards.',
          appStoreLink:
              'https://apps.apple.com/us/app/microsoft-power-bi/id929738808',
          playStoreLink:
              'https://play.google.com/store/apps/details?id=com.microsoft.powerbim',
          iosUrlScheme: 'mspbi://'),
      AppsHubModel(
          image: 'onedrive',
          title: 'OneDrive',
          description: 'Securely access and share company files from anywhere.',
          appStoreLink:
              'https://apps.apple.com/us/app/microsoft-onedrive/id477537958',
          playStoreLink:
              'https://play.google.com/store/apps/details?id=com.microsoft.skydrive',
          iosUrlScheme: 'ms-onedrive://'),
      AppsHubModel(
          image: 'onenote',
          title: 'OneNote',
          description:
              "Organise your thoughts, discoveries, and ideas with the company's digital notepad.",
          appStoreLink:
              'https://apps.apple.com/us/app/microsoft-onenote/id410395246',
          playStoreLink:
              'https://play.google.com/store/apps/details?id=com.microsoft.office.onenote',
          iosUrlScheme: 'onenote://'),
      AppsHubModel(
          image: 'planner',
          title: 'Planner',
          description:
              'Manage your teamwork in a simple and visual way: create plans, assign tasks, and track status.',
          appStoreLink:
              'https://apps.apple.com/us/app/microsoft-planner/id1219301037',
          playStoreLink:
              'https://play.google.com/store/apps/details?id=com.microsoft.planner',
          iosUrlScheme: 'ms-planner://'),
      AppsHubModel(
          image: 'powerautomate',
          title: 'Power Automate',
          description:
              'Create automated workflows to get notifications, synchronise files, and more.',
          appStoreLink:
              'https://apps.apple.com/us/app/power-automate/id1094928825',
          playStoreLink:
              'https://play.google.com/store/apps/details?id=com.microsoft.flow',
          iosUrlScheme: 'ms-powerautomate://'),
    ];
    return Scaffold(
      appBar: MasterAppBar(
          textEditingController: TextEditingController(), context: context),
      body: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 20.h),
        itemCount: items.length,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          final item = items[index];
          return AppsHubCards(item: item);
        },
        separatorBuilder: (context, index) => H(10),
      ),
    );
  }
}
