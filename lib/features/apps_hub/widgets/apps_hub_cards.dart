import 'dart:io';

import 'package:addc/features/apps_hub/models/app_hub_model.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppsHubCards extends StatelessWidget {
  final AppsHubModel item;

  const AppsHubCards({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String image = item.image;
    String title = item.title;
    String description = item.description;
    String link = item.appStoreLink;
    String urlScheme = item.iosUrlScheme;
    if (Platform.isAndroid) {
      link = item.playStoreLink;
      link = extractPackageName(link);
      // List<String> tempID = item.playStoreLink.split('com.');
      // link = tempID[1];
    }

    return InkWell(
      // onTap: () => log('message -- $link'),
      // ms-outlook://
      onTap: () => launchApp(
          androidPackageName: link,
          appStoreLink: link,
          iosUrlScheme: urlScheme),
      child: Container(
        padding: EdgeInsets.only(top: 5.h, left: 7.w, bottom: 5.h),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(18),
            color: ColorConstants.colorFFFFFF),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              scale: 2,
              height: 68.h,
              width: 65.w,
              image.asImagePng(),
            ),
            W(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: ts14c1E1E1Ew4,
                  ),
                  H(3),
                  Text(
                    description,
                    style: ts12c9D9D9Dw4h1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
