extension StringExtension on String {
  String asImagePng() => 'assets/images/$this.png';
  String asIconPng() => 'assets/icons/$this.png';
  String asIconSvg() => 'assets/svg/$this.svg';
  String asDummyPng() => 'assets/dummy/$this.png';
  String asNotificationPng() => 'assets/notification/$this.png';
  String capitalize() =>
      "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
}

// class UniqueFunctions {
//   static Gradient? hiGradient = LinearGradient(
//     begin: const Alignment(0.24, -0.97),
//     end: const Alignment(-0.24, 0.97),
//     colors: [ThemeColors.primaryColor, ThemeColors.primaryColor2],
//   );
// }
