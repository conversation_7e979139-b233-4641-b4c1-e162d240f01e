import 'dart:developer';
import 'dart:io';
import 'package:addc/shared/helper/image_picker_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../widgets/common_success_bottom_sheet.dart';
import '../widgets/photo_upload_dialog.dart';
import 'package:url_launcher/url_launcher.dart';

showSnackBarMessage({required BuildContext context, required String msg}) {
  SnackBar snackBar = SnackBar(
    content: Text(msg),
    behavior: SnackBarBehavior.floating,
  );
  ScaffoldMessenger.of(context).showSnackBar(snackBar);
}

hideKeyboard(BuildContext context) {
  FocusScope.of(context).unfocus();
}

onTryToPickImage({required BuildContext context}) {
  final provider = context.read<ImagePickerProvider>();
  if (Platform.isAndroid) {
    showDialog(
      context: context,
      builder: (ctx) => PhotoUploadDialog(
        onCamera: () async {
          await provider.imagePicker(source: ImageSource.camera);
        },
        onGallery: () async {
          await provider.imagePicker(source: ImageSource.gallery);
        },
      ),
    );
  } else {
    Navigator.of(context).push(modalBuilder(context));
  }
}

CupertinoModalPopupRoute<void> modalBuilder(BuildContext context) {
  final provider = context.read<ImagePickerProvider>();

  return CupertinoModalPopupRoute<void>(
    builder: (BuildContext ctx) {
      return CupertinoActionSheet(
        actions: <CupertinoActionSheetAction>[
          CupertinoActionSheetAction(
            child: Text('Camera'),
            onPressed: () async {
              Navigator.pop(context);
              await provider.imagePicker(source: ImageSource.camera);
            },
          ),
          CupertinoActionSheetAction(
            onPressed: () async {
              await provider.imagePicker(source: ImageSource.gallery);
            },
            child: Text('Gallery'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () async {
            Navigator.pop(context);
          },
          isDestructiveAction: true,
          child: Text('Cancel'),
        ),
      );
    },
  );
}

successBottomsheet({
  required BuildContext context,
  required String title,
  String? buttonTitle,
  VoidCallback? onPressed,
  BottomSheetStatus status = BottomSheetStatus.success,
  String? description,
}) {
  showModalBottomSheet(
    isDismissible: false,
    backgroundColor: Colors.transparent,
    shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(30.r))),
    context: context,
    builder: (builder) {
      return CommonSuccessBottomSheet(
        title: title,
        onPressed: onPressed,
        buttonTitle: buttonTitle,
        status: status,
        desciption: description,
      );
    },
  );
}

Future<void> launchURL({required String url}) async {
  log('url -- $url');
  final Uri launchURL = Uri.parse(url);
  if (!await launchUrl(launchURL)) {
    throw Exception('Could not launch $launchURL');
  }
}

Future<void> launchApp({
  required String androidPackageName,
  required String iosUrlScheme,
  required String appStoreLink,
}) async {
  if (Platform.isAndroid) {
    // First check if we can launch the url - depends on if app is installed or not and if you have the permissions
    // if (isHuawei) { // check using DeviceInfoPlugin package: androidInfo.manufacturer == 'HUAWEI'
    //   final launchedApp = await LaunchApp.openApp(
    //     // We do not use this plugin for all platforms since it freezes the app on ios
    //     androidPackageName: androidPackageName,
    //     iosUrlScheme: iosUrlScheme,
    //     appStoreLink: appStoreLink,
    //     openStore: false,
    //   );

    //   if (launchedApp.toBool() == false) {
    //     Uri huaweiMarketUrl =
    //         Uri.parse("market://details?id=$androidPackageName");

    //     await launchUrl(huaweiMarketUrl);
    //   }
    //   return;
    // }

    // Other Android Devices than Huawei
    final androidOpenAppUrl =
        Uri.parse("market://launch?id=$androidPackageName");
    final appInstalled = await canLaunchUrl(androidOpenAppUrl);

    if (appInstalled) {
      //if we can launch the url then open the app

      await launchUrl(androidOpenAppUrl);
    } else {
      //if we cannot, then open a link to the playstore so the user downloads the app
      final googlePlayUrl = Uri.parse(
          "https://play.google.com/store/apps/details?id=$androidPackageName");

      await launchUrl(googlePlayUrl);
    }
  } else {
    // iOS logic
    final urlScheme = Uri.parse(iosUrlScheme);
    log('Trying to launch: $urlScheme');

    if (await canLaunchUrl(urlScheme)) {
      log('App is installed, launching...');
      await launchUrl(urlScheme, mode: LaunchMode.externalApplication);
    } else {
      log('App not installed, opening App Store...');
      final appStoreUrl = Uri.parse(appStoreLink);
      await launchUrl(appStoreUrl, mode: LaunchMode.externalApplication);
    }
  }
}

String extractPackageName(String url) {
  Uri uri = Uri.parse(url);
  return uri.queryParameters['id'] ?? 'Package name not found';
}

Future<bool?> showToast(String text) {
  return Fluttertoast.showToast(
      msg: text,
      backgroundColor: Colors.black,
      textColor: Colors.white,
      fontSize: 14,
      timeInSecForIosWeb: 5);
}

Future<void> openMap(
    {required double latitude, required double longitude}) async {
  String googleUrl =
      'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
  if (await canLaunchUrl(Uri.parse(googleUrl))) {
    await launchUrl(Uri.parse(googleUrl));
  } else {
    throw 'Could not open the map.';
  }
}

String convertTo12Hour(String time24) {
  final inputFormat = DateFormat.Hm(); // 24-hour format
  final outputFormat = DateFormat.jm(); // 12-hour format with AM/PM
  final dateTime = inputFormat.parse(time24);
  return outputFormat.format(dateTime);
}
