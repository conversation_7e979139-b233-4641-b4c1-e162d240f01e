import 'dart:convert';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:addc/services/firebase_remote_config_service.dart';

class ApiConstants {
  // static const String myPersonID = '100000011925691';
  // static const String personID = '45775';
  static String credentials =
      '${dotenv.env['BASIC_AUTH_USERNAME']}:${dotenv.env['BASIC_AUTH_PASSWORD']}';
  static String encodedCredentials = base64Encode(utf8.encode(credentials));
  static Map<String, String> authHeader() {
    return {
      'Authorization': 'Basic $encodedCredentials',
      'Content-Type': 'application/json'
    };
  }

  // Get URLs from Firebase Remote Config
  static String get endPointWebTAUrl =>
      FirebaseRemoteConfigService.instance.webtaBaseUrl;

  static String soapActionUrl = 'http://tempuri.org/'; // WebTA

  static String get oracleBaseURL =>
      FirebaseRemoteConfigService.instance.oracleBaseUrl;

  static String get taqaBaseUrl =>
      FirebaseRemoteConfigService.instance.taqaMaximoBaseUrl;

  static String get employeeDetails => "${oracleBaseURL}emps";
  static String get workersDetails => "${oracleBaseURL}workers/";
  static String get employeeAbsence => "${oracleBaseURL}absences/";
  static String get planBalances => "${oracleBaseURL}planBalances/";
  static String get batchURL => "${oracleBaseURL}batch";
}


// static String endPointWebTAUrl =
//       'https://api-uat.taqa.ae/ws/ADDCMobileWebServiceServices/1.0'; // WebTA URL
//   static String soapActionUrl =
//       'http://tempuri.org/'; // WebTA URL soapAction URL
//   static const oracleBaseURL =
//       'https://fa-ewzp-dev3-saasfaprod1.fa.ocs.oraclecloud.com:443/hcmRestApi/resources/11.13.18.05/'; // Oracle base URL