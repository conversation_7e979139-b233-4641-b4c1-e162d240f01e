import 'package:addc/features/notifications/models/notification_model.dart';
import 'package:addc/features/notifications/widgets/notification_tile.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_divider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Notificationcontainer extends StatelessWidget {
  const Notificationcontainer({super.key});

  @override
  Widget build(BuildContext context) {
    final List<NotificationModel> notificationList1 = [
      NotificationModel(
        icon: 'complaint',
        title: 'Post',
        description:
            'Your onsite observation complaint with ID 12654 has forwarded successfully',
        isSelected: true,
      ),
      NotificationModel(
          icon: 'leave_request',
          title: 'Leave Request',
          description:
              'Your onsite observation complaint with ID 12654 has forwarded successfully',
          id: 454545),
    ];
    final List<NotificationModel> notificationList2 = [
      NotificationModel(
          icon: 'post',
          title: 'Post',
          description: 'Aye<PERSON> Shaih Likes your post',
          id: 44532),
      NotificationModel(
          icon: 'account_update',
          title: 'Account Update',
          description:
              'Your onsite observation complaint with ID 12654 has forwarded successfully',
          id: 345234),
      NotificationModel(
        icon: 'complaint',
        title: 'Complaint',
        description:
            'Your onsite observation complaint with ID 12654 has forwarded successfully',
        id: 345234,
        isSelected: true,
      ),
      NotificationModel(
          icon: 'complaint',
          title: 'Complaint',
          description:
              'Your onsite observation complaint with ID 12654 has forwarded successfully',
          id: 345234),
      NotificationModel(
          icon: 'complaint',
          title: 'Complaint',
          description:
              'Your onsite observation complaint with ID 12654 has forwarded successfully',
          id: 345234),
      NotificationModel(
          icon: 'complaint',
          title: 'Complaint',
          description:
              'Your onsite observation complaint with ID 12654 has forwarded successfully',
          id: 345234),
      NotificationModel(
          icon: 'complaint',
          title: 'Complaint',
          description:
              'Your onsite observation complaint with ID 12654 has forwarded successfully',
          id: 345234),
      NotificationModel(
          icon: 'complaint',
          title: 'Complaint',
          description:
              'Your onsite observation complaint with ID 12654 has forwarded successfully',
          id: 345234),
      NotificationModel(
          icon: 'complaint',
          title: 'Complaint',
          description:
              'Your onsite observation complaint with ID 12654 has forwarded successfully',
          id: 345234),
      NotificationModel(
          icon: 'complaint',
          title: 'Complaint',
          description:
              'Your onsite observation complaint with ID 12654 has forwarded successfully',
          id: 345234),
    ];
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(18),
                color: ColorConstants.colorFFFFFF),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 12, top: 20),
                    child: Text("TODAY", style: ts16c989898w4),
                  ),
                  H(12),
                  ListView.separated(
                    shrinkWrap: true,
                    itemCount: notificationList1.length,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      final item = notificationList1[index];
                      return NotificationTile(index: index, item: item);
                    },
                    separatorBuilder: (context, index) {
                      return CommonDivider(
                        color:
                            ColorConstants.color9D9D9D.withValues(alpha: 0.1),
                        height: 1,
                        thickness: 1,
                      );
                    },
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 12.w),
                    child: Text("YESTERDAY", style: ts16c989898w4),
                  ),
                  H(12),
                  ListView.separated(
                    shrinkWrap: true,
                    itemCount: notificationList2.length,
                    physics: const NeverScrollableScrollPhysics(),
                    separatorBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: CommonDivider(
                          color:
                              ColorConstants.color9D9D9D.withValues(alpha: 0.1),
                          height: 1,
                          thickness: 1,
                        ),
                      );
                    },
                    itemBuilder: (context, index) {
                      final item = notificationList2[index];
                      return NotificationTile(index: index, item: item);
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
