import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';

import '../models/notification_model.dart';

class NotificationTile extends StatelessWidget {
  final int index;
  final NotificationModel item;
  const NotificationTile({super.key, required this.index, required this.item});

  @override
  Widget build(BuildContext context) {
    String title = item.title ?? '';
    String icon = (item.icon ?? '').asNotificationPng();
    bool isSelected = item.isSelected ?? false;
    int? id = item.id;
    String deliveredtime = item.deliveredtime ?? '';
    return Container(
      padding: EdgeInsets.only(left: 10, top: 8, right: 10, bottom: 6),
      color: isSelected ? ColorConstants.colorF7F8FF : null,
      child: Column(
        children: [
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: ColorConstants.colorC1C1C1.withValues(alpha: .10)),
                padding: EdgeInsets.all(13),
                height: 42,
                width: 42,
                child: Image.asset(icon),
              ),
              W(10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title),
                    H(1),
                    Text(
                      'Your onsite observation complaint with\tID\t$id has forwarded successfully',
                      style: ts14c9D9D9Dw4h1,
                    )
                  ],
                ),
              )
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                deliveredtime,
                style: ts12c9D9D9Dw4,
              )
            ],
          )
        ],
      ),
    );
  }
}
