import 'package:addc/features/notifications/widgets/notification_container.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_bottom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NotificationsScreen extends StatelessWidget {
  static const route = '/notification_screen';
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text('Notifications', style: ts20c000940w4),
        leading: const CommonBackButton(),
      ),
      body: Padding(
        padding: EdgeInsets.only(left: 12.w, right: 12, top: 25, bottom: 10),
        child: Notificationcontainer(),
      ),
      bottomNavigationBar: const CommonBottomAppbar(),
    );
  }
}
