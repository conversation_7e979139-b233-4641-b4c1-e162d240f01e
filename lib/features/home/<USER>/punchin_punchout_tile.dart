import 'package:addc/features/home/<USER>/models/attendance_status.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../shared/constants/color_constants.dart';
import '../../../shared/constants/text_styles.dart';
import '../../../widgets/common_gradient_dashline_widget.dart';
import '../../widgets/gap.dart';

class PunchinPunchoutTile extends StatelessWidget {
  final AttendanceStatus item;
  const PunchinPunchoutTile({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String? signInTime = item.signIn;
    String? signOutTime = item.signOut;
    // final date = item.date;
    String date = '';
    if (signInTime != null) {
      date = DateFormatter.formatStringDate(
          date: signInTime,
          inputFormat: 'dd/MM/yyyy HH:mm:ss',
          outputFormat: 'dd MMM, yyyy');
      signInTime = DateFormatter.formatStringDate(
          date: signInTime,
          inputFormat: 'dd/MM/yyyy HH:mm:ss',
          outputFormat: 'hh:mm a');
    }
    if (signOutTime != null) {
      signOutTime = DateFormatter.formatStringDate(
          date: signOutTime,
          inputFormat: 'dd/MM/yyyy HH:mm:ss',
          outputFormat: 'hh:mm a');
    }
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: 14.h, left: 14.w, right: 14.w, bottom: 9.h),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18.r),
          color: ColorConstants.colorFFFFFF),
      child: Column(
        children: [
          Row(
            children: [
              Text(signInTime ?? '', style: ts14c1E1E1Ew4),
              Expanded(
                  child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: CommonGradientDashLineWidget(),
              )),
              Text(signOutTime ?? '', style: ts14c1E1E1Ew4),
            ],
          ),
          H(1),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Punch In', style: ts12c9D9D9Dw4),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.h),
                decoration: BoxDecoration(
                    color: ColorConstants.colorE3EEFC,
                    borderRadius: BorderRadius.circular(10.r)),
                child: Text(date, style: ts10c1E1E1Ew4),
              ),
              Text('Punch Out', style: ts12c9D9D9Dw4),
            ],
          ),
        ],
      ),
    );
  }
}
