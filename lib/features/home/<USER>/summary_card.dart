import 'dart:developer';

import 'package:addc/features/leaves/view/leaves_screen.dart';
import 'package:addc/features/permissions/view/permissions_screen.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class SummaryCard extends StatelessWidget {
  final int index;
  final String icon;
  final String title;
  final String? count;
  final String? duration;

  const SummaryCard(
      {super.key,
      required this.index,
      required this.icon,
      this.count,
      required this.title,
      this.duration});

  @override
  Widget build(BuildContext context) {
    List<String> parts = [];
    if (count != null) {
      parts = count?.split(' ') ?? [];
    }
    // log('leaveCount -- $parts');
    String? counter = parts.isNotEmpty ? parts[0] : null;
    String? label;
    // if (parts.length == 2) {
    //   label = parts[1];
    // } else if (parts.length >= 3) {
    //   label = '${parts[1]} ${parts[2]}';
    // }
    switch (index) {
      case 0:
        if (counter == null || counter == '0' || counter == '1') {
          counter ??= '0';
          label = 'Day';
        } else {
          label = 'Days';
        }
        break;
      case 1:
        label = 'Personal Hours';
        break;
    }
    log('label - $label');
    return InkResponse(
      onTap: () => _onTap(context: context, index: index),
      child: Container(
        height: 180.h,
        width: 180.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18.r),
          border: Border.all(color: ColorConstants.colorEDEFFF, width: 1),
          color: ColorConstants.colorFFFFFF,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(18.r),
          child: Stack(
            children: [
              Align(
                  alignment: Alignment.bottomRight,
                  child: SvgPicture.asset('summary_graphics'.asIconSvg())),
              Padding(
                padding: EdgeInsets.only(
                    top: 15.h, bottom: 6.h, left: 12.w, right: 12.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 42.h,
                          width: 42.h,
                          padding: const EdgeInsets.all(11),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorConstants.colorC1C1C1
                                .withValues(alpha: 0.1),
                          ),
                          child: SvgPicture.asset(icon.asIconSvg()),
                        ),
                        if (duration != null)
                          Text(duration ?? '', style: ts12cFFBC47w4),
                      ],
                    ),
                    const Spacer(),
                    Text(title, style: ts12c0AB3A1w4),
                    // H(6),
                    if (counter != null)
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(text: '$counter ', style: ts46c1E1E1Ew4h1),
                            if (label != null)
                              TextSpan(text: '\n$label', style: ts16c1E1E1Ew4),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _onTap({required int index, required BuildContext context}) {
    switch (index) {
      case 0:
        Navigator.pushNamed(context, LeavesScreen.route);
        break;
      case 1:
        Navigator.pushNamed(context, PermissionsScreen.route);
        break;
    }
  }
}
