import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:readmore/readmore.dart';

import '../../../shared/constants/color_constants.dart';
import '../../../shared/constants/text_styles.dart';
import '../../../widgets/common_circle_avatar.dart';
import '../../../widgets/common_divider.dart';
import '../../widgets/gap.dart';
import 'latest_feed_card_footer.dart';

class LatestFeedCardWidget extends StatelessWidget {
  const LatestFeedCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: 16.h, bottom: 14.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18.r),
        color: ColorConstants.colorFFFFFF,
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Column(
              children: [
                Row(
                  children: [
                    CommonCircleAvatar(
                      height: 42.h,
                      image: 'abdullah_obaid',
                    ),
                    W(10),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('Abdullah Obaid', style: ts14c000940w4),
                        Text('1 mins ago', style: ts12c9D9D9Dw4),
                      ],
                    ),
                  ],
                ),
                H(15),
                Container(
                  height: 222.h,
                  width: double.infinity,
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(12.r)),
                  child: Image.asset(
                    fit: BoxFit.cover,
                    'party'.asDummyPng(),
                  ),
                ),
                H(10),
                ReadMoreText(
                  'It’s Party Time, Guys! Happy Birthday to our Digital Manager, Maitha Issa! 🎉🎈 Wishing you a fantastic day filled with joy, success, and endless happiness. May this year bring you even more achievements and memorable moments! Enjoy your special day!',
                  trimMode: TrimMode.Line,
                  style: ts14c1E1E1Ew4,
                  trimLines: 2,
                  colorClickableText: Colors.pink,
                  trimCollapsedText: '.... Read More',
                  trimExpandedText: 'Show less',
                  moreStyle: ts14c1E1E1Ew6,
                ),
                CommonDivider(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    LatestFeedCardFooter(isLike: true),
                    LatestFeedCardFooter(isLike: false),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
