import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../shared/constants/text_styles.dart';
import '../../../widgets/common_bottom_sheet.dart';
import '../../widgets/gap.dart';
import 'commets_bottomsheet.dart';

class LatestFeedCardFooter extends StatelessWidget {
  final bool isLike;
  const LatestFeedCardFooter({super.key, required this.isLike});

  @override
  Widget build(BuildContext context) {
    //showBottomSheet
    String icon = 'comments';
    String count = '49';
    String title = 'Comments';

    if (isLike) {
      icon = 'heart';
      count = '349';
      title = 'Likes';
    }
    return InkWell(
      onTap: () => _onPressed(isLike: isLike, context: context),
      child: Row(
        children: [
          SvgPicture.asset(icon.asIconSvg()),
          W(8),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(text: '$count ', style: ts14c000940w6),
                TextSpan(text: title, style: ts14c000940w4),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _onPressed({required bool isLike, required BuildContext context}) {
    if (!isLike) {
      callBottomSheet(ctx: context, body: const CommentsBottomSheet());
    }
  }
}
