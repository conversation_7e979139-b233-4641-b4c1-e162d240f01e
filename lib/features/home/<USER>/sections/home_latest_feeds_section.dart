import 'package:addc/features/home/<USER>/latest_feed_card_widget.dart';
import 'package:addc/features/master_screen/providers/master_provider.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/widgets/common_section_title.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class HomeLatestFeedsSection extends StatelessWidget {
  const HomeLatestFeedsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final masterProvider = context.read<MasterProvider>();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
        children: [
          H(10),
          CommonSectionTitle(
              title: 'Latest Feeds',
              onViewAllPressed: () {
                masterProvider.masterIndex = 2;
                // Navigator.pushNamed(context, InboxScreen.route);
              }),
          const LatestFeedCardWidget(),
        ],
      ),
    );
  }
}
