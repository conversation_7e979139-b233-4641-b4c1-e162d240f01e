import 'package:addc/features/home/<USER>/models/home_upcoming_event_model.dart';
import 'package:addc/features/upcoming_events/view/upcoming_events_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../widgets/common_section_title.dart';
import '../../../widgets/gap.dart';
import '../../widgets/upcoming_event_card.dart';

class HomeUpcomingEventSection extends StatelessWidget {
  final double? titleGap;
  final double? bottomGap;
  const HomeUpcomingEventSection({super.key, this.titleGap, this.bottomGap});

  @override
  Widget build(BuildContext context) {
    final List<HomeUpcomingEventModel> items = [
      HomeUpcomingEventModel(
          date: 'Jan 20, 2025',
          backgroundImage: 'upcoming_event_1',
          title: 'Annual Day Celebration 2025',
          time: '09:00 AM - 10:00 PM'),
      HomeUpcomingEventModel(
          date: 'Jan 20, 2025',
          backgroundImage: 'upcoming_event_2',
          title: 'Annual Day Celebration 2025',
          time: '09:00 AM - 10:00 PM'),
    ];
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
        children: [
          H(titleGap ?? 15.h),
          CommonSectionTitle(
              title: 'Upcoming Events',
              onViewAllPressed: () =>
                  Navigator.pushNamed(context, UpcomingEventsScreen.route)),
          Row(
            spacing: 10.w,
            children: items.map(
              (e) {
                return Expanded(child: UpcomingEventCard(item: e));
              },
            ).toList(),
          ),
          // SizedBox(
          //   height: 198.h,
          //   width: double.infinity,
          //   child: ListView.separated(
          //     padding: EdgeInsets.symmetric(horizontal: 12.w),
          //     scrollDirection: Axis.horizontal,
          //     shrinkWrap: true,
          //     itemCount: items.length,
          //     itemBuilder: (context, index) {
          //       return UpcomingEventCard(item: items[index]);
          //     },
          //     separatorBuilder: (context, index) => W(10),
          //   ),
          // ),
          H(bottomGap ?? 30),
        ],
      ),
    );
  }
}
