import 'dart:developer';

import 'package:addc/features/customize_home/model/home_customize_model.dart';
import 'package:addc/features/home/<USER>/models/my_leaves_model.dart';
import 'package:addc/features/home/<USER>/my_leaves_tile_widget.dart';
import 'package:addc/features/leaves/view/leaves_screen.dart';
import 'package:addc/features/profile/providers/profile_provider.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_divider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../widgets/common_section_title.dart';

class HomeMyLeavesSection extends StatelessWidget {
  final Future<List<MyLeavesModel>?> future;
  const HomeMyLeavesSection({super.key, required this.future});

  @override
  Widget build(BuildContext context) {
    // Home screen customization
    final provider = context.watch<ProfileProvider>();
    List<HomeCustomizeModel> items = provider.homeCustomizations
        .where((element) => element.isSelected == true && (element.index == 5))
        .toList();
    if (items.isEmpty) {
      return const SizedBox.shrink();
    }
    // Home screen customization
    return FutureBuilder(
      future: future,
      builder: (context, snapshot) {
        bool isLoading = false;
        List<MyLeavesModel> items = [];
        if (snapshot.connectionState == ConnectionState.waiting) {
          items = [
            MyLeavesModel(
                startDate: '2023-12-01',
                absenceType: 'Duty Leave',
                approvalStatusCd: 'Approved'),
            MyLeavesModel(
                startDate: '2023-12-01',
                absenceType: 'Duty Leave',
                approvalStatusCd: 'Approved')
          ];
          isLoading = true;
        } else if (!snapshot.hasData) {
          return Text('No data');
        } else if (snapshot.hasError) {
          return Text('Something went wrong');
        }
        if (snapshot.connectionState == ConnectionState.done) {
          isLoading = false;
          items = snapshot.data!;
        }
        log('message - ${items.length}');
        return Skeletonizer(
          enabled: isLoading,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Column(
              children: [
                H(20),
                CommonSectionTitle(title: 'My Leaves'),
                H(10),
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: ColorConstants.colorFFFFFF,
                      borderRadius: BorderRadius.circular(18.r)),
                  padding: EdgeInsets.only(top: 30.h),
                  child: items.isNotEmpty
                      ? Column(
                          children: [
                            ListView.separated(
                              physics: const NeverScrollableScrollPhysics(),
                              padding: EdgeInsets.symmetric(horizontal: 12.w),
                              shrinkWrap: true,
                              itemCount: items.take(4).length,
                              itemBuilder: (context, index) =>
                                  MyLeavesTileWidget(item: items[index]),
                              separatorBuilder: (context, index) =>
                                  CommonDivider(),
                            ),
                            H(12),
                            if (items.length > 4) ...[
                              TextButton(
                                  style: TextButton.styleFrom(
                                      foregroundColor:
                                          ColorConstants.primaryColor),
                                  onPressed: () => Navigator.pushNamed(
                                      context, LeavesScreen.route),
                                  child:
                                      Text('View All', style: ts16c0AB3A1w4)),
                              H(6),
                            ]
                          ],
                        )
                      : Column(
                          children: [
                            Align(
                                alignment: Alignment.center,
                                child: Text('No Leaves found')),
                            H(20),
                          ],
                        ),
                ),
                H(20),
              ],
            ),
          ),
        );
      },
    );
  }
}
