import 'package:addc/features/home/<USER>/providers/punch_in_punch_out_provider.dart';
import 'package:addc/features/home/<USER>/punchin_punchout_tile.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../widgets/common_section_title.dart';

class HomePunchinOutDetailsSection extends StatelessWidget {
  const HomePunchinOutDetailsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<PunchInPunchOutProvider>(
      builder: (context, provider, _) {
        final items = provider.attendanceStatusList;
        if (items.isEmpty) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Column(
              children: [
                CommonSectionTitle(title: 'Punch In - Out Details'),
                <PERSON>(10),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: ColorConstants.colorFFFFFF,
                      borderRadius: BorderRadius.circular(18.r)),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 20.0),
                        child: Text('No Punch In/Out Details Found'),
                      )
                    ],
                  ),
                ),
                H(35),
              ],
            ),
          );
        }
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          child: Column(
            children: [
              CommonSectionTitle(title: 'Punch In - Out Details'),
              H(10),
              ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                itemCount: items.length,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  final item = items[index];
                  return PunchinPunchoutTile(item: item);
                },
                separatorBuilder: (context, index) => H(7),
              ),
              H(kBottomNavigationBarHeight)
            ],
          ),
        );
      },
    );
  }
}
