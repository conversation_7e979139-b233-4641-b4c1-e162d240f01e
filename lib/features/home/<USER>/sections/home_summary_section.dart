import 'dart:developer';

import 'package:addc/features/customize_home/model/home_customize_model.dart';
import 'package:addc/features/home/<USER>/summary_card.dart';
import 'package:addc/features/profile/providers/profile_provider.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HomeSummarySection extends StatelessWidget {
  const HomeSummarySection({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ProfileProvider>();
    List<HomeCustomizeModel> items = provider.homeCustomizations
        .where((element) =>
            element.isSelected == true &&
            (element.index == 0 || element.index == 1))
        .toList();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
        children: [
          H(10),
          Skeletonizer(
            enabled: provider.isLoading,
            child: GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              // padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 25.h),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 9.w,
                mainAxisSpacing: 10.h,
                childAspectRatio: 1,
              ),
              itemCount: items.length,
              itemBuilder: (context, index) {
                HomeCustomizeModel item = items[index];
                log('indexes - ${item.index}');
                // return HomeCustomizeCard(item: item, index: index);
                return SummaryCard(
                  icon: item.icon ?? 'clock',
                  index: item.index ?? 0,
                  count: item.count,
                  title: item.title ?? '',
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
