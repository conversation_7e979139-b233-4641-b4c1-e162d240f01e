import 'dart:developer';

import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/utils/urls.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:dio/dio.dart';

class PunchInPunchOutServices {
  final dio = Dio();
  Future<Response> getClockIn() async {
    final formattedDate = DateFormatter.formatDateTime(
        dateTime: DateTime.now(), outputFormat: 'dd/MM/yyyy hh:mm:ss');
    log('formattedDate - $formattedDate - ${LoginedUser.employeeId}');
    final String xmlBody =
        '''<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:PostClockIn>
         <!--Optional:-->
         <tem:pPersonId>${LoginedUser.employeeId}</tem:pPersonId>
         <!--Optional:-->
         <tem:pDT>$formattedDate</tem:pDT>
      </tem:PostClockIn>
   </soapenv:Body>
</soapenv:Envelope>''';

    Response response = await dio.post(
      ApiConstants.endPointWebTAUrl,
      data: xmlBody,
      options: Options(
        headers: {
          'Content-Type': 'text/xml; charset=utf-8',
          'SOAPAction': '${ApiConstants.soapActionUrl}PostClockIn',
        },
      ),
    );
    return response;
  }

  Future<Response> getAttendanceStatus({required DateTime date}) async {
    final formattedDate = DateFormatter.formatDateTime(
        dateTime: date, outputFormat: 'dd/MM/yyyy');
    log('formattedDate - $formattedDate');
    final String xmlBody =
        '''<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:GetEmpAttendance>
         <!--Optional:-->
         <tem:pPersonId>${LoginedUser.employeeId}</tem:pPersonId>
         <!--Optional:-->
         <tem:pDate>$formattedDate</tem:pDate>
      </tem:GetEmpAttendance>
   </soapenv:Body>
</soapenv:Envelope>''';
    //  <tem:pPersonId>${LoginedUser.employeeId}</tem:pPersonId>
    //  <!--Optional:-->
    //  <tem:pDate>$formattedDate</tem:pDate>
    Response response = await dio.post(
      ApiConstants.endPointWebTAUrl,
      data: xmlBody,
      options: Options(
        headers: {
          'Content-Type': 'text/xml; charset=utf-8',
          'SOAPAction': '${ApiConstants.soapActionUrl}GetEmpAttendance',
        },
      ),
    );
    log('getAttendanceStatus data - >${response.data}  ');
    log('getAttendanceStatus - >employeeId  ${LoginedUser.employeeId} ');
    log('getAttendanceStatus - > ${response.realUri}');
    return response;
  }
}
