import 'dart:developer';

import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';

class LeaveBalanceServices {
  Dio dio = Dio();
  CancelToken? _cancelToken;
  Future<Response> getLeaveBalances() async {
    if (_cancelToken != null && !_cancelToken!.isCancelled) {
      _cancelToken!.cancel("Cancelled previous request");
    }
    _cancelToken = CancelToken();
    StringBuffer urlBuffer = StringBuffer(
        '${ApiConstants.planBalances}?q=personId=${LoginedUser.personId}');
    log(' -authHeader is ${ApiConstants.authHeader()}');
    Response response = await dio.get(urlBuffer.toString(),
        options: Options(
            headers: ApiConstants.authHeader(),
            validateStatus: (status) => true),
        cancelToken: _cancelToken);
    log('urlBuffer -- $urlBuffer - ${response.data} - ${response.realUri}');
    return response;
  }

  Future<Response> getLeaveBalanceCountWebTA() async {
    final response = await dio.get(
      '${ApiConstants.planBalances}?q=personId=${LoginedUser.personId}',
      options: Options(
          headers: ApiConstants.authHeader(), validateStatus: (status) => true),
    );
    return response;
  }
}
