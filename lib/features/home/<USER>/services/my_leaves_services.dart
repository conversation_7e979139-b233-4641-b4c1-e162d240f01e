import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';

class MyLeavesServices {
  final Dio _dio = Dio();
  CancelToken? _cancelToken;
  Future<Response> getMyLeaves({int? page, int? limit, String? sortKey}) async {
    if (_cancelToken != null && !_cancelToken!.isCancelled) {
      _cancelToken!.cancel("Cancelled previous request");
    }
    _cancelToken = CancelToken();
    StringBuffer urlBuffer = StringBuffer(
        '${ApiConstants.employeeAbsence}?offset=${page ?? 0}&limit=${limit ?? 5}&q=personNumber=${LoginedUser.employeeId}');
    if (sortKey != null) {
      urlBuffer.write(";approvalStatusCd=$sortKey");
    }
    urlBuffer.write('&orderBy=creationDate:desc');
    Response? response = await _dio.get(urlBuffer.toString(),
        options: Options(
            headers: ApiConstants.authHeader(),
            validateStatus: (status) => true),
        cancelToken: _cancelToken);
    return response;
  }
}
