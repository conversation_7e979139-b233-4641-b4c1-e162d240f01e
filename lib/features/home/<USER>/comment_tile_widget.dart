import 'package:addc/features/home/<USER>/models/comment_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../shared/constants/text_styles.dart';
import '../../../widgets/common_circle_avatar.dart';
import '../../widgets/gap.dart';

class CommentTileWidget extends StatelessWidget {
  final CommentModel item;
  const CommentTileWidget({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String comment = item.comment ?? '';
    String designation = item.designation ?? '';
    String image = item.image ?? '';
    String name = item.name ?? '';
    String time = item.time ?? '';
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonCircleAvatar(height: 32.h, image: image),
        W(12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(name, style: ts14c000940w4h1),
            Text(designation, style: ts12c9D9D9Dw4),
            H(5),
            Text(comment, style: ts16c181818w2h1),
          ],
        ),
        const Spacer(),
        Text(time, style: ts12c9D9D9Dw4),
      ],
    );
  }
}
