// ignore_for_file: use_build_context_synchronously
import 'dart:developer';

import 'package:addc/features/home/<USER>/models/my_leaves_model.dart';
import 'package:addc/features/home/<USER>/providers/my_leaves_provider.dart';
import 'package:addc/features/home/<USER>/providers/punch_in_punch_out_provider.dart';
import 'package:addc/features/home/<USER>/sections/home_latest_feeds_section.dart';
import 'package:addc/features/home/<USER>/sections/home_my_leaves_section.dart';
import 'package:addc/features/home/<USER>/sections/home_punchin_out_details_section.dart';
import 'package:addc/features/home/<USER>/sections/home_summary_section.dart';
import 'package:addc/features/home/<USER>/sections/home_upcoming_event_section.dart';
import 'package:addc/features/profile/providers/profile_provider.dart';
import 'package:addc/main.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../master_screen/view/widgets/master_appbar.dart';
import '../widgets/home_punch_in_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  late ProfileProvider _profileProvider;
  late MyLeavesProvider _myLeavesProvider;
  late PunchInPunchOutProvider _punchInPunchOutProvider;
  late Future<List<MyLeavesModel>?> _myLeavesFuture;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _init();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // When app comes to foreground, restore the countdown timer
      if (mounted) {
        log('count down resumed');
        _punchInPunchOutProvider.restorePunchInCountdown();
        log('count down resumed 2');
      }
    }
    super.didChangeAppLifecycleState(state);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MasterAppBar(
          textEditingController: TextEditingController(), context: context),
      body: SingleChildScrollView(
        child: Column(
          children: [
            HomePunchInCard(),
            HomeSummarySection(),
            if (!isTestVersion) HomeLatestFeedsSection(),
            if (!isTestVersion) HomeUpcomingEventSection(),
            HomeMyLeavesSection(future: _myLeavesFuture),
            HomePunchinOutDetailsSection(),
          ],
        ),
      ),
    );
  }

  _init() async {
    _profileProvider = context.read<ProfileProvider>();
    _myLeavesProvider = context.read<MyLeavesProvider>();
    _punchInPunchOutProvider = context.read<PunchInPunchOutProvider>();
    _myLeavesFuture = _myLeavesProvider.getMyLeaves();

    // await _permissionProvider.getViolationCount();

    await Future.wait([
      _profileProvider.getHomeCustomisationsForHome(context: context),
      _punchInPunchOutProvider.getAttendanceStatus(),
      _punchInPunchOutProvider.getPunchInPunchOutList(),
    ]);

    // WidgetsBinding.instance.addPostFrameCallback(
    //   (timeStamp) {
    //     setState(() {});
    //     // _profileProvider.getProfile();
    //     // _profileProvider.getAssignmentBatch();
    //   },
    // );
  }
}
