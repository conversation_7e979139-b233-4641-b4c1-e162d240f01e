import 'dart:convert';
import 'dart:developer';
import 'package:addc/features/home/<USER>/models/my_leaves_model.dart';
import 'package:addc/features/home/<USER>/services/my_leaves_services.dart';
import 'package:flutter/foundation.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

class MyLeavesProvider extends ChangeNotifier {
  bool isLoading = false;

  final List<String> leaveCategories = [
    "All",
    "Awaiting",
    "Approved",
    "Denied"
  ];
  int selectedCategoryIndex = 0;
  void onCategoryChanged({required int index}) {
    if (isLoading) return; // ignore: avoid_redundant_await
    selectedCategoryIndex = index;
    myLeavesCurrentPage = 0;
    filterKey = leaveCategories[index];
    if (filterKey == leaveCategories.first) {
      filterKey = null; // reset filter key
    }
    myLeavesPagingController.refresh();
    notifyListeners();
  }

  void onInBoxCategoryChanged({required int index}) {
    if (isLoading) return; // ignore: avoid_redundant_await
    selectedCategoryIndex = index;
    myLeavesCurrentPage = 0;
    filterKey = leaveCategories[index];
    if (filterKey == leaveCategories.first) {
      filterKey = null; // reset filter key
    }
    inBoxLeavesPagingController.refresh();
    notifyListeners();
  }

  Future<List<MyLeavesModel>?> getMyLeaves() async {
    final response = await MyLeavesServices().getMyLeaves();
    log('getMyLeaves --> ${jsonEncode(response.data)}');
    if (response.statusCode == 200) {
      Map<String, dynamic> json = response.data;
      if (json.containsKey('items')) {
        List data = json['items'] ?? [];
        return data.map((e) => MyLeavesModel.fromJson(e)).toList();
      }
    }
    return null;
  }

  int myLeavesCurrentPage = 0;
  String? filterKey;
  late PagingController<int, MyLeavesModel> myLeavesPagingController;
  void initMyLeavesPagination() {
    filterKey = null;
    selectedCategoryIndex = 0;
    myLeavesCurrentPage = 0;
    myLeavesPagingController = PagingController(firstPageKey: 1);
    myLeavesPagingController.addPageRequestListener(
      (pageKey) {
        getMyLeavesWithPagination(page: pageKey);
      },
    );
  }

  Future<void> getMyLeavesWithPagination({required int page}) async {
    // try {
    if (myLeavesCurrentPage != page) {
      myLeavesCurrentPage = page;
      String? sortKey;
      if (filterKey == null) {
        sortKey = null;
      } else {
        sortKey = filterKey?.toUpperCase();
      }
      isLoading = true;
      final response = await MyLeavesServices()
          .getMyLeaves(limit: 15, page: page, sortKey: sortKey);
      isLoading = false;
      if (kDebugMode) {
        log('getMyLeavesWithPagination------${response.data} - ${response.realUri}');
      }
      if (response.statusCode == 200) {
        Map<String, dynamic> records = response.data;
        List<MyLeavesModel> temp = (records['items'] as List)
            .map((e) => MyLeavesModel.fromJson(e))
            .toList();

        if (records['hasMore'] == true) {
          myLeavesPagingController.appendPage(temp, page + 1);
        } else {
          myLeavesPagingController.appendLastPage(temp);
        }
      } else {
        myLeavesPagingController.appendLastPage([]);
      }
    }
    // } on DioException catch (e) {
    //   isLoading = false;
    //   debugPrint(e.toString());
    // }
  }

  int inBoxLeavesCurrentPage = 0;
  String? inBoxfilterKey;
  late PagingController<int, MyLeavesModel> inBoxLeavesPagingController;
  void initInBoxLeavesPagination() {
    filterKey = null;
    selectedCategoryIndex = 0;
    inBoxLeavesCurrentPage = 0;
    inBoxLeavesPagingController = PagingController(firstPageKey: 1);
    inBoxLeavesPagingController.addPageRequestListener(
      (pageKey) {
        getInBoxLeavesWithPagination(page: pageKey);
      },
    );
  }

  Future<void> getInBoxLeavesWithPagination({required int page}) async {
    // try {
    if (inBoxLeavesCurrentPage != page) {
      myLeavesCurrentPage = page;
      String? sortKey;
      if (filterKey == null) {
        sortKey = null;
      } else {
        sortKey = filterKey?.toUpperCase();
      }
      isLoading = true;
      final response = await MyLeavesServices()
          .getMyLeaves(limit: 15, page: page, sortKey: sortKey);
      isLoading = false;
      if (kDebugMode) {
        log('getInBoxLeavesWithPagination------${response.data} - ${response.realUri}');
      }
      if (response.statusCode == 200) {
        Map<String, dynamic> records = response.data;
        List<MyLeavesModel> temp = (records['items'] as List)
            .map((e) => MyLeavesModel.fromJson(e))
            .toList();

        if (records['hasMore'] == true) {
          inBoxLeavesPagingController.appendPage(temp, page + 1);
        } else {
          inBoxLeavesPagingController.appendLastPage(temp);
        }
      } else {
        inBoxLeavesPagingController.appendLastPage([]);
      }
    }
    // } on DioException catch (e) {
    //   isLoading = false;
    //   debugPrint(e.toString());
    // }
  }
}
