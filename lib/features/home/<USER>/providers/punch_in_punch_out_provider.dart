import 'dart:async';
import 'dart:developer';
import 'package:addc/features/home/<USER>/models/attendance_status.dart';
import 'package:addc/features/home/<USER>/models/clock_in_model.dart';
import 'package:addc/features/home/<USER>/services/punch_in_punch_out_services.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/common_success_bottom_sheet.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:xml/xml.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PunchInPunchOutProvider extends ChangeNotifier {
  bool isLoading = false;
  Timer? _countdownTimer;
  int _secondsLeft = 0;
  int get secondsLeft => _secondsLeft;
  bool get isCountdownActive => _secondsLeft > 0;

  PunchInPunchOutProvider() {
    restorePunchInCountdown();
  }

  Future<void> savePunchInStartTime() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setInt('punch_in_start_time', DateTime.now().millisecondsSinceEpoch);
  }

  Future<void> restorePunchInCountdown() async {
    final prefs = await SharedPreferences.getInstance();
    final startTime = prefs.getInt('punch_in_start_time');
    if (startTime != null) {
      final now = DateTime.now().millisecondsSinceEpoch;
      final elapsed = ((now - startTime) / 1000).round();
      final remaining = 15 * 60 - elapsed;
      if (remaining > 0) {
        startCountdown(remaining);
      } else {
        stopCountdown();
        prefs.remove('punch_in_start_time');
      }
    }
  }

  void startCountdown(int seconds) {
    _countdownTimer?.cancel();
    _secondsLeft = seconds;
    notifyListeners();
    _countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) async {
      if (_secondsLeft > 0) {
        _secondsLeft--;
        notifyListeners();
      } else {
        _countdownTimer?.cancel();
        final prefs = await SharedPreferences.getInstance();
        prefs.remove('punch_in_start_time');
      }
    });
  }

  void stopCountdown() async {
    _countdownTimer?.cancel();
    _secondsLeft = 0;
    final prefs = await SharedPreferences.getInstance();
    prefs.remove('punch_in_start_time');
    notifyListeners();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  Future<BottomSheetStatus?> getClockIn() async {
    try {
      if (isLoading) return BottomSheetStatus.none;
      isLoading = true;
      EasyLoading.show();
      final response = await PunchInPunchOutServices().getClockIn();
      EasyLoading.dismiss();
      isLoading = false;
      log('getClockIn - ${response.statusCode} - ${response.data}');
      if (response.statusCode == 200) {
        final XmlDocument document = XmlDocument.parse(response.data);
        final clockInElement = document.findAllElements('ClockIn').firstOrNull;
        if (clockInElement != null) {
          final model = ClockInModel.fromXml(clockInElement);
          log('Status: ${model.status}, Description: ${model.statusDesc}');
          if (model.status == '1' &&
              model.statusDesc.toLowerCase() == 'success') {
            log('Clock in success');
            await savePunchInStartTime();
            startCountdown(15 * 60); // 15 minutes
            return BottomSheetStatus.success;
          } else if (model.status == '14' &&
              model.statusDesc.toLowerCase() == 'action already taken') {
            showToast(model.statusDesc);
            return BottomSheetStatus.none;
          } else if (model.status == '3') {
            showToast(model.statusDesc);
            return BottomSheetStatus.none;
          }
        } else {
          log('No <ClockIn> element found.');
          return BottomSheetStatus.failure;
        }
      } else {
        log("SOAP Request failed: ${response.statusCode} - ${response.data}");
        return BottomSheetStatus.failure;
      }
      return BottomSheetStatus.none;
    } catch (e) {
      log('Error: $e');
      return null;
    } finally {
      EasyLoading.dismiss();
      isLoading = false;
    }
  }

  AttendanceStatus? attendanceStatusModel;
  Future<void> getAttendanceStatus() async {
    try {
      final response = await PunchInPunchOutServices()
          .getAttendanceStatus(date: DateTime.now());
      if (response.statusCode == 200) {
        final XmlDocument document = XmlDocument.parse(response.data);
        final empAttendanceElement =
            document.findAllElements('EmpAttendance').firstOrNull;
        if (empAttendanceElement != null) {
          attendanceStatusModel =
              AttendanceStatus.fromXml(empAttendanceElement);
          log("Sign In: ${attendanceStatusModel?.signIn}");
          log("Sign Out: ${attendanceStatusModel?.signOut}");
        } else {
          log("No <EmpAttendance> element found.");
        }
      }
      notifyListeners();
    } catch (e) {
      log('Error: $e');
    }
  }

  List<AttendanceStatus> attendanceStatusList = [];

  Future<void> getPunchInPunchOutList() async {
    final date = DateTime(2021, 09, 08);
    // final date = DateTime.now();
    attendanceStatusList.clear();
    await getPunchInPunchOut(date: date);
    await getPunchInPunchOut(date: date.subtract(Duration(days: 1)));
    await getPunchInPunchOut(date: date.subtract(Duration(days: 2)));
    attendanceStatusList.map((e) => log(
        'getPunchInPunchOutList -> ${e.date}-signIn>${e.signIn}-${e.signOut}'));
    log('getPunchInPunchOutList --> ${attendanceStatusList.map((e) => e.signOut)}');
    notifyListeners();
  }

  Future<void> getPunchInPunchOut({required DateTime date}) async {
    final response =
        await PunchInPunchOutServices().getAttendanceStatus(date: date);
    if (response.statusCode == 200) {
      final formattedDate = DateFormatter.formatDateTime(
          dateTime: date, outputFormat: 'dd MMM, yyyy');
      final XmlDocument document = XmlDocument.parse(response.data);
      final empAttendanceElement =
          document.findAllElements('EmpAttendance').firstOrNull;
      log('empAttendanceElement --${empAttendanceElement}');
      if (empAttendanceElement != null) {
        final attendanceStatusModel =
            AttendanceStatus.fromXml(empAttendanceElement);
        if (attendanceStatusModel.signIn != null) {
          final item = AttendanceStatus(
              signIn: attendanceStatusModel.signIn,
              signOut: attendanceStatusModel.signOut,
              date: formattedDate);
          attendanceStatusList.add(item);
          attendanceStatusList.toSet().toList();
        }
      } else {
        log("No <EmpAttendance> element found.");
      }
    }
  }
}
