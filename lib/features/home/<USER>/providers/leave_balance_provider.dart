import 'dart:convert';
import 'dart:developer';
import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/home/<USER>/models/leave_balance_model.dart';
import 'package:addc/features/home/<USER>/services/leave_balance_services.dart';
import 'package:flutter/material.dart';

class LeaveBalanceProvider extends ChangeNotifier {
  LeaveBalanceModel? leaveBalanceModel;
  Future<String> getLeaveBalance() async {
    try {
      leaveBalanceModel = null;
      final response = await LeaveBalanceServices().getLeaveBalances();
      log('getLeaveBalance -> ${jsonEncode(response.data)} - ${response.realUri} - ${LoginedUser.employeeId}');

      if (response.statusCode == 200) {
        Map<String, dynamic> json = response.data;
        if (json.containsKey('items')) {
          List data = (json['items'] ?? []) as List;
          final items = data.map((e) => LeaveBalanceModel.fromJson(e)).toList();
          if (items.isNotEmpty) {
            final item = items.firstWhere(
                (element) => '${element.personId}' == LoginedUser.personId);
            leaveBalanceModel = item;
            log('getLeaveBalance -> ${item.formattedBalance}');
            return item.formattedBalance ?? '0';
          } else {
            leaveBalanceModel = null;
            return '0';
          }
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return '0';
  }

  Future<void> getLeaveBalanceCountWebTA() async {}
}
