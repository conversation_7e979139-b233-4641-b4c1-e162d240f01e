import 'package:addc/features/event_detail/view/event_detalis_screen.dart';
import 'package:addc/features/home/<USER>/models/home_upcoming_event_model.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../shared/constants/color_constants.dart';
import '../../../shared/constants/text_styles.dart';
import '../../widgets/gap.dart';

class UpcomingEventCard extends StatelessWidget {
  final HomeUpcomingEventModel item;
  const UpcomingEventCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String backgroundImage = item.backgroundImage;
    String date = item.date;
    String time = item.time;
    String title = item.title;
    return InkWell(
      onTap: () => Navigator.pushNamed(context, EventDetalisScreen.route),
      child: Container(
        width: 180.w,
        padding: EdgeInsets.only(top: 6.h, left: 6.w, right: 6.w, bottom: 10.h),
        decoration: BoxDecoration(
            color: ColorConstants.colorFFFFFF,
            borderRadius: BorderRadius.circular(18.r)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: Stack(
                children: [
                  Image.asset(
                    height: 120.h,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    backgroundImage.asDummyPng(),
                  ),
                  Container(
                    margin: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        border: Border.all(
                            width: 0.5,
                            color: ColorConstants.colorFFFFFF
                                .withValues(alpha: .01)),
                        color:
                            ColorConstants.color0AB3A1.withValues(alpha: 0.3)),
                    padding:
                        EdgeInsets.symmetric(vertical: 5.5.h, horizontal: 6.w),
                    child: Text(
                      date,
                      style: ts12cFFFFFFw4,
                    ),
                  )
                ],
              ),
            ),
            H(8),
            Text(title, style: ts14c000940w4h1),
            // H(5),
            Text(time, style: ts12c9D9D9Dw4)
          ],
        ),
      ),
    );
  }
}
