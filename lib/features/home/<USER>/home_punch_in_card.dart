import 'dart:developer';
import 'dart:ui';
import 'package:addc/features/home/<USER>/models/attendance_status.dart';
import 'package:addc/features/home/<USER>/providers/punch_in_punch_out_provider.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/common_success_bottom_sheet.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../shared/constants/color_constants.dart';
import '../../../shared/constants/text_styles.dart';
import '../../widgets/gap.dart';

class HomePunchInCard extends StatelessWidget {
  const HomePunchInCard({super.key});

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final formattedDate = DateFormatter.formatDateTime(
        dateTime: now, outputFormat: 'dd MMMM, yyyy');
    final weekdayName = DateFormat('EEEE').format(now);
    String dateOnly =
        DateFormatter.formatDateTime(dateTime: now, outputFormat: 'dd');

    return Column(
      children: [
        H(25.h),
        Container(
          height: 140.h,
          width: double.infinity,
          margin: EdgeInsets.symmetric(horizontal: 12.w),
          child: Stack(
            children: [
              Container(
                height: 140.h,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(18.r),
                  gradient: RadialGradient(
                    center: Alignment.bottomLeft,
                    focalRadius: 0,
                    radius: 1.4,
                    focal: Alignment.bottomLeft,
                    colors: [
                      ColorConstants.color071986.withValues(alpha: 0.7),
                      ColorConstants.color071986,
                      ColorConstants.color000940,
                    ],
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 10.w, vertical: 10.w),
                      child: Column(
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                height: 32.h,
                                width: 32.h,
                                padding: EdgeInsets.all(7),
                                decoration: BoxDecoration(
                                  color: ColorConstants.colorFFFFFF
                                      .withValues(alpha: 0.1),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: ColorConstants.colorFFFFFF
                                        .withValues(alpha: 0.1),
                                    width: 0.5,
                                  ),
                                ),
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    SvgPicture.asset(
                                      'calendar_grey'.asIconSvg(),
                                    ),
                                    FittedBox(
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                            bottom: 2.0,
                                            left: 2,
                                            right: 2,
                                            top: 10),
                                        child: Text(dateOnly,
                                            style: TextStyle(
                                                color:
                                                    ColorConstants.color071986)
                                            //  ColorConstants.color071986.withValues(alpha: 0.7),
                                            // ColorConstants.color071986,
                                            // ColorConstants.color000940,
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              W(10),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(weekdayName, style: ts12cD7D7D7w4),
                                  Text(formattedDate, style: ts12cFFFFFFw4),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomRight,
                      child: Image.asset(
                        'punch_in_card_half_circle'.asImagePng(),
                        scale: 4,
                      ),
                    )
                  ],
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      // height: 75.h,
                      width: double.infinity,
                      child: Container(
                        padding: EdgeInsets.all(9),
                        // decoration: BoxDecoration(
                        //     color: ColorConstants.colorFFFFFF.withValues(alpha: 0.1),
                        //     shape: BoxShape.circle,
                        //     border: Border.all(
                        //         color: ColorConstants.colorFFFFFF, width: 0.5)),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12.r),
                          child: BackdropFilter(
                            filter: ImageFilter.blur(
                                sigmaX: 5.0,
                                sigmaY: 5.0,
                                tileMode: TileMode.repeated),
                            child: Container(
                              padding: EdgeInsets.symmetric(vertical: 15.h),
                              color: ColorConstants.colorFFFFFF
                                  .withValues(alpha: 0.1),
                              child: Padding(
                                padding:
                                    EdgeInsets.only(left: 12.w, right: 16.w),
                                child: Consumer<PunchInPunchOutProvider>(
                                  builder: (context, provider, _) {
                                    AttendanceStatus? item =
                                        provider.attendanceStatusModel;
                                    String? signIn = item?.signIn;
                                    String? signOut = item?.signOut;
                                    log('signIn - $signIn');
                                    log('signOut - $signOut');
                                    // Can punch in if not signed in yet
                                    bool canPunchIn =
                                        signIn == null || signIn == 'NULL';
                                    // Can punch out if signed in but not signed out
                                    bool canPunchOut = signIn != null &&
                                        (signOut == null || signOut == 'NULL');
                                    String title = canPunchIn
                                        ? 'Punch In Time'
                                        : 'Punch Out Time';
                                    String buttonTitle =
                                        canPunchIn ? 'Punch In' : 'Punch Out';
                                    String punchInTime = '';

                                    if (signIn != null && signIn != 'NULL') {
                                      log('punchInTime 1');
                                      punchInTime =
                                          DateFormatter.formatStringDate(
                                              date: signIn,
                                              inputFormat:
                                                  'dd/MM/yyyy HH:mm:ss',
                                              outputFormat: 'hh:MM a');
                                    }

                                    if (signOut != null && signOut != 'NULL') {
                                      log('punchInTime 2');
                                      punchInTime =
                                          DateFormatter.formatStringDate(
                                              date: signOut,
                                              inputFormat:
                                                  'dd/MM/yyyy HH:mm:ss',
                                              outputFormat: 'hh:MM a');
                                    }

                                    return Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(title, style: ts12cD7D7D7w4),
                                            if (punchInTime.isNotEmpty)
                                              Text(punchInTime,
                                                  style: ts16cFFFFFFw4),
                                          ],
                                        ),
                                        provider.isCountdownActive
                                            ? _CountdownTimerWidget(
                                                secondsLeft:
                                                    provider.secondsLeft)
                                            : ElevatedButton(
                                                style: ElevatedButton.styleFrom(
                                                    minimumSize:
                                                        Size(120.w, 40.h)),
                                                onPressed: canPunchIn ||
                                                        canPunchOut
                                                    ? () => _onPunchinPerformed(
                                                          provider: provider,
                                                          context: context,
                                                        )
                                                    : null,
                                                child: Text(buttonTitle),
                                              ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  _onPunchinPerformed(
      {required PunchInPunchOutProvider provider,
      required BuildContext context}) async {
    // Check if current time is within allowed punch-in hours (6:45 AM - 9:00 AM)
    final now = DateTime.now();
    final startTime = DateTime(now.year, now.month, now.day, 6, 45); // 6:45 AM
    final endTime = DateTime(now.year, now.month, now.day, 9, 0); // 9:00 AM

    if (now.isBefore(startTime) || now.isAfter(endTime)) {
      // Show error message if outside allowed time range
      successBottomsheet(
        status: BottomSheetStatus.failure,
        context: context,
        title: 'Punch In Not Available',
        description:
            'This feature works only between 6:45AM - 9:00AM and\nemployee must tag their ID at the access gate',
        onPressed: () => Navigator.pop(context),
      );
      return;
    }
    BottomSheetStatus? status = await provider.getClockIn();
    if (status != null) {
      String title = 'Punch In Success';
      String description = 'You have successfully punched in';
      String? extraMessage;
      switch (status) {
        case BottomSheetStatus.success:
          extraMessage =
              'This feature works only between 6:45AM - 9:00AM and\nemployee must tag their ID at the access gate';
          break;
        case BottomSheetStatus.failure:
          title = 'Punch In Failed';
          description = 'Failed to punch in';
          break;
        case BottomSheetStatus.warning:
          title = 'Something went wrong';
          description = 'Please try again';
          break;
        case BottomSheetStatus.none:
          break;
      }
      if (status != BottomSheetStatus.none) {
        provider.getAttendanceStatus();
        provider.getPunchInPunchOutList();
        successBottomsheet(
            status: status,
            // ignore: use_build_context_synchronously
            context: context,
            title: title,
            description: extraMessage != null
                ? '$description\n\n$extraMessage'
                : description,
            onPressed: () => Navigator.pop(context));
      }
    } else {
      showToast('Punch In Failed');
    }
  }
}

class _CountdownTimerWidget extends StatelessWidget {
  final int secondsLeft;
  const _CountdownTimerWidget({required this.secondsLeft});

  @override
  Widget build(BuildContext context) {
    final minutes = (secondsLeft ~/ 60).toString().padLeft(2, '0');
    final seconds = (secondsLeft % 60).toString().padLeft(2, '0');
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.timer, color: Colors.white),
          SizedBox(width: 8),
          Text('$minutes:$seconds', style: ts16cFFFFFFw4),
        ],
      ),
    );
  }
}
