import 'package:xml/xml.dart';

class ClockInModel {
  final String status;
  final String statusDesc;

  ClockInModel({
    required this.status,
    required this.statusDesc,
  });

  factory ClockInModel.fromXml(XmlElement element) {
    return ClockInModel(
      status: element.getElement('STATUS')?.innerText ?? '',
      statusDesc: element.getElement('STATUS_DESC')?.innerText ?? '',
    );
  }
}
