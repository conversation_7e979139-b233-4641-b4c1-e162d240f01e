class MyLeavesModel {
  String? absenceCaseId;
  bool? absenceEntryBasicFlag;
  String? absencePatternCd;
  String? absenceStatusCd;
  int? absenceTypeId;
  int? absenceTypeReasonId;
  String? agreementId;
  String? approvalStatusCd;
  String? comments;
  String? confirmedDate;
  String? creationDate;
  num? duration;
  String? endDate;
  num? endDateDuration;
  String? endDateTime;
  String? startDate;
  num? startDateDuration;
  String? startDateTime;
  String? startTime;
  String? submittedDate;
  String? userMode;
  String? personNumber;
  String? absenceType;
  String? employer;
  String? absenceReason;
  String? absenceDispStatus;
  String? formattedDuration;
  String? absenceDispStatusMeaning;
  Stream? absenceUpdatableFlag;
  String? approvalDatetime;

  MyLeavesModel(
      {this.absenceCaseId,
      this.absenceEntryBasicFlag,
      this.absencePatternCd,
      this.absenceStatusCd,
      this.absenceTypeId,
      this.absenceTypeReasonId,
      this.agreementId,
      this.approvalStatusCd,
      this.comments,
      this.confirmedDate,
      this.creationDate,
      this.duration,
      this.endDate,
      this.endDateDuration,
      this.endDateTime,
      this.startDate,
      this.startDateDuration,
      this.startDateTime,
      this.startTime,
      this.submittedDate,
      this.userMode,
      this.personNumber,
      this.absenceType,
      this.employer,
      this.absenceReason,
      this.absenceDispStatus,
      this.formattedDuration,
      this.absenceDispStatusMeaning,
      this.absenceUpdatableFlag,
      this.approvalDatetime});

  MyLeavesModel.fromJson(Map<String, dynamic> json) {
    absenceCaseId = json['absenceCaseId'];
    absenceEntryBasicFlag = json['absenceEntryBasicFlag'];
    absencePatternCd = json['absencePatternCd'];
    absenceStatusCd = json['absenceStatusCd'];
    absenceTypeId = json['absenceTypeId'];
    absenceTypeReasonId = json['absenceTypeReasonId'];
    agreementId = json['agreementId'];
    approvalStatusCd = json['approvalStatusCd'];
    comments = json['comments'];
    confirmedDate = json['confirmedDate'];
    creationDate = json['creationDate'];
    duration = json['duration'];
    endDate = json['endDate'];
    endDateDuration = json['endDateDuration'];
    endDateTime = json['endDateTime'];
    startDate = json['startDate'];
    startDateDuration = json['startDateDuration'];
    startDateTime = json['startDateTime'];
    startTime = json['startTime'];
    submittedDate = json['submittedDate'];
    userMode = json['userMode'];
    personNumber = json['personNumber'];
    absenceType = json['absenceType'];
    employer = json['employer'];
    absenceReason = json['absenceReason'];
    absenceDispStatus = json['absenceDispStatus'];
    formattedDuration = json['formattedDuration'];
    absenceDispStatusMeaning = json['absenceDispStatusMeaning'];
    absenceUpdatableFlag = json['absenceUpdatableFlag'];
    approvalDatetime = json['ApprovalDatetime'];
  }
}
