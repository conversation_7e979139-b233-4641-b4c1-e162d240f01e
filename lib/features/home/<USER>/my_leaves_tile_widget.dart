import 'package:addc/features/home/<USER>/models/my_leaves_model.dart';
import 'package:addc/features/home/<USER>/leave_status_widget.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import '../../../shared/constants/text_styles.dart';

class MyLeavesTileWidget extends StatelessWidget {
  final MyLeavesModel item;
  const MyLeavesTileWidget({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String startDate = item.startDate ?? '';
    String endDate = item.endDate ?? '';
    String type = item.absenceType ?? '';
    String status = (item.approvalStatusCd ?? '').capitalize();

    switch (status.toLowerCase()) {
      case 'awaiting':
        break;
      case 'approved':
        break;
      case 'denied':
        break;
    }
    startDate = DateFormatter.formatStringDate(
        date: startDate,
        inputFormat: 'yyyy-MM-dd',
        outputFormat: 'MMM dd, yyyy');
    if (endDate.isNotEmpty) {
      endDate =
          '$startDate - ${DateFormatter.formatStringDate(date: endDate, inputFormat: 'yyyy-MM-dd', outputFormat: 'MMM dd, yyyy')}';
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(endDate,
                  style: ts14c000940w4, overflow: TextOverflow.ellipsis),
              // H(10),
              Text(type, style: ts12c9D9D9Dw4),
            ],
          ),
        ),
        LeaveStatusWidget(status: status)
      ],
    );
  }
}
