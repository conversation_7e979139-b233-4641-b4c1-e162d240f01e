import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../shared/constants/color_constants.dart';
import '../../../shared/constants/text_styles.dart';

class LeaveStatusWidget extends StatelessWidget {
  final String status;

  const LeaveStatusWidget({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    Color color = ColorConstants.colorFFBC47;
    switch (status.toLowerCase()) {
      // case 'pending':
      //   break;
      case 'approved':
        color = ColorConstants.color0C902F;
        break;
      case 'declined':
        color = ColorConstants.colorFF453F;
        break;
    }
    return Container(
      width: 80.w,
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 5.5),
      alignment: Alignment.center,
      decoration: BoxDecoration(
          color: color, borderRadius: BorderRadius.circular(20.r)),
      child: Text(status,
          style: ts12cFFFFFFw4, overflow: TextOverflow.ellipsis, maxLines: 1),
    );
  }
}
