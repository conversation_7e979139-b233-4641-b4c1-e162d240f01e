import 'package:addc/features/home/<USER>/models/comment_model.dart';
import 'package:addc/features/home/<USER>/comment_tile_widget.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_divider.dart';
import 'package:addc/widgets/common_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CommentsBottomSheet extends StatelessWidget {
  const CommentsBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final List<CommentModel> items = [
      CommentModel(
          image: 'khaled_bin_ali',
          name: '<PERSON><PERSON><PERSON> <PERSON>',
          designation: 'Marketing Lead',
          comment: 'Happy Birthday!!',
          time: 'Just Now'),
      CommentModel(
          image: 'fatimah_bint_ahmed',
          name: '<PERSON><PERSON><PERSON> bint <PERSON>',
          designation: 'Sr S/S Emergency Engineer',
          comment: 'Wishing you a happiest birthday',
          time: '15 min ago'),
    ];
    return Column(
      children: [
        H(24.9),
        Text('Comments', style: ts20c000940w4h1),
        H(35),
        ListView.separated(
          shrinkWrap: true,
          padding: EdgeInsets.only(bottom: 100.h),
          itemCount: items.length,
          itemBuilder: (context, index) {
            return CommentTileWidget(item: items[index]);
          },
          separatorBuilder: (context, index) => CommonDivider(
              height: 40, thickness: 0.5, color: ColorConstants.colorD9D9D9),
        ),
        H(10),
        CommonTextFormField(
          fillColor: ColorConstants.colorF6F6F6,
          hintText: 'Enter your comment ...',
          contentPadding:
              EdgeInsets.symmetric(horizontal: 18.w, vertical: 14.h),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              color: ColorConstants.colorF6F6F6,
              width: 0.5,
            ),
            borderRadius: BorderRadius.circular(30.r),
          ),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                color: ColorConstants.colorE7E7E7,
                width: 1,
                height: 34.h,
              ),
              IconButton(
                onPressed: () {
                  Navigator.pop(context);
                  FocusManager.instance.primaryFocus?.unfocus();
                },
                icon: Padding(
                  padding: EdgeInsets.only(right: 10.w, left: 3),
                  child: SvgPicture.asset('send'.asIconSvg()),
                ),
              ),
            ],
          ),
        ),
        H(25),
      ],
    );
  }
}
