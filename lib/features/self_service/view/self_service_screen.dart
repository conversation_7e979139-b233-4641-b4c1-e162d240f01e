import 'dart:io';
import 'package:addc/features/self_service/models/self_service_card_model.dart';
import 'package:addc/features/self_service/view/widgets/self_service_card_widget.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SelfServiceScreen extends StatelessWidget {
  const SelfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final items = [
      SelfServiceCardModel(title: 'Leaves', icon: 'message-white'),
      SelfServiceCardModel(title: 'Document Center', icon: 'document-normal'),
      SelfServiceCardModel(title: 'Business Card', icon: 'personal_card'),
      SelfServiceCardModel(title: 'Inbox', icon: 'message-white'),
    ];
    double bottom = MediaQuery.paddingOf(context).bottom;
    if (Platform.isAndroid) {
      bottom -= 20.5.h;
    }
    return Padding(
      padding: EdgeInsets.only(left: 12.w, right: 12.w, bottom: bottom + 31.h),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          H(20),
          Expanded(
            child: Stack(
              // fit: StackFit.passthrough,
              alignment: Alignment.bottomCenter,
              children: items.map(
                (e) {
                  int index = items.indexOf(e);
                  return SelfServiceCardWidget(index: index, item: e);
                },
              ).toList(),
            ),
          )
        ],
      ),
    );
  }
}
