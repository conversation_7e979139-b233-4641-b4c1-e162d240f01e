import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/find%20people/models/find_people_model.dart';
import 'package:addc/features/inbox/views/inbox_screen.dart';
import 'package:addc/features/leaves/view/leaves_screen.dart';
import 'package:addc/features/my_business_card/view/my_business_card_screen.dart';
import 'package:addc/features/self_service/models/self_service_card_model.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';

class SelfServiceCardWidget extends StatelessWidget {
  final int index;
  final SelfServiceCardModel item;
  const SelfServiceCardWidget(
      {super.key, required this.index, required this.item});

  @override
  Widget build(BuildContext context) {
    List<Color> colors = [
      ColorConstants.color7AD9A1,
      ColorConstants.color61B985
    ];

    colors = [ColorConstants.color091976, ColorConstants.color000940];
    double height = 535.h;
    switch (index) {
      case 1:
        colors = [ColorConstants.color8E62FA, ColorConstants.color6B41D1];
        height = 408.h;
        break;
      case 2:
        colors = [ColorConstants.color0AB3A1, ColorConstants.color1ACDBA];
        height = 281.h;
        break;
      case 3:
        colors = [ColorConstants.color7AD9A1, ColorConstants.color61B985];
        height = 154.h;
        break;
    }
    return InkWell(
      onTap: () => _onTap(index: index, context: context),
      child: Container(
        height: height,
        width: double.infinity,
        padding: EdgeInsets.only(top: 15.h, left: 16.w, right: 12.w),
        decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: colors,
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: [0.1, 0.5],
            ),
            // color: color,
            borderRadius: BorderRadius.circular(30)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: 42.h,
                  width: 42.h,
                  padding: const EdgeInsets.all(11),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: ColorConstants.colorFFFFFF.withValues(alpha: 0.1),
                  ),
                  child: SvgPicture.asset(item.icon.asIconSvg()),
                ),
                SvgPicture.asset('arrow-share'.asIconSvg()),
              ],
            ),
            H(14),
            Text(item.title, style: ts20cF6F6F6w4),
            const Spacer(),
          ],
        ),
      ),
    );
  }

  _onTap({required int index, required BuildContext context}) async {
    switch (index) {
      case 0:
        Navigator.pushNamed(context, LeavesScreen.route);
        break;
      case 2:
        Navigator.pushNamed(context, MyBusinessCardScreen.route,
            arguments: MyBusinessCardScreen(
                item: FindPeopleModel(
                    displayName: LoginedUser.displayName,
                    workMail: LoginedUser.mail,
                    assignment: LoginedUser.jobTitle,
                    workPhoneNumber: LoginedUser.mobilePhone)));
        break;
      case 3:
        Navigator.pushNamed(context, InboxScreen.route,
            arguments: InboxScreen(isShowMasterAppBar: false));
        break;
    }
  }
}
