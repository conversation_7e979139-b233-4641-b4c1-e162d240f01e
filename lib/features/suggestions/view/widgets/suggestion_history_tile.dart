import 'package:addc/features/suggestions/models/suggestion_history_model.dart';
import 'package:flutter/cupertino.dart';

import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';

class SuggestionHistoryTile extends StatelessWidget {
  final SuggestionHistoryModel item;
  const SuggestionHistoryTile({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String descriptions = item.descriptions ?? '';
    String time = item.time ?? '';
    String title = item.title ?? '';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        H(10),
        Text(title, style: ts16c000940w4h1),
        H(7.5),
        Text(descriptions, style: ts15c9D9D9Dw4h1),
        H(6),
        Align(
            alignment: Alignment.bottomRight,
            child: Text(time, style: ts12c9D9D9Dw4h1)),
        H(10),
      ],
    );
  }
}
