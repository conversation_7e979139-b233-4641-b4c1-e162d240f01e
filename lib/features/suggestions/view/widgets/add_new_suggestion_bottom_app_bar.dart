import 'package:addc/features/complaints/view/common_success_screen.dart';
import 'package:addc/features/suggestions/view/suggestions_screen.dart';
import 'package:addc/widgets/common_bottom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../shared/constants/color_constants.dart';
import '../../../widgets/gap.dart';

class AddNewSuggestionBottomAppBar extends StatelessWidget {
  const AddNewSuggestionBottomAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.color8B8D97),
                    onPressed: () => _onSaveADraftPressed(context: context),
                    child: Text('Save as Draft')),
              ),
              W(15),
              Expanded(
                child: ElevatedButton(
                    onPressed: () => _onSubmitPressed(context: context),
                    child: Text('Submit')),
              )
            ],
          ),
        ),
        H(8),
        Row(
          children: [
            Expanded(child: const CommonBottomAppbar()),
          ],
        ),
      ],
    );
  }

  _onSaveADraftPressed({required BuildContext context}) {
    Navigator.pushNamed(context, CommonSuccessScreen.route,
        arguments: CommonSuccessScreen(
            description:
                'Your suggestion has been saved as a draft.\nYou can revisit and edit it anytime from your\ndrafts section',
            title: 'Successfully\nSaved  as draft',
            imageInSVG: 'draft_icon',
            onDonePressed: () => Navigator.popUntil(
                context, ModalRoute.withName(SuggestionsScreen.route))));
  }

  _onSubmitPressed({required BuildContext context}) {
    Navigator.pushNamed(context, CommonSuccessScreen.route,
        arguments: CommonSuccessScreen(
            description: 'Your input has been successfully submitted',
            title: 'Submitted Successfully',
            onDonePressed: () => Navigator.popUntil(
                context, ModalRoute.withName(SuggestionsScreen.route))));
  }
}
