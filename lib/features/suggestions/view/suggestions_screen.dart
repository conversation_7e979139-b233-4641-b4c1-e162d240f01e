import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_bottom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/suggestions_provider.dart';
import 'sections/suggestion_draft_tab_view.dart';
import 'sections/suggestion_history_tab_view.dart';
import 'sections/suggestion_tab_bar_section.dart';

class SuggestionsScreen extends StatefulWidget {
  static const route = '/suggestions_screen';
  const SuggestionsScreen({super.key});

  @override
  State<SuggestionsScreen> createState() => _SuggestionsScreenState();
}

class _SuggestionsScreenState extends State<SuggestionsScreen> {
  late PageController _pageController;
  late SuggestionsProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<SuggestionsProvider>();
    _pageController = PageController(initialPage: 0, keepPage: false);
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        _provider.selectedTabBarIndex = 0;
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: const Text('Suggestion'), leading: const CommonBackButton()),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SuggestionTabBarSection(pageController: _pageController),
          Expanded(
            child: Consumer<SuggestionsProvider>(
              builder: (context, provider, _) {
                return PageView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: provider.suggestionTabBars.length,
                  controller: _pageController,
                  itemBuilder: (context, index) {
                    switch (provider.selectedTabBarIndex) {
                      case 0:
                        return const SuggestionHistoryTabView();
                      case 1:
                        return const SuggestionDraftTabView();
                      // case 2:
                      //   return const FeedsAnnouncementTabView();
                    }
                    return const SizedBox();
                  },
                  onPageChanged: (index) =>
                      provider.onSuggestionTabBarChanaged(index: index),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: const CommonBottomAppbar(),
    );
  }
}
