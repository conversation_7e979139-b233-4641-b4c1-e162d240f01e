import 'package:addc/features/suggestions/view/widgets/add_new_suggestion_bottom_app_bar.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../widgets/standard_text_form_field.dart';

class AddNewSuggestionScreen extends StatelessWidget {
  static const route = '/add_new_uggestion_screen';
  const AddNewSuggestionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Suggestion'),
        leading: const CommonBackButton(),
      ),
      body: Column(
        children: [
          Expanded(
            child: Column(
              children: [
                H(25),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 12.w),
                  padding: EdgeInsets.only(
                      top: 30.h, left: 12.w, right: 12.w, bottom: 15.h),
                  decoration: BoxDecoration(
                      color: ColorConstants.colorFFFFFF,
                      borderRadius: BorderRadius.circular(18.r)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Subject', style: ts16c000940w4h1),
                      H(15),
                      StandartTextFormField(hintText: 'Enter subject'),
                      H(25),
                      Text('Message', style: ts16c000940w4h1),
                      H(15),
                      StandartTextFormField(
                        hintText: 'Message here...',
                        maxLines: 6,
                        borderRaduis: 20,
                      ),
                    ],
                  ),
                ),
                // const Spacer(),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: const AddNewSuggestionBottomAppBar(),
    );
  }
}
