import 'package:addc/features/suggestions/models/suggestion_history_model.dart';
import 'package:addc/features/suggestions/view/add_new_suggestion_screen.dart';
import 'package:addc/features/suggestions/view/widgets/suggestion_history_tile.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SuggestionHistoryTabView extends StatelessWidget {
  const SuggestionHistoryTabView({super.key});

  @override
  Widget build(BuildContext context) {
    final List<SuggestionHistoryModel> items = [
      SuggestionHistoryModel(
          title: 'Training and Development',
          descriptions:
              'Create a learning hub to access courses, track certifications, and set training goals',
          time: '12 Dec 2024'),
      SuggestionHistoryModel(
          title: 'Training and Development',
          descriptions:
              'Create a learning hub to access courses, track certifications, and set training goals',
          time: '12 Dec 2024'),
      SuggestionHistoryModel(
          title: 'Training and Development',
          descriptions:
              'Create a learning hub to access courses, track certifications, and set training goals',
          time: '12 Dec 2024'),
      SuggestionHistoryModel(
          title: 'Leave and Attendance Management',
          descriptions:
              'Allow employees to swap shifts or trade holidays with peers after manager approval',
          time: '05 Dec 2024'),
      SuggestionHistoryModel(
          title: 'Goal Setting and Progress',
          descriptions:
              'Allow employees to set personal goals and track progress alongside manager feedback.',
          time: '22 Nov 2024'),
      SuggestionHistoryModel(
          title: 'Mentorship Pairing',
          descriptions:
              'Introduce a mentorship program feature to connect employees with mentors in the company',
          time: '12 Nov 2024'),
      SuggestionHistoryModel(
          title: 'Mentorship Pairing',
          descriptions:
              'Introduce a mentorship program feature to connect employees with mentors in the company',
          time: '12 Nov 2024'),
      SuggestionHistoryModel(
          title: 'Mentorship Pairing',
          descriptions:
              'Introduce a mentorship program feature to connect employees with mentors in the company',
          time: '12 Nov 2024'),
      SuggestionHistoryModel(
          title: 'Mentorship Pairing',
          descriptions:
              'Introduce a mentorship program feature to connect employees with mentors in the company',
          time: '12 Nov 2024'),
      SuggestionHistoryModel(
          title: 'Mentorship Pairing',
          descriptions:
              'Introduce a mentorship program feature to connect employees with mentors in the company',
          time: '12 Nov 2024'),
    ];
    return Column(
      children: [
        H(20),
        Expanded(
          child: Column(
            children: [
              Flexible(
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 12.w),
                  padding: EdgeInsets.symmetric(vertical: 15.h),
                  decoration: BoxDecoration(
                      color: ColorConstants.colorFFFFFF,
                      borderRadius: BorderRadius.circular(18.r)),
                  child: ListView.separated(
                    shrinkWrap: true,
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    itemCount: items.length,
                    itemBuilder: (context, index) {
                      return SuggestionHistoryTile(item: items[index]);
                    },
                    separatorBuilder: (context, index) => Container(
                        height: 0.5,
                        color:
                            ColorConstants.colorD9D9D9.withValues(alpha: 0.5)),
                  ),
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8),
          child: ElevatedButton(
              onPressed: () =>
                  Navigator.pushNamed(context, AddNewSuggestionScreen.route),
              child: Text('Add New')),
        )
      ],
    );
  }
}
