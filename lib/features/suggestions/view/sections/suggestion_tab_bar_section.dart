import 'package:addc/features/suggestions/providers/suggestions_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../../widgets/common_tab_bar_tile.dart';
import '../../../widgets/gap.dart';

class SuggestionTabBarSection extends StatelessWidget {
  final PageController pageController;
  const SuggestionTabBarSection({super.key, required this.pageController});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<SuggestionsProvider>();
    return Column(
      children: [
        H(25),
        SizedBox(
          height: 36.h,
          child: ListView.separated(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            itemCount: provider.suggestionTabBars.length,
            itemBuilder: (context, index) {
              return Consumer<SuggestionsProvider>(
                  builder: (context, provider, _) {
                bool isSelected = provider.selectedTabBarIndex == index;

                return CommonTabBarTile(
                  title: provider.suggestionTabBars[index],
                  isSelected: isSelected,
                  onTap: () {
                    provider.onSuggestionTabBarChanaged(index: index);
                    pageController.animateToPage(index,
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.easeIn);
                  },
                );
              });
            },
            separatorBuilder: (context, index) => W(15),
          ),
        ),
      ],
    );
  }
}
