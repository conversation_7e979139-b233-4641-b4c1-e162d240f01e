import 'package:addc/features/find%20people/models/find_people_model.dart';
import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:addc/features/utils/extensions.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';
import '../../providers/business_provider.dart';

class BusinessCardRearView extends StatelessWidget {
  final FindPeopleModel item;

  const BusinessCardRearView({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<BusinessProvider>();
    String displayName = '';
    String email = '----@----';
    String designation = 'Designation';
    String mobilePhone = '';
    if (item.displayName != null) {
      displayName = item.displayName!;
    }
    if (item.workMail != null) {
      email = item.workMail!;
    }
    if (item.assignment != null) {
      designation = item.assignment!;
    }
    if (item.workPhoneNumber != null) {
      mobilePhone = item.workPhoneNumber!;
    }
    String _vCardData = 'BEGIN:VCARD\n'
        'VERSION:3.0\n'
        'FN:$displayName\n'
        'TEL;TYPE=WORK,VOICE:$mobilePhone\n'
        'EMAIL:$email\n'
        'TITLE:$designation\n'
        'END:VCARD';
    return InkWell(
      onTap: () =>
          provider.isCardFrontViewEnabled = !provider.isCardFrontViewEnabled,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: Stack(
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: 300.h,
                width: double.infinity,
                alignment: Alignment.bottomCenter,
                padding: EdgeInsets.only(bottom: 15.h),
                margin: EdgeInsets.symmetric(horizontal: 4.w),
                decoration: BoxDecoration(
                    color: ColorConstants.colorE9ECFF,
                    borderRadius: BorderRadius.circular(20.r)),
                child: Row(
                  spacing: 4.5,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'personal_card'.asIconPng(),
                      height: 16.h,
                      width: 16.w,
                    ),
                    Text('Tap to view business card', style: ts12c6B73A5w4),
                  ],
                ),
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Align(
                alignment: Alignment.topCenter,
                child: SizedBox(
                  height: 252.h,
                  width: double.infinity,
                  child: Stack(
                    children: [
                      SizedBox(
                        height: 252.h,
                        width: double.infinity,
                        // padding: EdgeInsets.only(top: 15.89.h, bottom: 0),
                        child: Container(
                          height: 252.h,
                          width: double.infinity,
                          padding: EdgeInsets.only(
                              top: 20.h,
                              bottom: 12.44.h,
                              left: 10.w,
                              right: 10.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(18.r),
                            gradient: RadialGradient(
                              center: Alignment.bottomLeft,
                              focalRadius: 0,
                              radius: 1.4,
                              focal: Alignment.bottomLeft,
                              colors: [
                                ColorConstants.color071986
                                    .withValues(alpha: 0.7),
                                ColorConstants.color071986,
                                ColorConstants.color000940,
                              ],
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Align(
                                alignment: Alignment.bottomCenter,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    SizedBox(
                                      height: 130.h,
                                      width: 130.w,
                                      child: Container(
                                        padding: EdgeInsets.all(9),
                                        // decoration: BoxDecoration(
                                        //     color: ColorConstants.colorFFFFFF.withValues(alpha: 0.1),
                                        //     shape: BoxShape.circle,
                                        //     border: Border.all(
                                        //         color: ColorConstants.colorFFFFFF, width: 0.5)),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12.r),
                                          child: BackdropFilter(
                                            filter: ImageFilter.blur(
                                                sigmaX: 5.0,
                                                sigmaY: 5.0,
                                                tileMode: TileMode.repeated),
                                            child: Container(
                                              color: ColorConstants.colorFFFFFF
                                                  .withValues(alpha: 0.1),
                                              child: QrImageView(
                                                data: _vCardData,
                                                version: QrVersions.auto,
                                                size: 300,
                                                gapless: false,
                                                dataModuleStyle:
                                                    QrDataModuleStyle(
                                                        dataModuleShape:
                                                            QrDataModuleShape
                                                                .circle,
                                                        color: Colors.white),
                                                eyeStyle: QrEyeStyle(
                                                    eyeShape: QrEyeShape.square,
                                                    color: Colors.white),
                                              ),
                                            ),
                                            // child: Container(
                                            //   padding: EdgeInsets.symmetric(
                                            //       vertical: 15.h),
                                            //   color: ColorConstants.colorFFFFFF
                                            //       .withValues(alpha: 0.1),
                                            //   child: QrImageView(
                                            //     data:
                                            //         'This is a simple QR code',
                                            //     version: QrVersions.auto,
                                            //     size: 300,
                                            //     gapless: true,
                                            //   ),
                                            // ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Text(
                                displayName,
                                style: ts18cFFFFFFw4h1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(designation, style: ts13c9D9D9Dw4h1),
                              H(25.4),
                              Container(
                                height: 0.5.h,
                                width: double.infinity,
                                color: ColorConstants.colorFFFFFF,
                              ),
                              H(6),
                              Text(
                                  'Scan this code from another phone to view business card',
                                  style: ts10c0AB3A1w4h1),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
