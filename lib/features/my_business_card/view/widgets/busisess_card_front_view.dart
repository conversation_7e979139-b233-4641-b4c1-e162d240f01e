import 'dart:ui';
import 'package:addc/features/find%20people/models/find_people_model.dart';
import 'package:addc/features/my_business_card/providers/business_provider.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';

class BusisessCardFrontView extends StatelessWidget {
  final FindPeopleModel item;

  const BusisessCardFrontView({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<BusinessProvider>();
    String displayName = '';
    String email = '----@----';
    String designation = '';
    String mobilePhone = '';
    if (item.displayName != null) {
      displayName = item.displayName ?? '';
    }
    if (item.workMail != null) {
      email = item.workMail ?? '';
    }
    if (item.assignment != null) {
      designation = item.assignment ?? '';
    }
    if (item.workPhoneNumber != null) {
      mobilePhone = item.workPhoneNumber ?? '';
    }
    return InkWell(
      onTap: () =>
          provider.isCardFrontViewEnabled = !provider.isCardFrontViewEnabled,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: Stack(
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: 300.h,
                width: double.infinity,
                alignment: Alignment.bottomCenter,
                padding: EdgeInsets.only(bottom: 15.h),
                margin: EdgeInsets.symmetric(horizontal: 4.w),
                decoration: BoxDecoration(
                    color: ColorConstants.colorE9ECFF,
                    borderRadius: BorderRadius.circular(20.r)),
                child: Row(
                  spacing: 4.5,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset('qr_code'.asIconSvg()),
                    Text('Tap to view QR code', style: ts12c6B73A5w4),
                  ],
                ),
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Align(
                alignment: Alignment.topCenter,
                child: SizedBox(
                  height: 252.h,
                  width: double.infinity,
                  child: Stack(
                    children: [
                      SizedBox(
                        height: 252.h,
                        width: double.infinity,
                        // padding: EdgeInsets.only(top: 15.89.h, bottom: 0),
                        child: Container(
                          height: 252.h,
                          width: double.infinity,
                          // padding: EdgeInsets.only(
                          //     top: 15.89.h, bottom: 0, left: 10.w, right: 0.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(18.r),
                            gradient: RadialGradient(
                              center: Alignment.bottomLeft,
                              focalRadius: 0,
                              radius: 1.4,
                              focal: Alignment.bottomLeft,
                              colors: [
                                ColorConstants.color071986
                                    .withValues(alpha: 0.7),
                                ColorConstants.color071986,
                                ColorConstants.color000940,
                              ],
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                width: double.infinity,
                                alignment: Alignment.topRight,
                                padding:
                                    EdgeInsets.only(right: 15.w, top: 15.h),
                                child: SvgPicture.asset(
                                  'splash_logo'.asIconSvg(),
                                  width: 107.52.w,
                                  height: 49.55.h,
                                ),
                              ),
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Padding(
                                  padding: EdgeInsets.only(left: 15.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        displayName,
                                        style: ts18cFFFFFFw4h1,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      Text(
                                        designation,
                                        style: ts13c9D9D9Dw4h1,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Align(
                                alignment: Alignment.bottomRight,
                                child: Image.asset(
                                  'punch_in_card_half_circle'.asImagePng(),
                                  scale: 4,
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            SizedBox(
                              // height: 75.h,
                              width: double.infinity,
                              child: Container(
                                padding: EdgeInsets.only(
                                    left: 15.w,
                                    right: 15.w,
                                    top: 0.h,
                                    bottom: 15.h),
                                // decoration: BoxDecoration(
                                //     color: ColorConstants.colorFFFFFF.withValues(alpha: 0.1),
                                //     shape: BoxShape.circle,
                                //     border: Border.all(
                                //         color: ColorConstants.colorFFFFFF, width: 0.5)),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12.r),
                                  child: BackdropFilter(
                                    filter: ImageFilter.blur(
                                        sigmaX: 5.0,
                                        sigmaY: 5.0,
                                        tileMode: TileMode.repeated),
                                    child: Container(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 20.h),
                                      color: ColorConstants.colorFFFFFF
                                          .withValues(alpha: 0.1),
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                            left: 12.w, right: 16.w),
                                        child: Column(
                                          children: [
                                            Row(
                                              children: [
                                                Container(
                                                  height: 30.h,
                                                  width: 30.h,
                                                  padding: EdgeInsets.all(7),
                                                  decoration: BoxDecoration(
                                                      border: Border.all(
                                                          color: ColorConstants
                                                              .colorFFFFFF
                                                              .withValues(
                                                                  alpha: 0.4),
                                                          width: 0.5),
                                                      shape: BoxShape.circle,
                                                      color: ColorConstants
                                                          .colorFFFFFF
                                                          .withValues(
                                                              alpha: 0.1)),
                                                  child: SvgPicture.asset(
                                                      'sms'.asIconSvg()),
                                                ),
                                                W(9.35.w),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      // Text('Email Address',
                                                      //     style: ts10cB6B6B6w4),
                                                      Text(
                                                        email,
                                                        style: ts11cFFFFFFw4,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        maxLines: 3,
                                                      ),
                                                    ],
                                                  ),
                                                )
                                              ],
                                            ),
                                            H(10.h),
                                            Row(
                                              children: [
                                                Container(
                                                  height: 30.h,
                                                  width: 30.h,
                                                  padding: EdgeInsets.all(7),
                                                  decoration: BoxDecoration(
                                                      border: Border.all(
                                                          color: ColorConstants
                                                              .colorFFFFFF
                                                              .withValues(
                                                                  alpha: 0.4),
                                                          width: 0.5),
                                                      shape: BoxShape.circle,
                                                      color: ColorConstants
                                                          .colorFFFFFF
                                                          .withValues(
                                                              alpha: 0.1)),
                                                  child: SvgPicture.asset(
                                                      'call'.asIconSvg()),
                                                ),
                                                W(9.35.w),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    // Text('Mobile No',
                                                    //     style: ts10cB6B6B6w4),
                                                    Text(mobilePhone,
                                                        style: ts11cFFFFFFw4),
                                                  ],
                                                )
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
