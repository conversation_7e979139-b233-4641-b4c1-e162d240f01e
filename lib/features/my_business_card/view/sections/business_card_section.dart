import 'dart:math';

import 'package:addc/features/find%20people/models/find_people_model.dart';
import 'package:addc/features/my_business_card/providers/business_provider.dart';
import 'package:addc/features/my_business_card/view/widgets/busisess_card_front_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../widgets/gap.dart';
import '../widgets/business_card_rear_view.dart';

class BusinessCardSection extends StatelessWidget {
  final FindPeopleModel item;

  const BusinessCardSection({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        H(25.h),
        // const BusisessCardFrontView(),
        Consumer<BusinessProvider>(
          builder: (context, provider, _) {
            return AnimatedSwitcher(
              duration: Duration(milliseconds: 800),
              transitionBuilder: __transitionBuilder,
              layoutBuilder: (widget, list) =>
                  Stack(children: [widget!, ...list]),
              switchInCurve: Curves.easeInBack,
              switchOutCurve: Curves.easeInBack.flipped,
              child: provider.isCardFrontViewEnabled
                  ? BusisessCardFrontView(item: item, key: ValueKey(true))
                  : BusinessCardRearView(item: item, key: ValueKey(false)),
            );
            // if (provider.isCardFrontViewEnabled) {
            //   return const BusisessCardFrontView();
            // }
            // return const BusinessCardRearView();
          },
        ),
      ],
    );
  }

  Widget __transitionBuilder(Widget widget, Animation<double> animation) {
    final rotateAnim = Tween(begin: pi, end: 0.0).animate(animation);
    return Consumer<BusinessProvider>(
      builder: (context, provider, _) {
        return AnimatedBuilder(
          animation: rotateAnim,
          child: widget,
          builder: (context, widget) {
            final isUnder =
                (ValueKey(provider.isCardFrontViewEnabled) != widget!.key);
            var tilt = ((animation.value - 0.5).abs() - 0.5) * 0.003;
            tilt *= isUnder ? -1.0 : 1.0;
            final value =
                isUnder ? min(rotateAnim.value, pi / 2) : rotateAnim.value;
            return Transform(
              transform: (Matrix4.rotationY(value)..setEntry(3, 0, tilt)),
              // : (Matrix4.rotationX(value)..setEntry(3, 1, tilt)),
              alignment: Alignment.center,
              child: widget,
            );
          },
        );
      },
    );
  }

  // Widget __buildLayout(
  //     {required Key key,
  //     required Widget child,
  //     required String faceName,
  //     required Color backgroundColor}) {
  //   return Container(
  //     key: key,
  //     decoration: BoxDecoration(
  //       shape: BoxShape.rectangle,
  //       borderRadius: BorderRadius.circular(20.0),
  //       color: backgroundColor,
  //     ),
  //     child: Center(
  //       child: Text(faceName.substring(0, 1), style: TextStyle(fontSize: 80.0)),
  //     ),
  //   );
  // }
}
