import 'dart:io';

import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/find%20people/models/find_people_model.dart';
import 'package:addc/features/my_business_card/view/sections/business_card_section.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_bottom_appbar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:vcard_vcf/vcard.dart';

class MyBusinessCardScreen extends StatelessWidget {
  static const route = '/my_business_card_screen';

  final FindPeopleModel item;
  const MyBusinessCardScreen({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: Text(item.workMail == LoginedUser.mail
              ? 'My Business Card'
              : 'Business Card'),
          leading: const CommonBackButton()),
      body: Column(
        children: [
          // if (isTestVersion) ...[
          //   H(20),
          //   Text(item.workMail == LoginedUser.mail
          //       ? '(Graph API)'
          //       : ' (OF-emps)')
          // ],
          H(20),
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 12.w),
              decoration: BoxDecoration(
                  color: ColorConstants.colorFFFFFF,
                  borderRadius: BorderRadius.all(Radius.circular(18.r))),
              child: Column(
                children: [
                  BusinessCardSection(item: item),
                  const Spacer(),
                  Padding(
                    padding: EdgeInsets.only(bottom: 30.0, left: 8, right: 8),
                    child: ElevatedButton(
                      onPressed: shareVCard,
                      child: Text('Share My Card'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(bottom: Platform.isAndroid ? 30 : 0),
        child: const CommonBottomAppbar(),
      ),
    );
  }

  // Helper function for compute
  static Future<String> _writeVCardToFile(Map<String, dynamic> args) async {
    final vCardString = args['vCardString'] as String;
    final filePath = args['filePath'] as String;
    final file = File(filePath);
    await file.writeAsString(vCardString);
    return filePath;
  }

  void shareVCard() async {
    EasyLoading.show();
    String displayName = item.displayName ?? '';
    String email = item.workMail ?? '';
    String designation = item.assignment ?? '';
    String mobilePhone =
        '${item.workPhoneCountryCode ?? ''} ${item.workPhoneNumber ?? ''}';

    String firstName = '';
    String lastName = '';
    if (displayName.isNotEmpty) {
      final names = displayName.split(' ');
      if (names.isNotEmpty) {
        firstName = names.first;
        lastName = names.last;
      }
    }

    // 1. Create a vCard
    var vCard = VCard();
    vCard.firstName = firstName;
    vCard.lastName = lastName;
    vCard.organization = designation;
    // vCard.photo.attachFromUrl('https://www.example.com/profile.png', 'PNG');
    vCard.workPhone = mobilePhone;
    vCard.email = email;

    try {
      // 2. Get the temporary directory
      final directory = await getTemporaryDirectory();
      final filePath = '${directory.path}/contact.vcf';
      final vCardString = vCard.getFormattedString();
      // 3. Write the vCard to a file in a background isolate
      final writtenFilePath = await compute(_writeVCardToFile,
          {'vCardString': vCardString, 'filePath': filePath});
      EasyLoading.dismiss();
      // 4. Share the file (after dismissing loading, in a microtask)
      Future.microtask(() async {
        final result = await SharePlus.instance
            .share(ShareParams(files: [XFile(writtenFilePath)]));
        if (result.status == ShareResultStatus.success) {
          if (kDebugMode) {
            print('Thank you for sharing my vCard!');
          }
        }
      });
    } catch (e) {
      debugPrint(e.toString());
      EasyLoading.dismiss();
    }
  }
}
