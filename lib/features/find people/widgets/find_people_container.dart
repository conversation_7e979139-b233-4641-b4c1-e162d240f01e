import 'package:addc/features/find%20people/models/all_people_model.dart';
import 'package:addc/features/find%20people/providers/find_people_provider.dart';
import 'package:addc/features/find%20people/widgets/find_people_tiles.dart';
import 'package:addc/features/inbox/widgets/custom_search_bar.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class FindPeopleContainer extends StatelessWidget {
  final TextEditingController searchController;
  @override
  // Path: view/find_people_screen.dart
  //   late FindPeopleProvider _provider;
  const FindPeopleContainer({super.key, required this.searchController});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<FindPeopleProvider>();
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: ColorConstants.colorFFFFFF,
              borderRadius: BorderRadius.circular(18),
            ),
            child: Padding(
              padding: EdgeInsets.only(top: 14.h, left: 8.w, right: 12.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomSearchBar(
                    controller: searchController,
                    onFieldSubmitted: (val) =>
                        _onSearch(searchKey: val, provider: provider),
                    // onChanged: (val) =>
                    //     _onSearch(searchKey: val, provider: provider),
                  ),
                  H(20),
                  Text("All people", style: ts16c000940w4),
                  H(10),
                  Expanded(
                    child: PagedListView.separated(
                      shrinkWrap: true,
                      pagingController: provider.findPeoplePagingController,
                      builderDelegate: PagedChildBuilderDelegate<AllPeopleModel>(
                          animateTransitions: true,
                          firstPageErrorIndicatorBuilder: (context) => Padding(
                              padding: EdgeInsets.only(
                                  top: MediaQuery.of(context).size.height / 3),
                              child: Text('Something went wrong')),
                          firstPageProgressIndicatorBuilder: (context) =>
                              _skeletonizer(),
                          newPageErrorIndicatorBuilder: (context) => Padding(
                              padding: EdgeInsets.only(
                                  top: MediaQuery.of(context).size.height / 3),
                              child: Text('Something went wrong')),
                          newPageProgressIndicatorBuilder: (context) => const Center(
                              child: CircularProgressIndicator.adaptive()),
                          noItemsFoundIndicatorBuilder: (context) => Padding(
                              padding: EdgeInsets.only(
                                  top: MediaQuery.of(context).size.height / 3),
                              child: Center(
                                  child: Text('There is no data found'))),
                          itemBuilder: (context, item, index) =>
                              FindPeopleTiles(item: item)),
                      separatorBuilder: (context, index) => Container(
                        height: 1,
                        width: double.infinity,
                        margin: EdgeInsets.symmetric(vertical: 8.h),
                        color:
                            ColorConstants.color9D9D9D.withValues(alpha: 0.1),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _skeletonizer() {
    return Skeletonizer(
      enabled: true,
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: 4,
        itemBuilder: (context, index) => FindPeopleTiles(
            item: AllPeopleModel(
          names: <Names>[
            Names.fromJson({
              "DisplayName": "Umer ALSAWAID",
            })
          ],
        )),
        separatorBuilder: (context, index) => Container(
          height: 1,
          width: double.infinity,
          margin: EdgeInsets.symmetric(vertical: 8.h),
          color: ColorConstants.color9D9D9D.withValues(alpha: 0.1),
        ),
      ),
    );
  }

  _onSearch({required String searchKey, required FindPeopleProvider provider}) {
    provider.searchKey = searchKey;
    if (searchKey.isEmpty) {
      provider.searchKey = null;
    }
    provider.findPeopleCurrentPage = 0;
    provider.findPeoplePagingController.refresh();
  }
}
