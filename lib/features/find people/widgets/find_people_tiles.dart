import 'package:addc/features/find%20people/models/all_people_model.dart';
import 'package:addc/features/find%20people/models/find_people_model.dart';
import 'package:addc/features/my_business_card/view/my_business_card_screen.dart';
import 'package:addc/features/profile/view/sections/profile_avatar_section.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FindPeopleTiles extends StatelessWidget {
  final AllPeopleModel item;
  const FindPeopleTiles({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final name = item.names?.firstOrNull?.displayName ?? '';
    String designation = '';
    int? personID = item.personId;

    return InkWell(
      onTap: () async {
        if (personID != null) {
          // final provider = context.read<FindPeopleProvider>();
          // FindPeopleModel? findPeopleitem = await provider.getFindPeopleDetails(
          //     personID: personID.toString());
          // if (findPeopleitem != null) {
          // log('findPeopleitem - ${findPeopleitem.workMail}');
          final data = FindPeopleModel(
            personId: personID,
            displayName: name,
            personNumber: item.personNumber,
            workPhoneCountryCode: item.phones?.firstOrNull?.countryCodeNumber,
            workPhoneAreaCode: '${item.phones?.firstOrNull?.countryCodeNumber}',
            workPhoneNumber:
                '${item.phones?.firstOrNull?.countryCodeNumber != null ? '+${item.phones?.firstOrNull?.countryCodeNumber}' : ''} ${item.phones?.firstOrNull?.phoneNumber ?? ''}',
            workMail: item.emails?.firstOrNull?.emailAddress ?? '',
          );
          // ignore: use_build_context_synchronously
          Navigator.pushNamed(context, MyBusinessCardScreen.route,
              arguments: MyBusinessCardScreen(item: data));
        }
        // }
      },
      child: Container(
        padding: EdgeInsets.all(4),
        child: Row(
          children: [
            // Image.asset(height: 42.h, width: 42.w, 'arab_person'.asImagePng()),
            CustomAvatar(
                borderWidth: 0, fontSize: 14, name: name, radius: (42.h) / 2),
            W(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(name,
                      style: ts14c000940w4,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1),
                  if (designation.isNotEmpty)
                    Text(designation,
                        style: ts12c9D9D9Dw4,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
