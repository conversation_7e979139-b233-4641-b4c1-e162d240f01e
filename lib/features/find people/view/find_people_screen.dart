import 'package:addc/features/find%20people/providers/find_people_provider.dart';
import 'package:addc/features/find%20people/widgets/find_people_container.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class FindPeopleScreen extends StatefulWidget {
  static const route = '/find_people_screen';
  const FindPeopleScreen({super.key});

  @override
  State<FindPeopleScreen> createState() => _FindPeopleScreenState();
}

class _FindPeopleScreenState extends State<FindPeopleScreen> {
  late FindPeopleProvider _provider;
  final TextEditingController _searchController = TextEditingController();
  @override
  void initState() {
    super.initState();
    _provider = context.read<FindPeopleProvider>();

    _provider.findPeopleInitPagination();
    _searchController.addListener(() {
      if (_searchController.text.isEmpty) {
        _provider.searchKey = null;
        _provider.findPeopleCurrentPage = 0;
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _provider.findPeoplePagingController.dispose();
    _searchController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text('Find People', style: ts20c000940w4),
        leading: const CommonBackButton(),
      ),
      body: Padding(
        padding:
            EdgeInsets.only(top: 25.h, left: 12.w, right: 12.w, bottom: 50.h),
        child: FindPeopleContainer(searchController: _searchController),
      ),
      // bottomNavigationBar: const CommonBottomAppbar(),
    );
  }
}
