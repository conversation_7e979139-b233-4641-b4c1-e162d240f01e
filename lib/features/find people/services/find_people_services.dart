import 'dart:convert';
import 'dart:developer';
import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class FindPeopleServices {
  Dio dio = Dio();
  CancelToken? _cancelToken;
  Future<Response> getFindPeopleDetails({required String personID}) async {
    // try {
    if (_cancelToken != null && !_cancelToken!.isCancelled) {
      _cancelToken!.cancel("Cancelled previous request");
    }
    _cancelToken = CancelToken();
    String apiUrl = '${ApiConstants.employeeDetails}?q=PersonId=$personID';
    log('apiUrl -- $apiUrl');
    String encodedCredentials = ApiConstants.encodedCredentials;

    Map<String, String> headers = {
      'Authorization': 'Basic $encodedCredentials',
      'Content-Type': 'application/json'
    };

    Response? response = await dio.get(apiUrl,
        options: Options(headers: headers), cancelToken: _cancelToken);
    if (kDebugMode) {
      log(jsonEncode(response.data), name: 'getFindPeoples');
    }
    return response;
    // } catch (e) {
    //   debugPrint(e.toString());
    // }
  }

  Future<Response> getAndFindPeople(
      {String? searchKey, required int offset}) async {
    // try {
    if (_cancelToken != null && !_cancelToken!.isCancelled) {
      _cancelToken!.cancel("Cancelled previous request");
    }
    _cancelToken = CancelToken();
    StringBuffer urlBuffer = StringBuffer(
        '${ApiConstants.oracleBaseURL}workers?expand=emails,names,phones&offset=$offset&limit=15');
    if (searchKey != null) {
      urlBuffer.write("&q=DisplayName=$searchKey");
    }
    String url = Uri.parse(urlBuffer.toString()).toString();
    log('urlBuffer --> $url');
    Response? response = await dio.get(urlBuffer.toString(),
        options: Options(headers: ApiConstants.authHeader()),
        cancelToken: _cancelToken);

    return response;
  }
}
