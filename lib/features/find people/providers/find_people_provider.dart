import 'dart:developer';
import 'package:addc/features/find%20people/models/all_people_model.dart';
import 'package:flutter/foundation.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:addc/features/find%20people/models/find_people_model.dart';
import 'package:addc/features/find%20people/services/find_people_services.dart';
import 'package:dio/dio.dart';

class FindPeopleProvider extends ChangeNotifier {
  bool isLoading = false;

  bool _isLoadingNotifier = false;
  bool get isLoadingNotifier => _isLoadingNotifier;
  set isLoadingNotifier(bool index) {
    _isLoadingNotifier = index;
    notifyListeners();
  }

  FindPeopleModel? findPeopleDetailModel;
  Future<FindPeopleModel?> getFindPeopleDetails(
      {required String personID}) async {
    try {
      findPeopleDetailModel = null;
      if (isLoadingNotifier) return null;
      // String userID =
      //     '00020000000EACED0005770800005AF311327F200000004AACED00057372000D6A6176612E73716C2E4461746514FA46683F3566970200007872000E6A6176612E7574696C2E44617465686A81014B59741903000078707708000001956DE6D80078';
      isLoadingNotifier = true;
      Response response =
          await FindPeopleServices().getFindPeopleDetails(personID: personID);
      isLoadingNotifier = false;
      if (response.statusCode == 200) {
        Map<String, dynamic> json = response.data;
        if (json.containsKey('items')) {
          List data = json['items'];
          List<FindPeopleModel> tempData = [];
          tempData = data.map((e) => FindPeopleModel.fromJson(e)).toList();
          findPeopleDetailModel = tempData.firstWhere(
              (element) => element.personId.toString() == personID,
              orElse: () => FindPeopleModel());
          log('getFindPeopleDetails - ${findPeopleDetailModel?.personId} - ${findPeopleDetailModel?.personNumber} - ${findPeopleDetailModel?.workMail}');
          return findPeopleDetailModel;
        }
      }
    } on Exception catch (e) {
      isLoadingNotifier = false;
      debugPrint('Error: $e');
    }
    return null;
  }

  // List<FindPeopleModel> findPeople = [];
  // Future<void> getAndFindPeople({String? searchKey}) async {
  //   try {
  //     // if (isLoading) return;
  //     isLoadingNotifier = true;
  //     final response = await FindPeopleServices()
  //         .getAndFindPeople(searchKey: searchKey, page: 0);
  //     log(jsonEncode(response.data), name: 'getAndFindPeople');
  //     isLoadingNotifier = false;
  //     if (response.statusCode == 200) {
  //       Map<String, dynamic> json = response.data;
  //       if (json.containsKey('items')) {
  //         List data = json['items'];
  //         findPeople = data.map((e) => FindPeopleModel.fromJson(e)).toList();
  //       }
  //     }
  //   } on Exception catch (e) {
  //     isLoadingNotifier = false;
  //   }
  // }

  int findPeopleCurrentPage = 0;
  String? searchKey;
  late PagingController<int, AllPeopleModel> findPeoplePagingController;

  void findPeopleInitPagination() {
    findPeopleCurrentPage = 0;
    searchKey = null;
    findPeoplePagingController = PagingController(firstPageKey: 0);
    findPeoplePagingController.addPageRequestListener(
      (pageKey) {
        getFindPeopleWithPagination(page: pageKey);
      },
    );
  }

  // Method to refresh pagination
  void refreshPagination() {
    findPeopleCurrentPage = 0;
    findPeoplePagingController.refresh();
  }

  // Method to update search key and refresh
  void updateSearchKey(String? newSearchKey) {
    searchKey = newSearchKey;
    refreshPagination();
  }

  Future<void> getFindPeopleWithPagination({required int page}) async {
    try {
      if (findPeopleCurrentPage != page) {
        findPeopleCurrentPage = page;
      }
      final response = await FindPeopleServices()
          .getAndFindPeople(offset: page, searchKey: searchKey);
      if (kDebugMode) {
        log('getFindPeopleWithPagination------${response.data}');
      }

      if (response.statusCode == 200) {
        Map<String, dynamic> records = response.data;
        List<AllPeopleModel> temp = (records['items'] as List)
            .map((e) => AllPeopleModel.fromJson(e))
            .toList();

        if (records['hasMore'] == true) {
          findPeoplePagingController.appendPage(temp, page + 1);
        } else {
          findPeoplePagingController.appendLastPage(temp);
        }
      } else {
        findPeoplePagingController.appendLastPage([]);
      }

      findPeopleCurrentPage = page;
      notifyListeners();
    } on DioException catch (e) {
      debugPrint(e.toString());
    }
  }
}
