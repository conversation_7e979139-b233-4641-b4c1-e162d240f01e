class FindPeopleModel {
  String? firstName;
  String? middleName;
  String? lastName;
  String? previousLastName;
  String? nameSuffix;
  String? displayName;
  String? preferredName;
  String? correspondenceLanguage;
  String? personNumber;
  String? workPhoneCountryCode;
  String? workPhoneAreaCode;
  String? workPhoneNumber;
  String? workPhoneExtension;

  String? addressLine1;
  String? addressLine2;
  String? addressLine3;
  String? city;
  String? region;
  String? region2;
  String? country;
  String? postalCode;
  String? dateOfBirth;

  String? gender;
  String? maritalStatus;
  String? nationalIdType;
  String? nationalId;
  String? nationalIdCountry;
  String? nationalIdExpirationDate;
  String? nationalIdPlaceOfIssue;
  int? personId;
  String? effectiveStartDate;
  String? userName;

  String? passportIssueDate;
  String? passportNumber;
  String? passportIssuingCountry;
  String? passportId;
  String? passportExpirationDate;
  String? licenseNumber;
  String? driversLicenseExpirationDate;
  String? driversLicenseIssuingCountry;
  String? driversLicenseId;
  String? workMail;
  String? assignment;
  List<Links>? links;

  FindPeopleModel({
    this.firstName,
    this.middleName,
    this.lastName,
    this.previousLastName,
    this.nameSuffix,
    this.displayName,
    this.preferredName,
    this.correspondenceLanguage,
    this.personNumber,
    this.workPhoneCountryCode,
    this.workPhoneAreaCode,
    this.workPhoneNumber,
    this.workPhoneExtension,
    this.addressLine1,
    this.addressLine2,
    this.addressLine3,
    this.city,
    this.region,
    this.region2,
    this.country,
    this.postalCode,
    this.dateOfBirth,
    this.gender,
    this.maritalStatus,
    this.nationalIdType,
    this.nationalId,
    this.nationalIdCountry,
    this.nationalIdExpirationDate,
    this.nationalIdPlaceOfIssue,
    this.personId,
    this.effectiveStartDate,
    this.userName,
    this.passportIssueDate,
    this.passportNumber,
    this.passportIssuingCountry,
    this.passportId,
    this.passportExpirationDate,
    this.licenseNumber,
    this.driversLicenseExpirationDate,
    this.driversLicenseIssuingCountry,
    this.driversLicenseId,
    this.links,
    this.workMail,
    this.assignment,
  });

  FindPeopleModel.fromJson(Map<String, dynamic> json) {
    firstName = json['FirstName'];
    middleName = json['MiddleName'];
    lastName = json['LastName'];
    previousLastName = json['PreviousLastName'];
    nameSuffix = json['NameSuffix'];
    displayName = json['DisplayName'];
    preferredName = json['PreferredName'];
    correspondenceLanguage = json['CorrespondenceLanguage'];
    personNumber = json['PersonNumber'];
    workPhoneCountryCode = json['WorkPhoneCountryCode'];
    workPhoneAreaCode = json['WorkPhoneAreaCode'];
    workPhoneNumber = json['WorkPhoneNumber'];
    addressLine1 = json['AddressLine1'];
    addressLine2 = json['AddressLine2'];
    addressLine3 = json['AddressLine3'];
    city = json['City'];
    region = json['Region'];
    region2 = json['Region2'];
    country = json['Country'];
    postalCode = json['PostalCode'];
    dateOfBirth = json['DateOfBirth'];
    gender = json['Gender'];
    maritalStatus = json['MaritalStatus'];
    nationalIdType = json['NationalIdType'];
    nationalId = json['NationalId'];
    nationalIdCountry = json['NationalIdCountry'];
    nationalIdExpirationDate = json['NationalIdExpirationDate'];
    nationalIdPlaceOfIssue = json['NationalIdPlaceOfIssue'];
    personId = json['PersonId'];
    effectiveStartDate = json['EffectiveStartDate'];
    userName = json['UserName'];
    passportIssueDate = json['PassportIssueDate'];
    passportNumber = json['PassportNumber'];
    passportIssuingCountry = json['PassportIssuingCountry'];
    passportId = json['PassportId'];
    passportExpirationDate = json['PassportExpirationDate'];
    licenseNumber = json['LicenseNumber'];
    driversLicenseExpirationDate = json['DriversLicenseExpirationDate'];
    driversLicenseIssuingCountry = json['DriversLicenseIssuingCountry'];
    driversLicenseId = json['DriversLicenseId'];
    workMail = json['WorkEmail'];
    if (json['links'] != null) {
      links = <Links>[];
      json['links'].forEach((v) {
        links!.add(Links.fromJson(v));
      });
    }
  }
}

class Links {
  String? rel;
  String? href;
  String? name;
  String? kind;
  Properties? properties;

  Links({this.rel, this.href, this.name, this.kind, this.properties});

  Links.fromJson(Map<String, dynamic> json) {
    rel = json['rel'];
    href = json['href'];
    name = json['name'];
    kind = json['kind'];
    properties = json['properties'] != null
        ? Properties.fromJson(json['properties'])
        : null;
  }
}

class Properties {
  String? changeIndicator;

  Properties({this.changeIndicator});

  Properties.fromJson(Map<String, dynamic> json) {
    changeIndicator = json['changeIndicator'];
  }
}
