class AllPeopleModel {
  int? personId;
  String? personNumber;
  String? dateOfBirth;
  String? createdBy;
  String? creationDate;
  String? lastUpdatedBy;
  String? lastUpdateDate;
  List<Emails>? emails;
  List<Names>? names;
  List<Phones>? phones;

  AllPeopleModel(
      {this.personId,
      this.personNumber,
      this.dateOfBirth,
      this.createdBy,
      this.creationDate,
      this.lastUpdatedBy,
      this.lastUpdateDate,
      this.emails,
      this.names,
      this.phones});

  AllPeopleModel.fromJson(Map<String, dynamic> json) {
    personId = json['PersonId'];
    personNumber = json['PersonNumber'];
    dateOfBirth = json['DateOfBirth'];
    createdBy = json['CreatedBy'];
    creationDate = json['CreationDate'];
    lastUpdatedBy = json['LastUpdatedBy'];
    lastUpdateDate = json['LastUpdateDate'];
    if (json['emails'] != null) {
      emails = <Emails>[];
      json['emails'].forEach((v) {
        emails!.add(Emails.fromJson(v));
      });
    }
    if (json['names'] != null) {
      names = <Names>[];
      json['names'].forEach((v) {
        names!.add(Names.fromJson(v));
      });
    }
    if (json['phones'] != null) {
      phones = <Phones>[];
      json['phones'].forEach((v) {
        phones!.add(Phones.fromJson(v));
      });
    }
  }
}

class Emails {
  int? emailAddressId;
  String? emailAddress;

  Emails({this.emailAddressId, this.emailAddress});

  Emails.fromJson(Map<String, dynamic> json) {
    emailAddressId = json['EmailAddressId'];
    emailAddress = json['EmailAddress'];
  }
}

class Names {
  int? personNameId;
  String? effectiveStartDate;
  String? effectiveEndDate;
  String? legislationCode;
  String? lastName;
  String? firstName;
  String? displayName;
  String? fullName;

  Names(
      {this.personNameId,
      this.effectiveStartDate,
      this.effectiveEndDate,
      this.legislationCode,
      this.lastName,
      this.firstName,
      this.displayName,
      this.fullName});

  Names.fromJson(Map<String, dynamic> json) {
    personNameId = json['PersonNameId'];
    effectiveStartDate = json['EffectiveStartDate'];
    effectiveEndDate = json['EffectiveEndDate'];
    legislationCode = json['LegislationCode'];
    lastName = json['LastName'];
    firstName = json['FirstName'];
    displayName = json['DisplayName'];
    fullName = json['FullName'];
  }
}

class Phones {
  int? phoneId;
  String? phoneType;
  String? countryCodeNumber;
  String? areaCode;
  String? phoneNumber;

  Phones(
      {this.phoneId,
      this.phoneType,
      this.countryCodeNumber,
      this.areaCode,
      this.phoneNumber});

  Phones.fromJson(Map<String, dynamic> json) {
    phoneId = json['PhoneId'];
    phoneType = json['PhoneType'];
    countryCodeNumber = json['CountryCodeNumber'];
    areaCode = json['AreaCode'];
    phoneNumber = json['PhoneNumber'];
  }
}
