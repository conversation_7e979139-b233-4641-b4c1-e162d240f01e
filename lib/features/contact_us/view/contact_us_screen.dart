import 'package:addc/features/contact_us/providers/contact_us_provider.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/features/widgets/standard_text_form_field.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

class ContactUsScreen extends StatefulWidget {
  static const route = '/contact_us';
  const ContactUsScreen({super.key});

  @override
  State<ContactUsScreen> createState() => _ContactUsScreenState();
}

class _ContactUsScreenState extends State<ContactUsScreen> {
  late ContactUsProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<ContactUsProvider>();
  }

  @override
  Widget build(BuildContext context) {
    final List<String> connects = [
      'facebook',
      'twitter',
      'instagram',
      'linkedin',
      'youtube'
    ];

    return Scaffold(
      appBar: AppBar(
          title: const Text('Contact Us'), leading: const CommonBackButton()),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Container(
              margin: EdgeInsets.only(top: 25.h, left: 12.w, right: 12.w),
              decoration: BoxDecoration(
                  color: ColorConstants.colorFFFFFF,
                  borderRadius: BorderRadius.circular(18.r)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 12.w, vertical: 30.h),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('How Can We Assist?', style: ts16c000940w4h1),
                          H(10),
                          Text(
                              "Let us know what you're looking for or any issue you're facing. We'll get back to you with the best solution.",
                              style: ts12c9D9D9Dw4h1),
                          H(28),
                          // CommonDropdownFormField(
                          //   value: _provider.selectedAssistanceType,
                          //   hintText: 'Select',
                          //   onChanged: (val) =>
                          //       _provider.selectedAssistanceType = val,
                          //   items: _provider.assistanceTypes
                          //       .map((e) => DropdownMenuItem(
                          //             enabled: true,
                          //             value: e,
                          //             child: Text(e),
                          //           ))
                          //       .toList(),
                          // ),
                          H(14),
                          const StandartTextFormField(
                              hintText: 'Share your problem',
                              maxLines: 6,
                              borderRaduis: 20),
                          // const Spacer(),
                          Center(
                            child: Column(
                              children: [
                                H(100),
                                Text('Stay Connected', style: ts12c000940w4),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: connects.map(
                                    (e) {
                                      return IconButton(
                                          onPressed: () {},
                                          icon:
                                              SvgPicture.asset(e.asIconSvg()));
                                    },
                                  ).toList(),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(left: 12.0, right: 12, bottom: 20),
        child: ElevatedButton(onPressed: () {}, child: Text('Submit')),
      ),
    );
  }
}
