// import 'package:addc/features/geo_finder/view/geo_finder_screen.dart';
import 'package:addc/features/more/models/more_modal.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../permissions/view/permissions_screen.dart';
import '../../prayer_time/view/prayer_time_screen.dart';

class MoreCards extends StatelessWidget {
  final MoreModel item;
  final int index;
  const MoreCards({super.key, required this.item, required this.index});

  @override
  Widget build(BuildContext context) {
    String img = item.img ?? '';
    String text = item.text ?? '';

    return ListTile(
      contentPadding: EdgeInsets.zero,
      onTap: () => _onTap(index: index, context: context),
      leading: Container(
        padding: EdgeInsets.all(11),
        height: 42.h,
        width: 42.w,
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: ColorConstants.colorC1C1C1.withValues(alpha: 0.1)),
        child: Column(
          children: [
            SvgPicture.asset(img.asIconSvg()),
          ],
        ),
      ),
      title: Text(text, style: ts16c000940w4),
      // trailing: index == 5
      //     ? Consumer<MoreProvider>(
      //         builder: (context, moreProvider, child) {
      //           return Transform.scale(
      //             scale: 0.7,
      //             child: CupertinoSwitch(
      //               activeTrackColor: ColorConstants.color0AB3A1,
      //               value: moreProvider.switchValue,
      //               onChanged: moreProvider.onToggledBiometrics,
      //             ),
      //           );
      //         },
      //       )
      //     : null,
    );
  }

  _onTap({required int index, required BuildContext context}) {
    switch (index) {
      // case 0:
      //   Navigator.pushNamed(context, ProfileScreen.route);
      //   break;

      // case 0:
      //   Navigator.pushNamed(context, FindPeopleScreen.route);
      //   break;
      case 0:
        Navigator.pushNamed(context, PrayerTimeScreen.route);
        break;
      // case 1:
      //   Navigator.pushNamed(context, GeoFinderScreen.route);
      //   break;
      // case 3:
      //   Navigator.pushNamed(context, BookAVehicleHistoryScreen.route);
      //   break;
      // case 4:
      //   Navigator.pushNamed(context, SurveyScreen.route);
      //   break;
      // case 5:
      //   Navigator.pushNamed(context, ComplaintsScreen.route);
      //   break;
      case 1:
        launchURL(
            url:
                'https://app.powerbi.com/links/3E2D4CdVy2?ctid=5774259b-7c35-4df0-bf25-3901eb73df2c&pbi_source=linkShare');
        break;
      case 2:
        Navigator.pushNamed(context, PermissionsScreen.route);
        break;
      // case 5:
      //   Navigator.pushNamed(context, InboxScreen.route,
      //       arguments: InboxScreen(isShowMasterAppBar: false));
      //   break;
      // case 8:
      //   Navigator.pushNamed(context, SuggestionsScreen.route);
      //   break;
      // case 8:
      //   Navigator.pushNamed(context, TermsAndConditionScreen.route);
      //   break;
      // case 9:
      //   Navigator.pushNamed(context, PrivacyPoliciesScreen.route);
      //   break;
    }
  }
}
