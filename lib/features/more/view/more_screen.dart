import 'package:addc/features/authentication/services/biometric_auth_service.dart';
import 'package:addc/features/master_screen/providers/master_provider.dart';
import 'package:addc/features/more/widgets/more_cards.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:bottom_sheet_scaffold/bottom_sheet_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../models/more_modal.dart';

class MoreScreen extends StatefulWidget {
  const MoreScreen({super.key});

  @override
  State<MoreScreen> createState() => _MoreScreenState();
}

class _MoreScreenState extends State<MoreScreen> {
  final BiometricAuthService bioServices = BiometricAuthService();
  List<MoreModel> moreItems = [
    // MoreModel(img: 'profilepic', text: 'My Profile'),
    // MoreModel(img: 'find', text: 'Find people'),
    MoreModel(img: 'time', text: 'Prayer time'),
    // MoreModel(img: 'geofinder', text: 'Geofinder'),
    // MoreModel(img: 'car', text: 'Book a Vehicle'),
    // MoreModel(
    //     img: 'document-text',
    //     text: 'Survey${isTestVersion ? ' (Not Integrated)' : ''}'),
    MoreModel(img: 'apd', text: 'APD Dashboard'),
    MoreModel(img: 'attendance_2', text: 'Attendance'),
    // MoreModel(img: 'sms', text: 'Inbox'),
    // MoreModel(img: 'calendar-2', text: 'Suggestions( Fikrah)'),

    // MoreModel(img:'profilepic' ,text:'Terms and condition' ),
    // MoreModel(img:'profilepic' ,text:'Privacy Policies' ),
  ];
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      BottomSheetPanel.open();
    });
  }

  @override
  Widget build(BuildContext context) {
    final masterProvider = context.read<MasterProvider>();

    return BottomSheetScaffold(
      bottomSheet: DraggableBottomSheet(
        gradientOpacity: false,
        draggableBody: false,
        backgroundColor: ColorConstants.colorFFFFFF,
        onHide: () {
          masterProvider.masterIndex = masterProvider.prevIndex;
        },
        maxHeight: MediaQuery.of(context).size.height * 0.75,
        body: Align(
          alignment: Alignment.bottomCenter,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                constraints: BoxConstraints(maxHeight: 660.h, minHeight: 400.h),
                decoration: BoxDecoration(
                  color: ColorConstants.colorFFFFFF,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: ListView.separated(
                        shrinkWrap: true,
                        padding: EdgeInsets.only(
                            left: 24.w,
                            right: 24.w,
                            bottom: kBottomNavigationBarHeight * 1.5),
                        itemBuilder: (context, index) =>
                            MoreCards(index: index, item: moreItems[index]),
                        separatorBuilder: (context, index) => Container(
                          height: 1,
                          width: double.infinity,
                          color:
                              ColorConstants.color9D9D9D.withValues(alpha: 0.1),
                          margin: EdgeInsets.only(bottom: 5.w, top: 8.h),
                        ),
                        itemCount: moreItems.length,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        header: InkWell(
          onTap: () => BottomSheetPanel.open(),
          child: Container(
            height: 60,
            width: double.infinity,
            decoration: BoxDecoration(
                color: ColorConstants.colorFFFFFF,
                borderRadius:
                    BorderRadius.vertical(top: Radius.circular(20.r))),
            alignment: Alignment.topCenter,
            child: Container(
              height: 4.31.h,
              width: 72.42.w,
              margin: EdgeInsets.only(top: 13.h),
              decoration: BoxDecoration(color: ColorConstants.colorE7E7E7),
            ),
          ),
        ),
      ),
    );
  }
}
