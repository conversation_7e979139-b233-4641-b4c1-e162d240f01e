import 'package:addc/features/authentication/services/biometric_auth_service.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MoreProvider with ChangeNotifier {
  final BiometricAuthService _bioService = BiometricAuthService();
  bool switchValue = true;
  onToggledBiometrics(value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    if (value) {
      // bool isAuthenticated = false;
      BiometricStatus biometricStatus = await _bioService.authenticate();
      if (biometricStatus == BiometricStatus.unAuthorized) {
        switchValue = false;
        prefs.setBool('is_biometrics_enabled', switchValue);
        return;
      }
      // If authenticated, enable biometrics
      switchValue = value;
      prefs.setBool('is_biometrics_enabled', switchValue);
    } else {
      bool isAuthenticated =
          await _bioService.addedSecurityForDisableBiometricFeature();
      if (isAuthenticated) {
        switchValue = value;
        prefs.setBool('is_biometrics_enabled', switchValue);
      }
    }
    notifyListeners();
  }
}
