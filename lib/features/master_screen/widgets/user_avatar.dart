import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class UserAvatar extends StatelessWidget {
  final double diameter;
  final String? name;
  final double nameFontSize;
  final ImageProvider<Object>? imageProvider;
  final double borderWidth;
  const UserAvatar(
      {super.key,
      required this.diameter,
      required this.nameFontSize,
      this.name,
      required this.imageProvider,
      this.borderWidth = 1});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: diameter.h,
      width: diameter.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
              width: borderWidth,
              color: ColorConstants.color000940.withValues(alpha: 0.1))),
      padding: EdgeInsets.all(1),
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: ColorConstants.primaryColor,
          image: imageProvider != null
              ? DecorationImage(
                  image: imageProvider!,
                  fit: BoxFit.contain,
                  onError: (exception, stackTrace) {},
                )
              : null,
        ),
        child: imageProvider == null
            ? Text(
                name != null ? name![0].toUpperCase() : '',
                style: TextStyle(fontSize: nameFontSize),
              )
            : null,
      ),
    );
  }
}
