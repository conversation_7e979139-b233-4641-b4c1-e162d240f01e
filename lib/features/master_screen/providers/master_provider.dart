import 'package:flutter/material.dart';
import '../models/bottom_appbar_item.dart';

class MasterProvider extends ChangeNotifier {
  int prevIndex = 0;
  int _masterIndex = 0;
  int get masterIndex => _masterIndex;
  set masterIndex(int index) {
    prevIndex = _masterIndex;
    _masterIndex = index;
    notifyListeners();
  }

  List<BottomBarItem> bottomBarItems = [
    BottomBarItem(
        title: 'Home',
        unSelectedIcon: 'home_unselected',
        selectedIcon: 'home_selected'),
    BottomBarItem(
        title: 'Inbox',
        unSelectedIcon: 'inbox_unselected',
        selectedIcon: 'inbox_selected'),
    BottomBarItem(
        title: 'App Hub',
        unSelectedIcon: 'apps_hub_unselected',
        selectedIcon: 'apps_hub_selected'),
    BottomBarItem(
        title: 'More',
        unSelectedIcon: 'more_unselected',
        selectedIcon: 'more_selected'),
  ];
  // : [
  //     BottomBarItem(
  //         title: 'Home',
  //         unSelectedIcon: 'home_unselected',
  //         selectedIcon: 'home_selected'),
  //     BottomBarItem(
  //         title: 'Inbox',
  //         unSelectedIcon: 'inbox_unselected',
  //         selectedIcon: 'inbox_selected'),
  //     BottomBarItem(
  //         title: 'Feeds',
  //         unSelectedIcon: 'feeds_unselected',
  //         selectedIcon: 'feeds_selected'),
  //     BottomBarItem(
  //         title: 'App Hub',
  //         unSelectedIcon: 'apps_hub_unselected',
  //         selectedIcon: 'apps_hub_selected'),
  //     BottomBarItem(
  //         title: 'More',
  //         unSelectedIcon: 'more_unselected',
  //         selectedIcon: 'more_selected'),
  //   ];
}
