import 'dart:io';
import 'package:addc/features/inbox/providers/inbox_provider.dart';
import 'package:addc/features/inbox/providers/maximo_provider.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../models/bottom_appbar_item.dart';
import '../../providers/master_provider.dart';

class MasterBottomBar extends StatelessWidget {
  const MasterBottomBar({super.key});
  @override
  Widget build(BuildContext context) {
    double bottom = MediaQuery.paddingOf(context).bottom;
    if (Platform.isAndroid) {
      bottom += 12.5.h;
    }
    return Container(
      height: kBottomNavigationBarHeight + bottom,
      decoration: BoxDecoration(
        color: ColorConstants.colorFFFFFF,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(8.r),
        ),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.color000000.withValues(alpha: 0.1),
            blurRadius: 40,
            spreadRadius: 0,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      padding: EdgeInsets.only(bottom: bottom, top: 12.5.h),
      child: Consumer<MasterProvider>(
        builder: (context, provider, child) {
          List<BottomBarItem> bottomBarItems = provider.bottomBarItems;
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: bottomBarItems.map((e) {
              int index = bottomBarItems.indexOf(e);
              bool isSelected = provider.masterIndex == index;
              String icon = e.unSelectedIcon;
              Color bgColor = Colors.transparent;
              String? title;

              if (isSelected) {
                icon = e.selectedIcon;
                title = e.title;
                bgColor = ColorConstants.colorE9ECFF;
              }
              return InkWell(
                onTap: () => _onIndexChanged(
                    provider: provider, index: index, context: context),
                child: AnimatedSize(
                  alignment: Alignment.centerLeft,
                  duration: const Duration(milliseconds: 300),
                  child: Container(
                    height: 40.h,
                    decoration: BoxDecoration(
                        color: bgColor,
                        borderRadius: BorderRadius.circular(100.r)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.0.w),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                icon.asIconSvg(),
                                height: 24.h,
                                width: 24.w,
                                fit: BoxFit.cover,
                              ),
                              if (title != null) ...[
                                W(6),
                                Text(
                                  title,
                                  style: ts12c000940w4,
                                  maxLines: 2,
                                  textAlign: TextAlign.start,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ]
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          );
        },
      ),
    );
  }

  _onIndexChanged(
      {required MasterProvider provider,
      required int index,
      required BuildContext context}) {
    provider.masterIndex = index;
    switch (index) {
      case 1:
        _onIndexOne(context: context);
        break;
      case 4:
        _onIndexFour(context: context);
        break;
    }
  }

  _onIndexOne({required BuildContext context}) async {
    final provider = context.read<MaximoProvider>();
    final inboxProvider = context.read<InboxProvider>();
    inboxProvider.selectedCategoryIndex = 0;
    await provider.getMaximoList();
  }

  _onIndexFour({required BuildContext context}) {
    // callBottomSheet(ctx: context, body: MoreScreen());
  }
}
