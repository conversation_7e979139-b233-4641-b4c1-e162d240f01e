import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/authentication/providers/login_provider.dart';
import 'package:addc/features/master_screen/widgets/user_avatar.dart';
import 'package:addc/features/profile/providers/profile_provider.dart';
import 'package:addc/features/profile/view/profile_screen.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';

class MasterAppBar extends AppBar {
  final TextEditingController textEditingController;
  MasterAppBar(
      {super.key,
      required this.textEditingController,
      required BuildContext context})
      : super(
          // Removed the bottom PreferredSize containing CommonSearchTextFormField
          // bottom: PreferredSize(
          //   preferredSize: Size(double.infinity, 60.h),
          //   child: Padding(
          //     padding: EdgeInsets.only(bottom: 10.0.h, left: 12.w, right: 12.w),
          //     child: SizedBox(
          //       height: 40,
          //       child: CommonSearchTextFormField(
          //         controller: textEditingController,
          //         hintText: 'Search...',
          //         contentPadding:
          //             EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
          //       ),
          //     ),
          //   ),
          // ),
          leading: Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: InkWell(
              splashFactory: NoSplash.splashFactory,
              radius: 60,
              customBorder: CircleBorder(),
              onTap: () {
                Navigator.pushNamed(context, ProfileScreen.route);
              },
              // child: CommonCircleAvatar(
              //   image: 'john_big',
              //   height: 44.h,
              // ),
              child: Consumer<LogInProvider>(
                builder: (context, provider, _) {
                  return UserAvatar(
                      diameter: 44,
                      nameFontSize: 12,
                      imageProvider: provider.imageProvider,
                      name: LoginedUser.displayName);
                  // return CircleAvatar(
                  //   radius: 1,
                  //   // backgroundColor: ColorConstants.primaryColor,
                  //   // foregroundColor: provider.imageProvider != null
                  //   //     ? ColorConstants.primaryColor
                  //   //     : null,
                  //   foregroundImage: provider.imageProvider ?? AssetImage(''),
                  //   // AssetImage('john_big'.asDummyPng()),
                  //   // backgroundImage: AssetImage('john_big'.asDummyPng()),
                  //   onForegroundImageError: (exception, stackTrace) {},
                  //   // onBackgroundImageError: (exception, stackTrace) {},
                  //   child: Text(LoginedUser.displayName != null
                  //       ? LoginedUser.displayName![0].toUpperCase()
                  //       : ''),
                  // );
                },
              ),
            ),
          ),
          // toolbarHeight: 100.h,
          titleSpacing: 12.w,
          leadingWidth: 56.h,
          title: Consumer<ProfileProvider>(
            builder: (context, provider, _) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      SvgPicture.asset('hand'.asIconSvg()),
                      W(6),
                      if (LoginedUser.displayName == null)
                        Skeletonizer(
                            enabled: true,
                            child: Text('LoginedUser.displayName',
                                style: ts16c000940w4)),
                      if (LoginedUser.displayName != null)
                        Flexible(
                          child: Text(
                            '${LoginedUser.displayName}',
                            style: ts16c000940w4,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                        ),
                    ],
                  ),
                  // if (LoginedUser.jobTitle == null)
                  //   Skeletonizer(
                  //       enabled: true,
                  //       child:
                  //           Text('LoginedUser.jobTitle', style: ts12c9D9D9Dw4)),
                  if (LoginedUser.jobTitle != null)
                    Text(LoginedUser.jobTitle ?? '', style: ts12c9D9D9Dw4),
                ],
              );
            },
          ),
          actions: [
            //  CommonOutlinedIconButton(
            //   icon: SvgPicture.asset('notification_with_badge'
            //       .asIconSvg()), // if badge is not enabled use notification else notification_with_badge
            //   onPressed: () {
            //     Navigator.pushNamed(context, NotificationsScreen.route);
            //   },
            //   isBagdeEnabled: true,
            // ),
            // W(10),
            // CommonOutlinedIconButton(
            //   icon: SvgPicture.asset('message_with_badge'
            //       .asIconSvg()), // if badge is not enabled use message else message_with_badge
            //   onPressed: () {
            //     Navigator.pushNamed(context, InboxScreen.route);
            //   },
            //   isBagdeEnabled: true,
            // ),
            W(12),
          ],
        );
}
