import 'package:addc/features/inbox/views/inbox_screen.dart';
import 'package:addc/features/master_screen/providers/master_provider.dart';
import 'package:addc/features/master_screen/view/widgets/fade_indexstack.dart';
import 'package:double_back_to_close/double_back_to_close.dart';
import 'package:flutter/material.dart';
import 'package:addc/features/home/<USER>/home_screen.dart';
import 'package:addc/features/apps_hub/view/apps_hub_screen.dart';
import 'package:addc/features/more/view/more_screen.dart';
import 'package:provider/provider.dart';
import 'widgets/master_bottom_bar.dart';

class MasterScreen extends StatefulWidget {
  static const route = '/master_screen';
  const MasterScreen({super.key});

  @override
  State<MasterScreen> createState() => _MasterScreenState();
}

class _MasterScreenState extends State<MasterScreen> {
  late MasterProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<MasterProvider>();
  }

  @override
  Widget build(BuildContext context) {
    return DoubleBack(
      condition: _provider.masterIndex == 0,
      onConditionFail: () {
        _provider.masterIndex = 0;
      },
      onFirstBackPress: () {
        _provider.masterIndex = 0;
      },
      child: Scaffold(
        // extendBodyBehindAppBar: false,
        // extendBody: true,
        body: Consumer<MasterProvider>(
          builder: (context, provider, _) {
            return FadeIndexedStack(
              index: provider.masterIndex,
              children: [
                HomeScreen(),
                InboxScreen(isShowMasterAppBar: true),
                // FeedScreen(),
                AppsHubScreen(),
                MoreScreen(),
              ],
            );
          },
        ),
        bottomNavigationBar: const MasterBottomBar(),
      ),
    );
  }
}
