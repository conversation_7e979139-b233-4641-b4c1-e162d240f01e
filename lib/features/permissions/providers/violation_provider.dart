import 'dart:developer';

import 'package:addc/features/permissions/services/vioaltion_soap_service.dart';
import 'package:flutter/foundation.dart';
import '../models/violation_request.dart';

class ViolationProvider with ChangeNotifier {
  final ViolationSoapService _soapService = ViolationSoapService();

  bool _isLoading = false;
  String? _error;
  Map<String, dynamic>? _lastResponse;

  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic>? get lastResponse => _lastResponse;

  Future<bool> submitViolation(ViolationRequest request) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _soapService.submitViolation(request);
      if (kDebugMode) {
        log('Response: ${response.data}');
      }
      if (response.success) {
        _lastResponse = response.data;
        _error = null;
        return true;
      } else {
        _error = response.error ?? response.message;
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearResponse() {
    _lastResponse = null;
    notifyListeners();
  }
}
