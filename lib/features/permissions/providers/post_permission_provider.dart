import 'package:flutter/foundation.dart';
import '../models/permission_request.dart';
import '../models/permission_response.dart';
import '../services/permission_service.dart';

class PostPermissionProvider with ChangeNotifier {
  final PermissionService _permissionService = PermissionService();

  bool _isLoading = false;
  String? _error;
  PermissionResponse? _lastResponse;
  List<PermissionRequest> _submittedRequests = [];

  bool get isLoading => _isLoading;
  String? get error => _error;
  PermissionResponse? get lastResponse => _lastResponse;
  List<PermissionRequest> get submittedRequests => _submittedRequests;

  Future<bool> submitPermissionRequest(PermissionRequest request) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response =
          await _permissionService.submitPermissionRequest(request);

      if (response.success && response.data != null) {
        _lastResponse = response.data!;
        _submittedRequests.add(request);
        _error = null;
        return true;
      } else {
        _error = response.error ??
            response.message ??
            'Failed to submit permission request';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearResponse() {
    _lastResponse = null;
    notifyListeners();
  }

  void clearHistory() {
    _submittedRequests.clear();
    notifyListeners();
  }
}
