import 'dart:developer';

import 'package:addc/features/permissions/models/violation_model.dart';
import 'package:addc/features/permissions/services/permission_count_services.dart';
import 'package:addc/features/permissions/services/violations_list_services.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:xml/xml.dart';

class PermissionProvider extends ChangeNotifier {
  bool isLoading = false;
  int selectedTabBarIndex = 0;
  List<String> permissionTabBars = ['Violations', 'Permission'];
  onPermissionTabBarChanaged({required int index}) {
    selectedTabBarIndex = index;
    notifyListeners();
  }

  String? selectedApplicationType;
  List<String> applicationType = ['Official', 'Personal'];

  num violationCount = 0;
  Future<void> getViolationCount() async {
    try {
      final response = await PermissionCountServices().getViolationCount();
      if (response.statusCode == 200) {
        final document = XmlDocument.parse(response.data);

        // Find the <ViolationCount> element in the response
        final violationElement =
            document.findAllElements('ViolationCount').firstOrNull;

        if (violationElement != null) {
          final count =
              violationElement.getElement('VIOLATIONCOUNT')?.innerText ?? '0';
          final status = violationElement.getElement('STATUS')?.innerText ?? '';
          final statusDesc =
              violationElement.getElement('STATUS_DESC')?.innerText ?? '';

          log('VIOLATIONCOUNT: $count');
          violationCount = num.parse(count);
          debugPrint('VIOLATIONCOUNT: $violationCount');
          debugPrint('STATUS: $status');
          debugPrint('STATUS_DESC: $statusDesc');
        } else {
          debugPrint('No <ViolationCount> element found.');
        }
      }
    } catch (e) {
      debugPrint('Error: $e');
    }
  }

  List<ViolationModel> violationData = [];
  Future<List<ViolationModel>> getViolationsList() async {
    try {
      isLoading = true;
      notifyListeners();
      final response = await ViolationsListServices().getViolationsList();
      isLoading = false;
      violationData.clear();
      if (response.statusCode == 200) {
        final XmlDocument document = XmlDocument.parse(response.data);
        // Check the status in the response only if ViolationCount exists
        final violationCountElement =
            document.findAllElements('ViolationCount').firstOrNull;
        final status = violationCountElement?.getElement('STATUS')?.innerText;

        // If ViolationCount is not found, check for EmpViolations or EmpBalance status
        String? fallbackStatus;
        if (status == null) {
          // Try to find status in EmpViolations
          final empViolationsElement =
              document.findAllElements('EmpViolations').firstOrNull;
          fallbackStatus =
              empViolationsElement?.getElement('STATUS')?.innerText;

          // Try to find status in EmpBalance if still not found
          if (fallbackStatus == null) {
            final empBalanceElement =
                document.findAllElements('EmpBalance').firstOrNull;
            fallbackStatus = empBalanceElement?.getElement('STATUS')?.innerText;
          }
        }

        final effectiveStatus = status ?? fallbackStatus;

        print(effectiveStatus);

        if (effectiveStatus != null && effectiveStatus != '1') {
          return [];
        }

        // Find all EmpViolations elements in the response
        final violationsElements = document.findAllElements('EmpViolations');

        // Convert each XML element to a ViolationModel object
        violationData = violationsElements
            .map((element) => ViolationModel.fromXml(element))
            .toList();

        // Sort violationData based on the date (dt) in descending order
        violationData.sort((a, b) {
          final dateA = formatDate(a.dt) ?? DateTime(0);
          final dateB = formatDate(b.dt) ?? DateTime(0);
          return dateB.compareTo(dateA); // Sort in descending order
        });

        log('Violations list length: ${violationData.map(
          (e) => e.personId,
        )}');
        return violationData;
      } else {
        debugPrint('Error: Status code ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('Error: $e');
      return [];
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  DateTime? formatDate(String? date) {
    if (date == null || date.isEmpty) return DateTime(0);
    return DateFormatter.toDateTime(
      date: date,
      inputFormat: 'dd/MM/yyyy',
    );
  }
}
