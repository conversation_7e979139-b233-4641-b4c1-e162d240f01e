import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/foundation.dart';
import '../models/employee_balance_request.dart';
import '../models/employee_balance_response.dart';
import '../services/employee_balance_service.dart';

class EmployeeBalanceProvider with ChangeNotifier {
  final EmployeeBalanceService _balanceService = EmployeeBalanceService();

  bool _isLoading = false;
  String? _error;
  EmployeeBalanceResponse? _balanceData;

  bool get isLoading => _isLoading;
  String? get error => _error;
  EmployeeBalanceResponse? get balanceData => _balanceData;

  Future<bool> fetchEmployeeBalance() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final DateTime date = DateTime.now();
      final request = EmployeeBalanceRequest(
        personId: LoginedUser.employeeId ?? '',
        date: DateFormatter.formatDateTime(
          dateTime: date,
          outputFormat: 'dd/MM/yyyy',
        ),
      );

      final response = await _balanceService.getEmployeeBalance(request);

      if (response.success && response.data != null) {
        _balanceData = response.data!;
        _error = null;
        return true;
      } else {
        _error =
            response.error ?? response.message ?? 'Failed to fetch balance';
        _balanceData = null;
        return false;
      }
    } catch (e) {
      _error = e.toString();
      _balanceData = null;
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearBalance() {
    _balanceData = null;
    notifyListeners();
  }
}
