import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/permissions/models/violation_model.dart';
import 'package:addc/features/permissions/models/violation_request.dart';
import 'package:addc/features/permissions/providers/permission_provider.dart';
import 'package:addc/features/permissions/providers/violation_provider.dart';
import 'package:addc/features/permissions/view/permissions_screen.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/features/widgets/standard_text_form_field.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class PermissionSubmissionScreen extends StatefulWidget {
  static const route = '/permission_submission_screen';

  final String type;
  final ViolationModel? violation;

  const PermissionSubmissionScreen(
      {super.key, required this.type, required this.violation});

  @override
  State<PermissionSubmissionScreen> createState() =>
      _PermissionSubmissionScreenState();
}

class _PermissionSubmissionScreenState
    extends State<PermissionSubmissionScreen> {
  final formKey = GlobalKey<FormState>();
  final TextEditingController _reqDescController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    // bool isTypeSplit = false;
    // bool isType = false;
    String applicationType = widget.type.toLowerCase();
    // if ((applicationType == 'split')) {
    //   isTypeSplit = true;
    // }
    return Scaffold(
      appBar: AppBar(
        title: const Text('Violations'),
        leading: const CommonBackButton(),
      ),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: EdgeInsets.symmetric(horizontal: 12.w, vertical: 20.h),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 15.h),
            decoration: BoxDecoration(
              color: ColorConstants.colorFFFFFF,
              borderRadius: BorderRadius.circular(18.r),
            ),
            child: Column(
              children: [
                H(7),
                Row(
                  children: [
                    Text('Application Type'),
                    const Spacer(),
                    Container(
                      alignment: Alignment.centerLeft,
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 6.h,
                      ),
                      decoration: BoxDecoration(
                        color: ColorConstants.colorFFFFFF,
                        borderRadius: BorderRadius.circular(20.r),
                        border: Border.all(
                          color: ColorConstants.color0AB3A1,
                          width: 0.5,
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(widget.type, style: ts13c0AB3A1w4h1),
                        ],
                      ),
                    )
                  ],
                ),
                // if (applicationType == 'split') ...[
                //   H(15),
                //   Container(
                //     padding: EdgeInsets.symmetric(
                //         vertical: 2.5.w, horizontal: 5.18.h),
                //     decoration: BoxDecoration(
                //         color: ColorConstants.colorF9F9FF,
                //         borderRadius: BorderRadius.circular(3)),
                //     child: Row(
                //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //       children: [
                //         Text('Total violation duration', style: ts10c9D9D9Dw4),
                //         Text('01:00 hr', style: ts12c000940w4),
                //       ],
                //     ),
                //   ),
                //   H(22),
                //   Row(
                //     children: [
                //       Expanded(
                //         child: CommonTextFormField(
                //           titleStyle: ts14c000940w4h1,
                //           title: 'Official',
                //           hintText: 'Duration',
                //           contentPadding: EdgeInsets.symmetric(
                //               horizontal: 16.w, vertical: 14.h),
                //           controller: _officialController,
                //           readOnly: true,
                //           onTap: () async {
                //             DateTime? dateTime = await _dateProvider.selectDate(
                //                 context: context);
                //             if (dateTime != null) {
                //               _officialController.text =
                //                   DateFormatter.formatDateTime(
                //                 dateTime: dateTime,
                //                 outputFormat: 'dd-MMM-yyyy',
                //               );
                //             }
                //           },
                //         ),
                //       ),
                //       W(11),
                //       Expanded(
                //         child: CommonTextFormField(
                //           titleStyle: ts14c000940w4h1,
                //           title: 'Personal',
                //           hintText: 'Duration',
                //           contentPadding: EdgeInsets.symmetric(
                //               horizontal: 16.w, vertical: 14.h),
                //           controller: _personalController,
                //           readOnly: true,
                //           onTap: () async {
                //             DateTime? dateTime = await _dateProvider.selectDate(
                //                 context: context);
                //             if (dateTime != null) {
                //               _personalController.text =
                //                   DateFormatter.formatDateTime(
                //                 dateTime: dateTime,
                //                 outputFormat: 'dd-MMM-yyyy',
                //               );
                //             }
                //           },
                //         ),
                //       ),
                //     ],
                //   )
                // ],
                H(20),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(text: 'Reason', style: ts14c000940w4h1),
                        if (applicationType == 'official')
                          TextSpan(text: '*', style: ts14cFF453Fw4h1),
                      ],
                    ),
                  ),
                ),
                H(10),
                Form(
                  key: formKey,
                  child: StandartTextFormField(
                    hintText: 'Reason here...',
                    maxLines: 7,
                    borderRaduis: 20,
                    controller: _reqDescController,
                    validator: (value) {
                      if (applicationType == 'official' && value!.isEmpty) {
                        return 'Please enter a reason';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 20),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorConstants.color8B8D97,
                ),
                onPressed: () => Navigator.pop(context),
                child: Text('Cancel'),
              ),
            ),
            W(15),
            Expanded(
              child: ElevatedButton(
                onPressed: () async {
                  if (formKey.currentState == null ||
                      !formKey.currentState!.validate()) {
                    return;
                  }
                  final provider = context.read<ViolationProvider>();
                  final request = ViolationRequest(
                    personId: LoginedUser.employeeId,
                    date: widget.violation?.dt,
                    vStart: widget.violation?.vStart,
                    vEnd: widget.violation?.vEnd,
                    appType: applicationType == 'official' ? '2' : '1',
                    pVType: widget.violation?.vType != null
                        ? widget.violation!.vType!.toInt().toString()
                        : '',
                    reqDesc: _reqDescController.text.isEmpty
                        ? null
                        : _reqDescController.text,
                  );
                  EasyLoading.show();
                  final result = await provider.submitViolation(request);
                  EasyLoading.dismiss();
                  if (result) {
                    if (!context.mounted) return;
                    final permission = context.read<PermissionProvider>();
                    permission.getViolationsList();
                    permission.getViolationCount();
                    successBottomsheet(
                      context: context,
                      title: 'Request submitted\nsuccessfully',
                      onPressed: () => Navigator.popUntil(
                        context,
                        ModalRoute.withName(PermissionsScreen.route),
                      ),
                    );
                  } else {
                    if (!context.mounted) return;
                    showToast(provider.error ?? 'An error occurred');
                  }
                },
                child: Text('Submit'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
