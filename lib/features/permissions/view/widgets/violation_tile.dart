import 'package:addc/features/permissions/models/violation_model.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';
import 'violation_type_chip.dart';

class ViolationTile extends StatelessWidget {
  final ViolationModel item;
  const ViolationTile({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String formatDuration(int minutes) {
      if (minutes <= 0) return '0 min';
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (hours > 0 && remainingMinutes > 0) {
        return '$hours hr $remainingMinutes min';
      } else if (hours > 0) {
        return '$hours hr';
      } else {
        return '$remainingMinutes min';
      }
    }

    String title = item.absentReasonDesc ?? '';
    String vStart = item.vStart ?? '';
    String vEnd = item.vEnd ?? '';
    String date = item.dt ?? '';
    int vDuration = item.vMins ?? 0;
    String punchInTime = item.firstSignIn ?? '';
    String punchOutTime = item.lastSignOut ?? '';
    if (date.isNotEmpty) {
      date = DateFormatter.formatStringDate(
        date: date,
        inputFormat: 'dd/MM/yyyy',
        outputFormat: 'dd MMM, yyyy',
      );
    }

    vStart = formatTime(vStart);
    vEnd = formatTime(vEnd);
    punchInTime = formatTime(punchInTime);
    punchOutTime = formatTime(punchOutTime);
    String vDurationStr = '';
    if (vDuration != 0) {
      vDurationStr = formatDuration(vDuration);
    }
    // List<String> violationTypes = ['Official', 'Personal', 'Break', 'Split'];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        H(20),
        Text(title, style: ts15c000940w4h1),
        H(5),
        Row(
          children: [
            Text(vStart, style: ts15c000940w4h1),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 6.w),
              height: 3,
              width: 3,
              decoration: BoxDecoration(
                  color: ColorConstants.color000940, shape: BoxShape.circle),
            ),
            Text(vEnd, style: ts15c000940w4h1),
            Container(
              height: 3,
              width: 3,
              margin: EdgeInsets.symmetric(horizontal: 6.w),
              decoration: BoxDecoration(
                  color: ColorConstants.color000940, shape: BoxShape.circle),
            ),
            Text(vDurationStr, style: ts15c000940w4h1),
            const Spacer(),
            Text(date, style: ts13c9D9D9Dw4h1),
          ],
        ),
        H(15),
        if (punchInTime != 'NULL' || punchOutTime != 'NULL') ...[
          Text('Punch In - Out', style: ts15c000940w4h1),
          H(5),
          Row(
            children: [
              Text(punchInTime, style: ts15c000940w4h1),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 6.w),
                height: 3,
                width: 3,
                decoration: BoxDecoration(
                    color: ColorConstants.color000940, shape: BoxShape.circle),
              ),
              Text(
                punchOutTime == 'NULL' ? '-' : punchOutTime,
                style: ts15c000940w4h1,
              ),
            ],
          ),
        ],
        H(15),
        Row(
          spacing: 5.w,
          children: [
            ViolationTypeChip(item: 'Official', violation: item),
            if (punchInTime != 'NULL' && punchOutTime != 'NULL')
              ViolationTypeChip(
                item: 'Personal',
                violation: item,
              ),
          ],
        ),
        // SizedBox(
        //   height: 36.h,
        //   child: ListView.separated(
        //     shrinkWrap: true,
        //     scrollDirection: Axis.horizontal,
        //     itemCount: violationTypes.length,
        //     itemBuilder: (context, index) {
        //       return Padding(
        //         padding: EdgeInsets.only(bottom: 10.h),
        //         child: ViolationTypeChip(item: violationTypes[index]),
        //       );
        //     },
        //     separatorBuilder: (context, index) => W(5),
        //   ),
        // ),
      ],
    );
  }

  String formatTime(String time) {
    if (time.isEmpty) return '';
    return DateFormatter.formatStringDate(
      date: time,
      inputFormat: 'dd/MM/yyyy hh:mm:ss',
      outputFormat: 'hh:mm:ss',
    );
  }
}
