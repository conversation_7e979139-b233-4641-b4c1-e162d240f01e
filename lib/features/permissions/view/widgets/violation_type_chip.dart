import 'package:addc/features/permissions/models/violation_model.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../shared/constants/text_styles.dart';
import '../permission_submission_screen.dart';

class ViolationTypeChip extends HookWidget {
  final String item;
  final ViolationModel? violation;
  const ViolationTypeChip(
      {super.key, required this.item, required this.violation});

  @override
  Widget build(BuildContext context) {
    final isSelected = useState<bool>(false);
    String violationType = item;
    return InkWell(
      onTap: () {
        isSelected.value = !isSelected.value;
        Navigator.pushNamed(
          context,
          PermissionSubmissionScreen.route,
          arguments: PermissionSubmissionScreen(
            type: item,
            violation: violation,
          ),
        );
      },
      child: Container(
        alignment: Alignment.centerLeft,
        padding: isSelected.value
            ? EdgeInsets.only(top: 6.h, bottom: 6.h, right: 16.w, left: 2.w)
            : EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: isSelected.value
              ? ColorConstants.primaryColor
              : ColorConstants.colorFFFFFF,
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(color: ColorConstants.color0AB3A1, width: 0.5),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isSelected.value)
              SvgPicture.asset('tick-circle-small'.asIconSvg()),
            Text(violationType,
                style: isSelected.value ? ts12cFFFFFFw4 : ts13c0AB3A1w4h1),
          ],
        ),
      ),
    );
  }
}
