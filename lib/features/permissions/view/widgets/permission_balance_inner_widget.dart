import 'package:flutter/material.dart';

import '../../../../shared/constants/text_styles.dart';

class PermissionBalanceInnerWidget extends StatelessWidget {
  final String title;
  final String balance;
  final String unit;
  const PermissionBalanceInnerWidget(
      {super.key,
      required this.balance,
      required this.title,
      required this.unit});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: ts12c0AB3A1w4),
        Text.rich(
          TextSpan(
            children: [
              TextSpan(text: '$balance ', style: ts40c1E1E1Ew4h1),
              TextSpan(text: unit, style: ts16c1E1E1Ew4h1),
            ],
          ),
        ),
      ],
    );
  }
}
