import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../../widgets/common_tab_bar_tile.dart';
import '../../../widgets/gap.dart';
import '../../providers/permission_provider.dart';

class PermissionsTabBarSection extends StatelessWidget {
  final PageController pageController;
  const PermissionsTabBarSection({super.key, required this.pageController});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<PermissionProvider>();
    return Column(
      children: [
        H(25),
        SizedBox(
          height: 36.h,
          child: ListView.separated(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            itemCount: provider.permissionTabBars.length,
            itemBuilder: (context, index) {
              return Consumer<PermissionProvider>(
                  builder: (context, provider, _) {
                bool isSelected = provider.selectedTabBarIndex == index;

                return CommonTabBarTile(
                  title: provider.permissionTabBars[index],
                  isSelected: isSelected,
                  onTap: () {
                    provider.onPermissionTabBarChanaged(index: index);
                    pageController.animateToPage(index,
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.easeIn);
                  },
                );
              });
            },
            separatorBuilder: (context, index) => W(15),
          ),
        ),
      ],
    );
  }
}
