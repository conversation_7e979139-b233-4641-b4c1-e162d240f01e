import 'package:addc/features/permissions/providers/employee_balance_provider.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../shared/constants/color_constants.dart';
import '../widgets/permission_balance_inner_widget.dart';

class PermissionBalanceSection extends StatelessWidget {
  const PermissionBalanceSection({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<EmployeeBalanceProvider>();
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        Container(
          margin: EdgeInsets.symmetric(horizontal: 12.w),
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.h),
          decoration: BoxDecoration(
              color: ColorConstants.colorFFFFFF,
              borderRadius: BorderRadius.circular(18.r)),
          child: Skeletonizer(
            enabled: provider.isLoading,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                PermissionBalanceInnerWidget(
                  title: 'Permission Balance',
                  balance: provider.balanceData?.personalBalance ?? '0',
                  unit: 'hrs',
                ),
                PermissionBalanceInnerWidget(
                  title: 'Break Balance',
                  balance: provider.balanceData?.breakBalance ?? '0',
                  unit: 'hrs',
                ),
              ],
            ),
          ),
        ),
        Align(
          alignment: Alignment.bottomRight,
          child: Padding(
            padding: EdgeInsets.only(right: 12.w),
            child: ClipRRect(
              borderRadius:
                  BorderRadius.only(bottomRight: Radius.circular(18.r)),
              child: SvgPicture.asset('summary_graphics'.asIconSvg()),
            ),
          ),
        ),
      ],
    );
  }
}
