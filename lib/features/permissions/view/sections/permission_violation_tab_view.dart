import 'package:addc/features/permissions/models/violation_model.dart';
import 'package:addc/features/permissions/providers/permission_provider.dart';
import 'package:addc/features/permissions/view/widgets/violation_tile.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class PermissionViolationTabView extends StatelessWidget {
  const PermissionViolationTabView({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Consumer<PermissionProvider>(
            builder: (context, provider, _) {
              if (provider.isLoading) {
                return Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height / 4),
                  child: Center(child: const CircularProgressIndicator()),
                );
              }
              if (!provider.isLoading && provider.violationData.isNotEmpty) {
                return Container(
                  margin: EdgeInsets.symmetric(horizontal: 12.w),
                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                  decoration: BoxDecoration(
                      color: ColorConstants.colorFFFFFF,
                      borderRadius: BorderRadius.circular(18.r)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      H(30),
                      Text(
                        'Violations',
                        style: ts17c000940w4h1,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: provider.violationData.length,
                        padding: EdgeInsets.only(bottom: 20.h),
                        itemBuilder: (context, index) {
                          ViolationModel item = provider.violationData[index];
                          return ViolationTile(item: item);
                        },
                        separatorBuilder: (context, index) => Container(
                          height: 0.5,
                          color: ColorConstants.colorD9D9D9,
                          margin: EdgeInsets.only(top: 10.h),
                        ),
                      ),
                    ],
                  ),
                );
              }
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 24.h),
                child: Center(
                  child: Text(
                    'No data found',
                    style: ts17c000940w4h1,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
