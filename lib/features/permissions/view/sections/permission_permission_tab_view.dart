import 'dart:io';

import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/permissions/models/permission_request.dart';
import 'package:addc/features/permissions/providers/permission_provider.dart';
import 'package:addc/features/permissions/providers/post_permission_provider.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/common_dropdown_field.dart';
import 'package:addc/features/widgets/common_success_bottom_sheet.dart';
import 'package:addc/features/widgets/standard_text_form_field.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/helper/date_picker_provider.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

import '../../../../shared/constants/text_styles.dart';
import '../../../../widgets/common_text_form_field.dart';
import '../../../widgets/gap.dart';

class PermissionPermissionTabView extends StatefulWidget {
  const PermissionPermissionTabView({super.key});

  @override
  State<PermissionPermissionTabView> createState() =>
      _PermissionPermissionTabViewState();
}

class _PermissionPermissionTabViewState
    extends State<PermissionPermissionTabView> {
  late DatePickerProvider _dateProvider;
  late PermissionProvider _provider;
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _startTimeController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();

  final _formKey = GlobalKey<FormState>();
  final TextEditingController _reasonController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _dateProvider = context.read<DatePickerProvider>();
    _provider = context.read<PermissionProvider>();
  }

  @override
  void dispose() {
    _dateController.dispose();
    _startTimeController.dispose();
    _endDateController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  bool _validateForm() {
    if (_provider.selectedApplicationType == null) {
      showToast('Please select application type');
      return false;
    }
    if (_dateController.text.isEmpty) {
      showToast('Please select date');
      return false;
    }
    if (_startTimeController.text.isEmpty) {
      showToast('Please select start time');
      return false;
    }
    if (_endDateController.text.isEmpty) {
      showToast('Please select end time');
      return false;
    }
    if (_reasonController.text.isEmpty) {
      showToast('Please enter reason');
      return false;
    }
    return _formKey.currentState?.validate() ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      Container(
                        margin: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 15.h),
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 15.h),
                        decoration: BoxDecoration(
                            color: ColorConstants.colorFFFFFF,
                            borderRadius: BorderRadius.circular(18.r)),
                        child: Column(
                          children: [
                            H(2),
                            Row(
                              children: [
                                Expanded(
                                  child: CommonDropdownFormField(
                                    value: _provider.selectedApplicationType,
                                    titleStyle: ts14c000940w4h1,
                                    title: 'Application Type*',
                                    hintText: 'Select',
                                    validator: (value) {
                                      if (value == null) {
                                        return 'Please select application type';
                                      }
                                      return null;
                                    },
                                    onChanged: (val) =>
                                        _provider.selectedApplicationType = val,
                                    items: _provider.applicationType
                                        .map((e) => DropdownMenuItem(
                                              enabled: true,
                                              value: e,
                                              child: Text(e),
                                            ))
                                        .toList(),
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: ColorConstants.primaryColor,
                                        width: 0.5,
                                      ),
                                      borderRadius: BorderRadius.circular(30.r),
                                    ),
                                  ),
                                ),
                                W(11),
                                Expanded(
                                  child: CommonTextFormField(
                                    titleStyle: ts14c000940w4h1,
                                    title: 'Date*',
                                    hintText: 'Select',
                                    validator: (value) {
                                      if (value?.isEmpty ?? true) {
                                        return 'Please select date';
                                      }
                                      return null;
                                    },
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 16.w, vertical: 14.h),
                                    controller: _dateController,
                                    readOnly: true,
                                    suffixIcon: Padding(
                                      padding: EdgeInsets.all(13.0),
                                      child: SvgPicture.asset(
                                          'calendar_3'.asIconSvg()),
                                    ),
                                    onTap: () async {
                                      DateTime? dateTime = await _dateProvider
                                          .selectDate(context: context);
                                      if (dateTime != null) {
                                        _dateController.text =
                                            DateFormatter.formatDateTime(
                                          dateTime: dateTime,
                                          outputFormat: 'dd-MMM-yyyy',
                                        );
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            H(15),
                            Row(
                              children: [
                                Expanded(
                                  child: CommonTextFormField(
                                    titleStyle: ts14c000940w4h1,
                                    title: 'Start Time*',
                                    hintText: 'Select',
                                    validator: (value) {
                                      if (value?.isEmpty ?? true) {
                                        return 'Please select start time';
                                      }
                                      return null;
                                    },
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 16.w, vertical: 14.h),
                                    controller: _startTimeController,
                                    readOnly: true,
                                    suffixIcon: Padding(
                                      padding: EdgeInsets.all(13.0),
                                      child:
                                          SvgPicture.asset('clock'.asIconSvg()),
                                    ),
                                    onTap: () async {
                                      DateTime? dateTime = await _dateProvider
                                          .pickTimer(context: context);
                                      if (dateTime != null) {
                                        _startTimeController.text =
                                            DateFormatter.formatDateTime(
                                          dateTime: dateTime,
                                          outputFormat: 'HH:mm',
                                        );
                                      }
                                    },
                                  ),
                                ),
                                W(11),
                                Expanded(
                                  child: CommonTextFormField(
                                    titleStyle: ts14c000940w4h1,
                                    title: 'End Time*',
                                    hintText: 'Select',
                                    validator: (value) {
                                      if (value?.isEmpty ?? true) {
                                        return 'Please select end time';
                                      }
                                      return null;
                                    },
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 16.w, vertical: 14.h),
                                    controller: _endDateController,
                                    readOnly: true,
                                    suffixIcon: Padding(
                                      padding: EdgeInsets.all(13.0),
                                      child:
                                          SvgPicture.asset('clock'.asIconSvg()),
                                    ),
                                    onTap: () async {
                                      DateTime? dateTime = await _dateProvider
                                          .pickTimer(context: context);
                                      if (dateTime != null) {
                                        _endDateController.text =
                                            DateFormatter.formatDateTime(
                                          dateTime: dateTime,
                                          outputFormat: 'HH:mm',
                                        );
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            H(15),
                            Align(
                                alignment: Alignment.centerLeft,
                                child: Text('Reason*', style: ts14c000940w4h1)),
                            H(15),
                            StandartTextFormField(
                              controller: _reasonController,
                              hintText: 'Reason here...',
                              borderRaduis: 20,
                              maxLines: 7,
                              validator: (value) {
                                if (value?.isEmpty ?? true) {
                                  return 'Please enter reason';
                                }
                                return null;
                              },
                            )
                          ],
                        ),
                      ),
                      H(kBottomNavigationBarHeight + (Platform.isIOS ? 50 : 30))
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: 16.w, vertical: Platform.isIOS ? 30 : 0),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.color8B8D97),
                    onPressed: () => Navigator.pop(context),
                    child: Text('Cancel'),
                  ),
                ),
                W(15),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      if (_formKey.currentState?.validate() == true &&
                          _validateForm()) {
                        final date = DateFormatter.formatStringDate(
                          date: _dateController.text,
                          inputFormat: 'dd-MMM-yyyy',
                          outputFormat: 'dd/MM/yyyy',
                        );
                        final startTime = DateFormatter.formatStringDate(
                          date: _startTimeController.text,
                          inputFormat: 'HH:mm',
                          outputFormat: 'HH:mm:ss',
                        );
                        final endTime = DateFormatter.formatStringDate(
                          date: _endDateController.text,
                          inputFormat: 'HH:mm',
                          outputFormat: 'HH:mm:ss',
                        );
                        final request = PermissionRequest(
                          personId: LoginedUser.employeeId,
                          // personId: '47726',
                          date: date,
                          vStart: startTime,
                          vEnd: endTime,
                          appType:
                              _provider.selectedApplicationType == 'Official'
                                  ? '2'
                                  : '1',
                          reqDesc: _reasonController.text.isEmpty
                              ? null
                              : _reasonController.text,
                        );
                        EasyLoading.show();
                        final provider = context.read<PostPermissionProvider>();
                        final result =
                            await provider.submitPermissionRequest(request);
                        EasyLoading.dismiss();
                        if (!context.mounted) return;
                        if (result) {
                          successBottomsheet(
                            context: context,
                            title: 'Permission Request Submitted',
                            description:
                                'Your permission request has been submitted successfully.',
                            status: BottomSheetStatus.success,
                            onPressed: () => Navigator.pop(context),
                          );
                        } else {
                          showToast(
                              'Failed to submit permission request. Please try again.');
                          successBottomsheet(
                            context: context,
                            title: 'Permission Request Failed',
                            description:
                                'There was an issue submitting your permission request. Please check your inputs and try again.',
                            status: BottomSheetStatus.warning,
                            onPressed: () => Navigator.pop(context),
                          );
                        }
                        // successBottomsheet(
                        //   context: context,
                        //   title: 'Insufficient personal\nquota',
                        //   description:
                        //       'Sorry ! You don’t have the requested\namount of time on your personal\nbalance.',
                        //   status: BottomSheetStatus.warning,
                        //   onPressed: () => Navigator.pop(context),
                        // );
                      }
                    },
                    child: Text('Submit'),
                  ),
                )
              ],
            ),
          ),
        )
      ],
    );
  }
}
