import 'package:addc/features/permissions/providers/employee_balance_provider.dart';
import 'package:addc/features/permissions/providers/permission_provider.dart';
import 'package:addc/features/permissions/view/sections/permissions_tab_bar_section.dart';
import 'package:addc/shared/extensions/sizedbox_extension.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'sections/permission_balance_section.dart';
import 'sections/permission_permission_tab_view.dart';
import 'sections/permission_violation_tab_view.dart';

class PermissionsScreen extends StatefulWidget {
  static const route = '/permissions_screen';
  const PermissionsScreen({super.key});

  @override
  State<PermissionsScreen> createState() => _PermissionsScreenState();
}

class _PermissionsScreenState extends State<PermissionsScreen> {
  late PageController _pageController;
  late PermissionProvider _provider;
  @override
  void initState() {
    super.initState();
    _provider = context.read<PermissionProvider>();
    final empBalanceProvider = context.read<EmployeeBalanceProvider>();
    _pageController = PageController(initialPage: 0, keepPage: false);
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        _provider.selectedTabBarIndex = 0;
        _provider.getViolationsList();
        empBalanceProvider.fetchEmployeeBalance();
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Permission'),
        leading: const CommonBackButton(),
      ),
      body: Padding(
        padding: EdgeInsets.only(bottom: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PermissionsTabBarSection(pageController: _pageController),
            25.height,
            const PermissionBalanceSection(),
            10.height,
            Expanded(
              child: Consumer<PermissionProvider>(
                builder: (context, provider, _) {
                  return PageView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: provider.permissionTabBars.length,
                    controller: _pageController,
                    itemBuilder: (context, index) {
                      switch (provider.selectedTabBarIndex) {
                        case 0:
                          return const PermissionViolationTabView();
                        case 1:
                          return const PermissionPermissionTabView();
                      }
                      return const SizedBox();
                    },
                    onPageChanged: (index) {
                      provider.onPermissionTabBarChanaged(index: index);
                      if (index == 0) {
                        _provider.getViolationsList();
                      }
                    },
                  );
                },
              ),
            ),
            // kBottomNavigationBarHeight.height
          ],
        ),
      ),
    );
  }
}
