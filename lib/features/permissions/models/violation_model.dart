import 'package:xml/xml.dart';

class ViolationModel {
  String? dt;
  String? personId;
  String? vStart;
  String? vEnd;
  int? vMins;
  double? vType;
  String? vTypeDesc;
  String? absentReasonDesc;
  double? appType;
  String? appTypeDesc;
  double? appMins;
  double? appStatus;
  String? appStatusDesc;
  double? isBreak;
  double? isSplit;
  String? firstSignIn;
  String? lastSignOut;
  double? status;
  String? statusDesc;

  ViolationModel({
    this.dt,
    this.personId,
    this.vStart,
    this.vEnd,
    this.vMins,
    this.vType,
    this.vTypeDesc,
    this.absentReasonDesc,
    this.appType,
    this.appTypeDesc,
    this.appMins,
    this.appStatus,
    this.appStatusDesc,
    this.isBreak,
    this.isSplit,
    this.firstSignIn,
    this.lastSignOut,
    this.status,
    this.statusDesc,
  });

  factory ViolationModel.fromXml(XmlElement element) {
    return ViolationModel(
      dt: element.getElement('DT')?.innerText,
      personId: element.getElement('PERSON_ID')?.innerText,
      vStart: element.getElement('V_START')?.innerText,
      vEnd: element.getElement('V_END')?.innerText,
      vMins: int.tryParse(element.getElement('V_MINS')?.innerText ?? ''),
      vType: double.tryParse(element.getElement('V_TYPE')?.innerText ?? ''),
      vTypeDesc: element.getElement('V_TYPE_DESC')?.innerText,
      absentReasonDesc: element.getElement('ABSENT_REASON_DESC')?.innerText,
      appType: double.tryParse(element.getElement('APP_TYPE')?.innerText ?? ''),
      appTypeDesc: element.getElement('APP_TYPE_DESC')?.innerText,
      appMins: double.tryParse(element.getElement('APP_MINS')?.innerText ?? ''),
      appStatus:
          double.tryParse(element.getElement('APP_STATUS')?.innerText ?? ''),
      appStatusDesc: element.getElement('APP_STATUS_DESC')?.innerText,
      isBreak: double.tryParse(element.getElement('IS_BREAK')?.innerText ?? ''),
      isSplit: double.tryParse(element.getElement('IS_SPLIT')?.innerText ?? ''),
      firstSignIn: element.getElement('FIRST_SIGN_IN')?.innerText,
      lastSignOut: element.getElement('LAST_SIGN_OUT')?.innerText,
      status: double.tryParse(element.getElement('STATUS')?.innerText ?? ''),
      statusDesc: element.getElement('STATUS_DESC')?.innerText,
    );
  }
}
