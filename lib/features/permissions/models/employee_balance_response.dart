class EmployeeBalanceResponse {
  final String personalBalance;
  final String breakBalance;
  final int status;
  final String statusDesc;

  EmployeeBalanceResponse({
    required this.personalBalance,
    required this.breakBalance,
    required this.status,
    required this.statusDesc,
  });

  factory EmployeeBalanceResponse.fromJson(Map<String, dynamic> json) {
    return EmployeeBalanceResponse(
      personalBalance: json['personalBalance'] ?? '00:00',
      breakBalance: json['breakBalance'] ?? '00:00',
      status: json['status'] ?? 0,
      statusDesc: json['statusDesc'] ?? 'Unknown',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'personalBalance': personalBalance,
      'breakBalance': breakBalance,
      'status': status,
      'statusDesc': statusDesc,
    };
  }

  bool get isSuccess => status == 1;
}
