class PermissionRequest {
  final String? personId;
  final String? date;
  final String? vStart;
  final String? vEnd;
  final String appType;
  final String? reqDesc;

  PermissionRequest({
    this.personId,
    this.date,
    this.vStart,
    this.vEnd,
    required this.appType,
    this.reqDesc,
  });

  Map<String, dynamic> toJson() {
    return {
      'personId': personId,
      'date': date,
      'vStart': vStart,
      'vEnd': vEnd,
      'appType': appType,
      'reqDesc': reqDesc,
    };
  }

  factory PermissionRequest.fromJson(Map<String, dynamic> json) {
    return PermissionRequest(
      personId: json['personId'],
      date: json['date'],
      vStart: json['vStart'],
      vEnd: json['vEnd'],
      appType: json['appType'] ?? '',
      reqDesc: json['reqDesc'],
    );
  }
}
