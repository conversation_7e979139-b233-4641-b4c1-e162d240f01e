class ViolationRequest {
  final String? personId;
  final String? date;
  final String? vStart;
  final String? vEnd;
  final String pVType;
  final String appType;
  final String? reqDesc;

  ViolationRequest({
    this.personId,
    this.date,
    this.vStart,
    this.vEnd,
    required this.pVType,
    required this.appType,
    this.reqDesc,
  });

  Map<String, dynamic> toJson() {
    return {
      'personId': personId,
      'date': date,
      'vStart': vStart,
      'vEnd': vEnd,
      'appType': appType,
      'pVType': pVType,
      'reqDesc': reqDesc,
    };
  }
}
