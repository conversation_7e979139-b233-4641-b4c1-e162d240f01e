class PermissionResponse {
  final bool success;
  final String message;
  final String? requestId;
  final DateTime timestamp;
  final Map<String, dynamic>? rawData;

  PermissionResponse({
    required this.success,
    required this.message,
    this.requestId,
    required this.timestamp,
    this.rawData,
  });

  factory PermissionResponse.fromXml(String xmlResponse) {
    return PermissionResponse(
      success: true,
      message: 'Permission request submitted successfully',
      timestamp: DateTime.now(),
      rawData: {'xmlResponse': xmlResponse},
    );
  }

  factory PermissionResponse.error(String errorMessage) {
    return PermissionResponse(
      success: false,
      message: errorMessage,
      timestamp: DateTime.now(),
    );
  }
}
