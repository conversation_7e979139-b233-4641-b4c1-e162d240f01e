import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';
import 'package:xml/xml.dart';
import '../models/employee_balance_request.dart';
import '../models/employee_balance_response.dart';
import '../models/api_response.dart';

class EmployeeBalanceService {
  late final Dio _dio;

  EmployeeBalanceService() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConstants.endPointWebTAUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': 'http://tempuri.org/GetEmpBalance',
      },
    ));

    // Add interceptors for logging (optional)
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));
  }

  String _buildSoapEnvelope(EmployeeBalanceRequest request) {
    return '''<?xml version="1.0" encoding="utf-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:GetEmpBalance>
         <tem:pPersonId>${request.personId}</tem:pPersonId>
         <tem:pDate>${request.date}</tem:pDate>
      </tem:GetEmpBalance>
   </soapenv:Body>
</soapenv:Envelope>''';
  }

  Future<ApiResponse<EmployeeBalanceResponse>> getEmployeeBalance(
      EmployeeBalanceRequest request) async {
    try {
      final soapEnvelope = _buildSoapEnvelope(request);

      final response = await _dio.post(
        '',
        data: soapEnvelope,
        options: Options(
          headers: {
            'Content-Type': 'text/xml; charset=utf-8',
            'SOAPAction': 'http://tempuri.org/GetEmpBalance',
          },
        ),
      );

      if (response.statusCode == 200) {
        final balanceData = _parseSoapResponse(response.data);
        return ApiResponse(
          success: balanceData.isSuccess,
          data: balanceData,
          message: balanceData.isSuccess
              ? 'Balance fetched successfully'
              : balanceData.statusDesc,
        );
      } else {
        return ApiResponse(
          success: false,
          error: 'HTTP Error: ${response.statusCode}',
          message: 'Failed to fetch employee balance',
        );
      }
    } on DioException catch (e) {
      return ApiResponse(
        success: false,
        error: _handleDioError(e),
        message: 'Network error occurred',
      );
    } catch (e) {
      return ApiResponse(
        success: false,
        error: e.toString(),
        message: 'An unexpected error occurred',
      );
    }
  }

  EmployeeBalanceResponse _parseSoapResponse(String responseData) {
    try {
      final document = XmlDocument.parse(responseData);

      // Navigate through the SOAP response structure
      final empBalanceElement = document
          .findAllElements('soap:Body')
          .first
          .findAllElements('GetEmpBalanceResponse')
          .first
          .findAllElements('GetEmpBalanceResult')
          .first
          .findAllElements('diffgr:diffgram')
          .first
          .findAllElements('DocumentElement')
          .first
          .findAllElements('EmpBalance')
          .first;

      // Extract the balance data
      final personalBalance =
          empBalanceElement.findElements('PERSONALBALANCE').firstOrNull?.text ??
              '00:00';

      final breakBalance =
          empBalanceElement.findElements('BREAKBALANCE').firstOrNull?.text ??
              '00:00';

      final status = int.tryParse(
              empBalanceElement.findElements('STATUS').firstOrNull?.text ??
                  '0') ??
          0;

      final statusDesc =
          empBalanceElement.findElements('STATUS_DESC').firstOrNull?.text ??
              'Unknown';

      return EmployeeBalanceResponse(
        personalBalance: personalBalance,
        breakBalance: breakBalance,
        status: status,
        statusDesc: statusDesc,
      );
    } catch (e) {
      print('Error parsing SOAP response: $e');
      return EmployeeBalanceResponse(
        personalBalance: '00:00',
        breakBalance: '00:00',
        status: 0,
        statusDesc: 'Parse Error: ${e.toString()}',
      );
    }
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout';
      case DioExceptionType.sendTimeout:
        return 'Send timeout';
      case DioExceptionType.receiveTimeout:
        return 'Receive timeout';
      case DioExceptionType.badResponse:
        return 'Bad response: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Request cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error';
      case DioExceptionType.unknown:
        return 'Unknown error: ${e.message}';
      default:
        return 'Network error occurred';
    }
  }
}
