import 'dart:developer';

import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';
import 'package:xml/xml.dart';
import '../models/violation_request.dart';
import '../models/api_response.dart';

class ViolationSoapService {
  late final Dio _dio;

  ViolationSoapService() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConstants.endPointWebTAUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': '${ApiConstants.soapActionUrl}PostPermissionRequest',
      },
    ));

    // Add interceptors for logging (optional)
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));
  }

  String _buildSoapEnvelope(ViolationRequest request) {
    return '''<?xml version="1.0" encoding="utf-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:PostPermissionRequest>
         ${request.personId != null ? '<tem:pPersonId>${request.personId}</tem:pPersonId>' : ''}
         ${request.date != null ? '<tem:pDate>${request.date}</tem:pDate>' : ''}
         ${request.vStart != null ? '<tem:pVStart>${request.vStart}</tem:pVStart>' : ''}
         ${request.vEnd != null ? '<tem:pVEnd>${request.vEnd}</tem:pVEnd>' : ''}
         <tem:pVType>${request.pVType}</tem:pVType>
         <tem:pAppType>${request.appType}</tem:pAppType>
         ${request.reqDesc != null ? '<tem:pReqDesc>${request.reqDesc}</tem:pReqDesc>' : ''}
      </tem:PostPermissionRequest>
   </soapenv:Body>
</soapenv:Envelope>''';
  }

  Future<ApiResponse<Map<String, dynamic>>> submitViolation(
      ViolationRequest request) async {
    try {
      final soapEnvelope = _buildSoapEnvelope(request);

      final response = await _dio.post(
        '',
        data: soapEnvelope,
        options: Options(
          headers: {
            'Content-Type': 'text/xml; charset=utf-8',
            'SOAPAction': 'http://tempuri.org/PostPermissionRequest',
          },
        ),
      );

      log(response.data.toString());

      if (response.statusCode == 200) {
        final parsedResponse = _parseSoapResponse(response.data);
        final resultText = parsedResponse['result'] as String? ?? '';
        if (resultText.startsWith('1')) {
          return ApiResponse(
            success: true,
            data: parsedResponse,
            message: 'Violation submitted successfully',
          );
        } else if (resultText.startsWith('2')) {
          return ApiResponse(
            success: false,
            data: parsedResponse,
            error: 'Invalid Input parameters',
            message: 'Invalid input parameters',
          );
        } else if (resultText.startsWith('5')) {
          return ApiResponse(
            success: false,
            data: parsedResponse,
            error: 'Permission Already Exist',
            message: 'Permission already exists',
          );
        }
        return ApiResponse(
          success: false,
          data: parsedResponse,
          error: 'Unknown error occurred',
          message: 'An unknown error occurred while submitting the violation',
        );
      } else {
        return ApiResponse(
          success: false,
          error: 'HTTP Error: ${response.statusCode}',
          message: 'Failed to submit violation',
        );
      }
    } on DioException catch (e) {
      return ApiResponse(
        success: false,
        error: _handleDioError(e),
        message: 'Network error occurred',
      );
    } catch (e) {
      return ApiResponse(
        success: false,
        error: e.toString(),
        message: 'An unexpected error occurred',
      );
    }
  }

  Map<String, dynamic> _parseSoapResponse(String responseData) {
    try {
      final document = XmlDocument.parse(responseData);
      final body = document.findAllElements('soap:Body').first;
      final response =
          body.findAllElements('PostPermissionRequestResponse').first;
      final result =
          response.findAllElements('PostPermissionRequestResult').first;

      return {
        'result': result.text,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'raw_response': responseData,
        'parse_error': e.toString(),
      };
    }
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout';
      case DioExceptionType.sendTimeout:
        return 'Send timeout';
      case DioExceptionType.receiveTimeout:
        return 'Receive timeout';
      case DioExceptionType.badResponse:
        return 'Bad response: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Request cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error';
      case DioExceptionType.unknown:
        return 'Unknown error: ${e.message}';
      default:
        return 'Network error occurred';
    }
  }
}
