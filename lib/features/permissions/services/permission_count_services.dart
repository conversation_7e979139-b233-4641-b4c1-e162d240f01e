import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';

import '../../authentication/models/logined_user.dart';

class PermissionCountServices {
  Dio dio = Dio();

  Future<Response> getViolationCount() async {
    final DateTime now = DateTime.now();
    final DateTime since = DateTime(2000, 1, 1);
    final String fromDate = DateFormat('dd/MM/yyyy').format(since);
    final String toDate = DateFormat('dd/MM/yyyy').format(now);
    final String xmlBody =
        '''<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:GetViolationCount>
         <!--Optional:-->
         <tem:pPersonId>${LoginedUser.employeeId}</tem:pPersonId>
         <!--Optional:-->
         <tem:pFromDate>$fromDate</tem:pFromDate>
         <!--Optional:-->
         <tem:pToDate>$toDate</tem:pToDate>
      </tem:GetViolationCount>
   </soapenv:Body>
</soapenv:Envelope>''';
    Response response = await dio.post(
      ApiConstants.endPointWebTAUrl,
      data: xmlBody,
      options: Options(
        headers: {
          'Content-Type': 'text/xml; charset=utf-8',
          'SOAPAction': '${ApiConstants.soapActionUrl}GetViolationCount',
        },
      ),
    );
    return response;
  }
}
