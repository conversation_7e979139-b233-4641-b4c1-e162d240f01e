import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';

import '../../authentication/models/logined_user.dart';

class ViolationsListServices {
  Dio dio = Dio();

  Future<Response> getViolationsList() async {
    final DateTime now = DateTime.now();
    final DateTime since = DateTime(2000, 1, 1);
    final String fromDate = DateFormat('dd/MM/yyyy').format(since);
    final String toDate = DateFormat('dd/MM/yyyy').format(now);
    final String xmlBody =
        '''<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:GetEmpViolations>
         <!--Optional:-->
         <tem:pPersonId>${LoginedUser.employeeId}</tem:pPersonId>
         <!--Optional:-->
         <tem:pFromDate>$fromDate</tem:pFromDate>
         <!--Optional:-->
         <tem:pToDate>$toDate</tem:pToDate>
      </tem:GetEmpViolations>
   </soapenv:Body>
</soapenv:Envelope>''';
    Response response = await dio.post(
      ApiConstants.endPointWebTAUrl,
      data: xmlBody,
      options: Options(
        headers: {
          'Content-Type': 'text/xml; charset=utf-8',
          'SOAPAction': '${ApiConstants.soapActionUrl}GetEmpViolations',
        },
      ),
    );
    return response;
  }
}
