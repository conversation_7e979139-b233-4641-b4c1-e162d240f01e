import 'dart:developer';

import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';
import 'package:xml/xml.dart';
import '../models/permission_request.dart';
import '../models/permission_response.dart';
import '../models/api_response.dart';

class PermissionService {
  late final Dio _dio;

  PermissionService() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConstants.endPointWebTAUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': 'http://tempuri.org/PostFuturePermRequest',
      },
    ));

    // Add interceptors for logging (optional)
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));
  }

  String _buildSoapEnvelope(PermissionRequest request) {
    return '''<?xml version="1.0" encoding="utf-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:PostFuturePermRequest>
         ${request.personId != null ? '<tem:pPersonId>${request.personId}</tem:pPersonId>' : ''}
         ${request.date != null ? '<tem:pDate>${request.date}</tem:pDate>' : ''}
         ${request.vStart != null ? '<tem:pVStart>${request.vStart}</tem:pVStart>' : ''}
         ${request.vEnd != null ? '<tem:pVEnd>${request.vEnd}</tem:pVEnd>' : ''}
         <tem:pAppType>${request.appType}</tem:pAppType>
         ${request.reqDesc != null ? '<tem:pReqDesc>${request.reqDesc}</tem:pReqDesc>' : ''}
      </tem:PostFuturePermRequest>
   </soapenv:Body>
</soapenv:Envelope>''';
  }

  Future<ApiResponse<PermissionResponse>> submitPermissionRequest(
      PermissionRequest request) async {
    try {
      final soapEnvelope = _buildSoapEnvelope(request);

      final response = await _dio.post(
        '',
        data: soapEnvelope,
        options: Options(
          headers: {
            'Content-Type': 'text/xml; charset=utf-8',
            'SOAPAction': 'http://tempuri.org/PostFuturePermRequest',
          },
        ),
      );

      if (response.statusCode == 200) {
        final permissionResponse = _parseSoapResponse(response.data);
        log(response.data.toString());
        return ApiResponse(
          success: permissionResponse.success,
          data: permissionResponse,
          message: permissionResponse.message,
        );
      } else {
        return ApiResponse(
          success: false,
          error: 'HTTP Error: ${response.statusCode}',
          message: 'Failed to submit permission request',
        );
      }
    } on DioException catch (e) {
      return ApiResponse(
        success: false,
        error: _handleDioError(e),
        message: 'Network error occurred',
      );
    } catch (e) {
      return ApiResponse(
        success: false,
        error: e.toString(),
        message: 'An unexpected error occurred',
      );
    }
  }

  PermissionResponse _parseSoapResponse(String responseData) {
    try {
      final document = XmlDocument.parse(responseData);

      // Look for the response in the SOAP body
      final body = document.findAllElements('soap:Body').first;
      final response =
          body.findAllElements('PostFuturePermRequestResponse').first;
      final result =
          response.findAllElements('PostFuturePermRequestResult').first;

      // Extract STATUS field from the result XML
      final statusElement = result.findAllElements('STATUS').isNotEmpty
          ? result.findAllElements('STATUS').first
          : null;
      int statusCode = int.tryParse(
              statusElement?.children.firstOrNull?.toString() ?? '0') ??
          0;
      // Extract result information
      final resultText = result.text;

      if (statusCode == 0) {
        return PermissionResponse(
          success: false,
          message: 'Failed to submit permission request',
          timestamp: DateTime.now(),
          rawData: {'rawResponse': responseData},
        );
      }
      if (statusCode == 2) {
        return PermissionResponse(
          success: false,
          message: 'Permission already exists',
          timestamp: DateTime.now(),
          rawData: {'rawResponse': responseData},
        );
      }

      return PermissionResponse(
        success: statusCode == 1,
        message: 'Permission request submitted successfully',
        requestId: _extractRequestId(resultText),
        timestamp: DateTime.now(),
        rawData: {
          'result': resultText,
          'fullResponse': responseData,
        },
      );
    } catch (e) {
      // If parsing fails, still consider it successful if we got a 200 response
      print('Warning: Could not parse SOAP response: $e');
      return PermissionResponse(
        success: false,
        message: 'Permission request submitted (response parsing incomplete)',
        timestamp: DateTime.now(),
        rawData: {
          'rawResponse': responseData,
          'parseError': e.toString(),
        },
      );
    }
  }

  String? _extractRequestId(String resultText) {
    // Try to extract request ID from result text if it contains one
    // This depends on the actual response format from your API
    try {
      if (resultText.contains('RequestId') || resultText.contains('ID')) {
        // Add logic here based on your actual response format
        return resultText;
      }
    } catch (e) {
      print('Could not extract request ID: $e');
    }
    return null;
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout';
      case DioExceptionType.sendTimeout:
        return 'Send timeout';
      case DioExceptionType.receiveTimeout:
        return 'Receive timeout';
      case DioExceptionType.badResponse:
        return 'Bad response: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Request cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error';
      case DioExceptionType.unknown:
        return 'Unknown error: ${e.message}';
      default:
        return 'Network error occurred';
    }
  }
}
