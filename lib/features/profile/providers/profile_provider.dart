import 'dart:developer';
import 'package:addc/features/customize_home/model/home_customize_model.dart';
import 'package:addc/features/home/<USER>/providers/leave_balance_provider.dart';
import 'package:addc/features/profile/services/profile_services.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:xml/xml.dart';

class ProfileProvider extends ChangeNotifier {
  bool isLoading = false;
  bool _isPushNotificationEnabled = false;
  bool get isPushNotificationEnabled => _isPushNotificationEnabled;
  set isPushNotificationEnabled(bool value) {
    _isPushNotificationEnabled = value;
    notifyListeners();
  }

  List<HomeCustomizeModel> homeCustomizationsTemp = [];
  List<HomeCustomizeModel> homeCustomizations = [
    HomeCustomizeModel(
        index: 0,
        icon: 'clock',
        title: 'Leave Balance',
        count: '0',
        isSelected: true),
    HomeCustomizeModel(
        index: 1,
        icon: 'calendar-2',
        title: 'Attendance',
        count: '0',
        isSelected: true),
    // HomeCustomizeModel(
    //     index: 2,
    //     icon: 'addc_news',
    //     title: 'ADDC News',
    //     count: '0',
    //     isSelected: false),
    // HomeCustomizeModel(
    //     index: 3,
    //     icon: 'attendance',
    //     title: 'Attendance',
    //     count: '0',
    //     isSelected: false),
    // HomeCustomizeModel(
    //     index: 4,
    //     icon: 'majlisna',
    //     title: 'Majlisna',
    //     count: '0',
    //     isSelected: false),
    HomeCustomizeModel(
        index: 5,
        icon: 'calendar',
        title: 'Leaves',
        count: '0',
        isSelected: true),
    // HomeCustomizeModel(
    //     index: 6,
    //     icon: 'permission',
    //     title: 'Attendance',
    //     count: '0',
    //     isSelected: false),
    // HomeCustomizeModel(
    //     index: 7,
    //     icon: 'my_info',
    //     title: 'My Info',
    //     count: '0',
    //     isSelected: false),
    // HomeCustomizeModel(
    //     index: 8,
    //     icon: 'apply_for_letter',
    //     title: 'Apply for Letter',
    //     count: '0',
    //     isSelected: false),
    // HomeCustomizeModel(
    //     index: 9,
    //     icon: 'complaint',
    //     title: 'Complaint',
    //     count: '0',
    //     isSelected: false),
  ];
  Future<void> saveHomeCustomizations() async {
    final prefs = await SharedPreferences.getInstance();
    homeCustomizationsTemp.map(
      (e) {
        if (e.index == 0) {
          prefs.setBool('clock', e.isSelected ?? false);
        }
        if (e.index == 1) {
          prefs.setBool('calendar-2', e.isSelected ?? false);
        }
        // if (e.index == 2) {
        //   prefs.setBool('addc_news', e.isSelected ?? false);
        // }
        // if (e.index == 3) {
        //   prefs.setBool('attendance', e.isSelected ?? false);
        // }
        // if (e.index == 4) {
        //   prefs.setBool('majlisna', e.isSelected ?? false);
        // }
        if (e.index == 5) {
          prefs.setBool('calendar', e.isSelected ?? false);
        }
        // if (e.index == 6) {
        //   prefs.setBool('permission', e.isSelected ?? false);
        // }
        // if (e.index == 7) {
        //   prefs.setBool('my_info', e.isSelected ?? false);
        // }
        // if (e.index == 8) {
        //   prefs.setBool('apply_for_letter', e.isSelected ?? false);
        // }
        // if (e.index == 9) {
        //   prefs.setBool('complaint', e.isSelected ?? false);
        // }
      },
    ).toList();
    // Deep copy using copyWith
    homeCustomizations =
        homeCustomizationsTemp.map((e) => e.copyWith()).toList();
    notifyListeners();
    // log('message --2 prefs.getBool('addc_news'));');
    // log('homeCustomizations - ${homeCustomizations.map((e) => e.isSelected).toList()}');
  }

  Future<void> getHomeCustomisationsForHome(
      {required BuildContext context}) async {
    final leaveBalanceProvider = context.read<LeaveBalanceProvider>();

    try {
      isLoading = true;
      String personalBalance = await getPersonalBalance();
      String leaveBalance = await leaveBalanceProvider.getLeaveBalance();
      // await Future.delayed(const Duration(milliseconds: 800));
      final prefs = await SharedPreferences.getInstance();
      log('personalBalance ----- $personalBalance');
      log('leaveBalance ----- $leaveBalance');
      homeCustomizations.map(
        (e) {
          switch (e.index) {
            case 0:
              // if (isTestVersion) {
              //   e.title = 'Leave Balance';
              // }
              e.count =
                  '$leaveBalance ${leaveBalance == '0' || leaveBalance == '1' ? 'Day' : ''}';
              e.isSelected = prefs.getBool('clock') ?? true;
              break;
            case 1:
              // if (isTestVersion) {
              //   e.title = 'Attendance';
              // }
              e.count = personalBalance;
              e.isSelected = prefs.getBool('calendar-2') ?? true;
              break;
            case 2:
              e.isSelected = prefs.getBool('addc_news');
              break;
            case 3:
              e.isSelected = prefs.getBool('attendance');
              break;
            case 4:
              e.isSelected = prefs.getBool('majlisna');
              break;
            case 5:
              e.isSelected = prefs.getBool('calendar') ?? true;
              break;
            case 6:
              e.isSelected = prefs.getBool('permission');
              break;
            case 7:
              e.isSelected = prefs.getBool('my_info');
              break;
            case 8:
              e.isSelected = prefs.getBool('apply_for_letter');
              break;
            case 9:
              e.isSelected = prefs.getBool('complaint');
              break;
          }
          isLoading = false;
        },
      ).toList();
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      isLoading = false;
      notifyListeners();
    }
    log('homeCustomizations - 2 ${homeCustomizations.map((e) => e.isSelected).toList()}');
  }

  Future<void> getHomeCustomisations({required BuildContext context}) async {
    // final leaveBalanceProvider = context.read<LeaveBalanceProvider>();

    try {
      isLoading = true;
      // String personalBalance = await getPersonalBalance();
      // String leaveBalance = await leaveBalanceProvider.getLeaveBalance();
      // await Future.delayed(const Duration(milliseconds: 800));
      final prefs = await SharedPreferences.getInstance();
      // log('personalBalance ----- $personalBalance');
      homeCustomizations.map(
        (e) {
          switch (e.index) {
            case 0:
              // if (isTestVersion) {
              //   e.title = 'Leave Balance';
              // }
              // e.count = leaveBalance;
              e.isSelected = prefs.getBool('clock') ?? true;
              break;
            case 1:
              // if (isTestVersion) {
              //   e.title = 'Attendance';
              // }
              // e.count = personalBalance;
              e.isSelected = prefs.getBool('calendar-2') ?? true;
              break;
            case 2:
              e.isSelected = prefs.getBool('addc_news');
              break;
            case 3:
              e.isSelected = prefs.getBool('attendance');
              break;
            case 4:
              e.isSelected = prefs.getBool('majlisna');
              break;
            case 5:
              e.isSelected = prefs.getBool('calendar') ?? true;
              break;
            case 6:
              e.isSelected = prefs.getBool('permission');
              break;
            case 7:
              e.isSelected = prefs.getBool('my_info');
              break;
            case 8:
              e.isSelected = prefs.getBool('apply_for_letter');
              break;
            case 9:
              e.isSelected = prefs.getBool('complaint');
              break;
          }
          isLoading = false;
        },
      ).toList();
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      isLoading = false;
      notifyListeners();
    }
    log('homeCustomizations - 2 ${homeCustomizations.map((e) => e.isSelected).toList()}');
  }

  homeCustomizationOnChanged({required HomeCustomizeModel item}) {
    // Count currently selected items
    int selectedCount =
        homeCustomizationsTemp.where((e) => e.isSelected == true).length;
    bool isSelected = item.isSelected ?? false;
    // Prevent deselecting the last selected item
    if (isSelected && selectedCount == 1) {
      showToast('Atleast one card should be select');
      // Optionally, show a message to the user here
      return;
    }
    item.isSelected = !isSelected;
    notifyListeners();
  }

  // Future<void> getProfile() async {
  //   // https://fa-ewzp-dev3-saasfaprod1.fa.ocs.oraclecloud.com:443/hcmRestApi/resources/11.13.18.05/emps/?offset=0&limit=5
  //   try {
  //     Dio dio = Dio();
  //     String apiUrl =
  //         '${ApiConstants.employeeDetails}/?q=PersonId=100000012074784';
  //     Response response = await dio.get(apiUrl,
  //         options: Options(headers: ApiConstants.authHeader()));
  //     if (kDebugMode) {}
  //     if (response.statusCode == 200) {
  //       Map<String, dynamic> json = response.data;
  //       if (json.containsKey('items')) {
  //         List data = json['items'] ?? [];
  //         final item = data.firstWhere(
  //             (element) => element['PersonId'].toString() == '100000012074784');
  //         Map<String, dynamic> tempData = {};
  //         String? phoneNumber;
  //         tempData['display_name'] = item['FirstName'];
  //         tempData['job_title'] = item['WorkEmail'];
  //         tempData['mail'] = item['WorkEmail'];
  //         if (item['WorkMobilePhoneCountryCode'] != null) {
  //           phoneNumber = item['WorkMobilePhoneCountryCode'];
  //         }
  //         if (item['WorkMobilePhoneNumber'] != null) {
  //           phoneNumber = '$phoneNumber${item['WorkMobilePhoneNumber']}';
  //         }
  //         tempData['mobile_phone'] = phoneNumber;
  //         // LoginedUser.fromJson(tempData);
  //       }
  //     }
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  //   notifyListeners();
  // }

  // Future<void> getAssignmentBatch() async {
  //   final dio = Dio();

  //   final parts = [
  //     {
  //       // "id": "get_assignment_${LoginedUser.employeeId}",
  //       "id": "get_assignment_47726",
  //       "path":
  //           "/assignments?q=PersonNumber=${LoginedUser.employeeId}", // relative path
  //       "operation": "GET"
  //     }
  //   ];

  //   final payload = {"parts": parts};
  //   try {
  //     final response = await dio.post(
  //       'https://fa-ewzp-dev3-saasfaprod1.fa.ocs.oraclecloud.com/hcmRestApi/resources/latest/batch',
  //       data: payload,
  //       options: Options(headers: ApiConstants.authHeader()),
  //     );
  //     log('response.data ----> ${response.data}');
  //   } catch (e) {
  //     log('Error: $e');
  //   }
  // }

  /// Fetches the personal balance and logs the PERSONALBALANCE value from the SOAP response
  Future<String> getPersonalBalance() async {
    try {
      final response = await ProfileServices().getPersonalBalance();
      log('PERSONALBALANCE:-1 ${response.data} - ');
      log('PERSONALBALANCE:-2 ${response.realUri} ');
      log('PERSONALBALANCE:-3 ${response.statusCode}');

      if (response.statusCode == 200 && response.data != null) {
        final document = XmlDocument.parse(response.data);
        // Navigate to the EmpBalance element
        final empBalanceElement =
            document.findAllElements('EmpBalance').firstOrNull;
        if (empBalanceElement != null) {
          final personalBalance = empBalanceElement
                  .findElements('PERSONALBALANCE')
                  .firstOrNull
                  ?.innerText ??
              '0 Days';
          log('PERSONALBALANCE 1: $personalBalance ');
          // Use the value as needed here
          return '$personalBalance Personal Hours';
        } else {
          log('EmpBalance element not found');
          return '';
        }
      } else {
        log('Failed to fetch personal balance: ${response.statusCode}');
        return '';
      }
    } catch (e) {
      log('Error fetching personal balance: $e');
    }
    return '';
  }
}
