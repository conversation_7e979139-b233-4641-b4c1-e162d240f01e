import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/authentication/services/biometric_auth_service.dart';
import 'package:addc/features/profile/view/sections/profile_avatar_section.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
// import 'package:addc/widgets/common_bottom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../model/my_profile_tile_model.dart';
import 'widgets/my_profile_tile.dart';

class ProfileScreen extends StatefulWidget {
  static const route = '/profile_screen';
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final BiometricAuthService bioServices = BiometricAuthService();
  List<MyProfileTileModel> items = [
    MyProfileTileModel(icon: 'my_business_card', title: 'My Business Card'),
    MyProfileTileModel(
        icon: 'customize_home_screen', title: 'Customize Home Screen'),
    MyProfileTileModel(
        icon: 'terms_and_conditions', title: 'Terms & Conditions'),
    MyProfileTileModel(icon: 'privacy_policies', title: 'Privacy Policies'),
    MyProfileTileModel(
        icon: 'push_notifications',
        title: 'Push Notifications',
        isPushNotificationTile: true),
    // MyProfileTileModel(icon: 'contact_us', title: 'Contact Us'),
    MyProfileTileModel(icon: 'logout', title: 'Log Out'),
  ];
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await bioServices.isDeviceSupported();
      if (bioServices.supportState == SupportState.supported) {
        items.insert(
            4,
            MyProfileTileModel(
                icon: 'finger-scan',
                title: 'Biometric Login',
                isBiometricTile: true));
        items.toSet().toList();
      }
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Profile'),
        leading: const CommonBackButton(),
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.only(
                  left: 12.w, right: 12.w, bottom: 20.h, top: 20.h),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: ColorConstants.colorFFFFFF,
                  borderRadius: BorderRadius.circular(18.r)),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    H(25),
                    Column(
                      children: [
                        Align(
                            alignment: Alignment.center,
                            child: const ProfileAvatarSection()),
                        H(16),
                        Text(LoginedUser.displayName ?? '',
                            style: ts20c000940w4h1),
                        H(4),
                        Text(LoginedUser.jobTitle ?? '',
                            style: ts14c9D9D9Dw4h1),
                        // H(10),
                        // TextButton(
                        //     onPressed: () async {},
                        //     child:
                        //         Text('Edit Profile', style: ts14c000940w4h1U)),
                        H(10),
                      ],
                    ),
                    // const Spacer(),
                    ListView.separated(
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                      shrinkWrap: true,
                      itemCount: items.length,
                      // itemCount: 2,
                      itemBuilder: (context, index) {
                        final item = items[index];
                        return MyProfileTile(index: index, item: item);
                      },
                      separatorBuilder: (context, index) => Container(
                        height: 0.5.h,
                        color:
                            ColorConstants.colorD9D9D9.withValues(alpha: 0.5),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      // bottomNavigationBar: const CommonBottomAppbar(),
    );
  }
}
