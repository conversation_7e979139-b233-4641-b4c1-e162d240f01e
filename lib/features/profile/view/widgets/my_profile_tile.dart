import 'dart:developer';

import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/find%20people/models/find_people_model.dart';
import 'package:addc/features/more/providers/more_provider.dart';
import 'package:addc/features/privacy_policy/views/privacy_policies_screen.dart';
import 'package:addc/features/privacy_policy/views/terms_and_condition_screen.dart';
import 'package:addc/features/profile/providers/profile_provider.dart';
import 'package:addc/features/profile/view/widgets/logout_bottom_sheet.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';
// import '../../../contact_us/view/contact_us_screen.dart';
import '../../../customize_home/view/customize_home_screen.dart';
import '../../../my_business_card/view/my_business_card_screen.dart';
import '../../../widgets/gap.dart';
import '../../model/my_profile_tile_model.dart';

class MyProfileTile extends StatelessWidget {
  final int index;
  final MyProfileTileModel item;
  const MyProfileTile({super.key, required this.index, required this.item});

  @override
  Widget build(BuildContext context) {
    String icon = item.icon ?? '';
    String title = item.title ?? '';
    bool isPushNotificationTile = item.isPushNotificationTile ?? false;
    bool isBiometricTile = item.isBiometricTile ?? false;
    return InkWell(
      onTap: () => _onTap(context: context, index: index),
      child: Padding(
        padding: EdgeInsets.only(bottom: 12.h, top: 15.h),
        child: Row(
          children: [
            Container(
              height: 42.h,
              width: 42.h,
              padding: EdgeInsets.all(11),
              decoration: BoxDecoration(
                  color: ColorConstants.colorC1C1C1.withValues(alpha: 0.1),
                  shape: BoxShape.circle),
              child: SvgPicture.asset(icon.asIconSvg()),
            ),
            W(10),
            Text(title, style: ts16c000940w4h1),
            const Spacer(),
            if (isPushNotificationTile)
              Transform.scale(
                scale: 0.7,
                child: Consumer<ProfileProvider>(
                  builder: (context, provider, _) {
                    return CupertinoSwitch(
                      value: provider.isPushNotificationEnabled,
                      onChanged: (value) => _onSwitchChanged(context: context),
                      activeTrackColor: ColorConstants.color0AB3A1,
                    );
                  },
                ),
              ),
            if (isBiometricTile)
              Consumer<MoreProvider>(
                builder: (context, moreProvider, child) {
                  return Transform.scale(
                    scale: 0.7,
                    child: CupertinoSwitch(
                      activeTrackColor: ColorConstants.color0AB3A1,
                      value: moreProvider.switchValue,
                      onChanged: moreProvider.onToggledBiometrics,
                    ),
                  );
                },
              )
          ],
        ),
      ),
    );
  }

  _onTap({required BuildContext context, required int index}) {
    switch (index) {
      case 0:
        log('${LoginedUser.displayName} - ${LoginedUser.mail} - ${LoginedUser.jobTitle} - ${LoginedUser.mobilePhone}');
        Navigator.pushNamed(context, MyBusinessCardScreen.route,
            arguments: MyBusinessCardScreen(
                item: FindPeopleModel(
                    displayName: LoginedUser.displayName,
                    workMail: LoginedUser.mail,
                    assignment: LoginedUser.jobTitle,
                    workPhoneNumber: LoginedUser.mobilePhone)));
        break;
      case 1:
        Navigator.pushNamed(context, CustomizeHomeScreen.route);
        break;
      case 2:
        Navigator.pushNamed(context, TermsAndConditionScreen.route);
        break;
      case 3:
        Navigator.pushNamed(context, PrivacyPoliciesScreen.route);
        break;
      case 4:
        break;
      // case 5:
      //   break;
      // case 6:
      //   Navigator.pushNamed(context, ContactUsScreen.route);
      //   break;
      case 6:
        showModalBottomSheet(
            backgroundColor: Colors.transparent,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(30.r))),
            context: context,
            builder: (builder) => LogoutBottomSheet());
        break;
    }
  }

  _onSwitchChanged({required BuildContext context}) {
    final provider = context.read<ProfileProvider>();
    provider.isPushNotificationEnabled = !provider.isPushNotificationEnabled;
  }
}
