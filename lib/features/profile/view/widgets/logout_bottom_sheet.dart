import 'package:addc/features/authentication/providers/login_provider.dart';
import 'package:addc/features/authentication/view/login_screen.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';

class LogoutBottomSheet extends StatelessWidget {
  const LogoutBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    String bgImage = 'warning_1';

    return Padding(
      padding: EdgeInsets.all(12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ConvexAppBar(
            curveSize: 100,
            cornerRadius: 30.r,
            initialActiveIndex: 0,
            style: TabStyle.fixed,
            curve: Curves.decelerate,
            elevation: 0,
            backgroundColor: ColorConstants.colorFFFFFF,
            items: [
              TabItem(
                icon: SvgPicture.asset(bgImage.asIconSvg(),
                    height: 90.h, width: 90.w),
              )
            ],
          ),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
                color: ColorConstants.colorFFFFFF,
                borderRadius:
                    BorderRadius.vertical(bottom: Radius.circular(30.r))),
            child: Column(
              children: [
                H(10),
                Text(
                  'Are you sure you want to\nlog out',
                  style: ts26c000940w4h1,
                  textAlign: TextAlign.center,
                ),
                H(18.65),
                Text(
                  'By logging out you will be ending your\ncurrent session.',
                  style: ts14c9D9D9Dw4h1,
                  textAlign: TextAlign.center,
                ),
                H(20),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 22.53.w),
                  child: Row(
                    spacing: 14.08.w,
                    children: [
                      Expanded(
                        child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                                minimumSize: Size(double.infinity, 50.h),
                                backgroundColor: ColorConstants.color8B8D97),
                            onPressed: () => Navigator.pop(context),
                            child: Text('Cancel')),
                      ),
                      Expanded(
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              minimumSize: Size(double.infinity, 50.h)),
                          child: Text('Logout'),
                          onPressed: () => _onLogout(context: context),
                        ),
                      ),
                    ],
                  ),
                ),
                H(20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _onLogout({required BuildContext context}) async {
    final provider = context.read<LogInProvider>();
    await provider.logout();
    if (!context.mounted) return;
    Navigator.pushNamedAndRemoveUntil(
        context, LoginScreen.route, (route) => false);
    // Phoenix.rebirth(context);
  }
}
