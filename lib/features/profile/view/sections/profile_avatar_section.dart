import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/authentication/providers/login_provider.dart';
import 'package:addc/features/master_screen/widgets/user_avatar.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../shared/constants/color_constants.dart';

class ProfileAvatarSection extends StatelessWidget {
  const ProfileAvatarSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<LogInProvider>(
      builder: (context, provider, _) {
        return UserAvatar(
          diameter: 120,
          borderWidth: 2,
          imageProvider: provider.imageProvider,
          name: LoginedUser.displayName,
          nameFontSize: 30,
        );
        // return CircleAvatar(
        //   radius: 1,
        //   // backgroundColor: ColorConstants.primaryColor,
        //   // foregroundColor: provider.imageProvider != null
        //   //     ? ColorConstants.primaryColor
        //   //     : null,
        //   foregroundImage: provider.imageProvider ?? AssetImage(''),
        //   // AssetImage('john_big'.asDummyPng()),
        //   // backgroundImage: AssetImage('john_big'.asDummyPng()),
        //   onForegroundImageError: (exception, stackTrace) {},
        //   // onBackgroundImageError: (exception, stackTrace) {},
        //   child: Text(LoginedUser.displayName != null
        //       ? LoginedUser.displayName![0].toUpperCase()
        //       : ''),
        // );
      },
    );
    return SizedBox(
      width: 94.h,
      height: 94.h,
      child: Stack(
        fit: StackFit.passthrough,
        alignment: Alignment.bottomRight,
        children: [
          Consumer<LogInProvider>(
            builder: (context, provider, _) {
              return CustomAvatar(
                radius: 90.h / 2,
                name: LoginedUser.displayName,
                image: provider.imageProvider ?? AssetImage(''.asDummyPng()),
              );
            },
          ),

          // Align(
          //   alignment: Alignment.bottomRight,
          //   child: SizedBox(
          //     height: 30.h,
          //     width: 30.h,
          //     child: FloatingActionButton.small(
          //       backgroundColor: ColorConstants.color000940,
          //       shape: CircleBorder(
          //           side: BorderSide(
          //               color: ColorConstants.colorFFFFFF, width: 1)),
          //       onPressed: () async {
          //         await onTryToPickImage(context: context);
          //       },
          //       child: Padding(
          //         padding: const EdgeInsets.all(8.17),
          //         child: Image.asset('camera_outlined'.asIconPng()),
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}

class CustomAvatar extends StatelessWidget {
  final ImageProvider? image;
  final String? name;
  final double? radius;
  final double? fontSize;
  final double? borderWidth;
  const CustomAvatar(
      {super.key,
      this.image,
      this.name,
      this.radius,
      this.fontSize,
      this.borderWidth});

  @override
  Widget build(BuildContext context) {
    String? subName;
    if (name != null && name!.isNotEmpty) {
      subName = name?[0].toUpperCase();
    }
    return Container(
      padding: EdgeInsets.all(1),
      decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 1)],
          color: ColorConstants.colorFFFFFF,
          border: Border.all(
              width: borderWidth ?? 0.5, color: ColorConstants.colorF9F9F9)),
      child: Consumer<LogInProvider>(
        builder: (context, provider, _) {
          return CircleAvatar(
            radius: radius,
            foregroundImage: image,
            child: subName != null
                ? Text(
                    subName,
                    style: TextStyle(fontSize: fontSize ?? 30),
                  )
                : null,
          );
        },
      ),
    );
  }
}
