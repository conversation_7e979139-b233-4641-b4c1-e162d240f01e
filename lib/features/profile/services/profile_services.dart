import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/utils/urls.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:dio/dio.dart';

class ProfileServices {
  final dio = Dio();
  Future<Response> getPersonalBalance() async {
    final formattedDate = DateFormatter.formatDateTime(
        dateTime: DateTime.now(), outputFormat: 'dd/MM/yyyy');
    final String xmlBody =
        '''<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:GetEmpBalance>
         <!--Optional:-->
         <tem:pPersonId>${LoginedUser.employeeId}</tem:pPersonId>
         <!--Optional:-->
         <tem:pDate>$formattedDate</tem:pDate>
      </tem:GetEmpBalance>
   </soapenv:Body>
</soapenv:Envelope>''';

    Response response = await dio.post(
      ApiConstants.endPointWebTAUrl,
      data: xmlBody,
      options: Options(
        headers: {
          'Content-Type': 'text/xml; charset=utf-8',
          'SOAPAction': '${ApiConstants.soapActionUrl}GetEmpBalance',
        },
      ),
    );
    return response;
  }
}
