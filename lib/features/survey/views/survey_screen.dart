import 'package:addc/features/survey/widgets/survey_container.dart';
import 'package:addc/features/survey/widgets/survey_tabbar.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SurveyScreen extends StatelessWidget {
   static const route = '/survey-screen';
  const SurveyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:AppBar(
        centerTitle: true,
        title: Text(
          'Survey',
          style: ts20c000940w4,
        ),
        leading: const CommonBackButton(),
      ), 
      body: Padding(
        padding: EdgeInsets.only(left: 12.w, top: 12.h,right: 12.w,),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
              H(10.h),
            SurveyTabbar(),
            H(20),
            Expanded(child: SurveyContainer())
          ],
        ),
      ),
    );
  }
}