
import 'package:addc/features/survey/providers/survey_provider.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SurveyTabbar extends StatelessWidget {
  const SurveyTabbar({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              
              children: [
                Consumer<SurveyProvider>(
                  builder: (context, surveyProvider, child) {
                    return Container(
                      decoration: BoxDecoration(),
                      height: 36,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: surveyProvider.categories.length,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return GestureDetector(
                            onTap: () =>
                                surveyProvider.onCategoryTap(index),
                            child: Container(
                              margin: EdgeInsets.only(right: 10),
                              padding: EdgeInsets.symmetric(
                                horizontal: 10,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    surveyProvider.selectedCategoryIndex ==
                                            index
                                        ? ColorConstants.color000940
                                        : Colors.white,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                surveyProvider.categories[index],
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    color: surveyProvider
                                                .selectedCategoryIndex ==
                                            index
                                        ? Colors.white
                                        : ColorConstants.color111111),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
          );
  }
}