import 'package:addc/features/inbox/widgets/status_button.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SurveyTiles extends StatelessWidget {
  final Color buttonColor;
  final String buttonText;
  final String? userimage;
  final String userName;
  final String date;
  const SurveyTiles(
      {super.key,
      required this.buttonColor,
      required this.buttonText,
      required this.userName,
      required this.date,
      this.userimage});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "CATERGORY",
                  style: ts12c9D9D9Dw4,
                ),
                Text(
                  "Survey Name",
                  style: ts14c000940w4,
                ),
              ],
            ),
            StatusButton(color: buttonColor, text: buttonText),
          ],
        ),
        H(10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 4.w),
                decoration: BoxDecoration(color: ColorConstants.colorF9F9FF),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text(
                          "Created by",
                          style: ts10c9D9D9Dw4,
                        ),
                        W(4),
                        Image.asset(
                          '$userimage'.asImagePng(),
                          scale: 2,
                          height: 14.h,
                          width: 14.w,
                        ),
                        W(4),
                        Text(
                          userName,
                          style: ts12c000940w4,
                        ),
                      ],
                    ),
                    Text(
                      date,
                      style: ts10c9D9D9Dw4,
                    )
                  ],
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
}
