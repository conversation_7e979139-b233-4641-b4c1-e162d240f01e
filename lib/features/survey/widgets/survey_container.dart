import 'package:addc/features/inbox/widgets/custom_search_bar.dart';
import 'package:addc/features/survey/models/survey_model.dart';
import 'package:addc/features/survey/widgets/survey_tiles.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_divider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// ignore: must_be_immutable
class SurveyContainer extends StatelessWidget {
  SurveyContainer({super.key});

  TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final List<SurveyModels> surveyModel=[
      SurveyModels(buttonColor: ColorConstants.color0C902F,date:'01 Jan,2025'),
      SurveyModels(buttonColor: ColorConstants.colorFF453F,date:'01 Jan,2025' ,),
      SurveyModels(buttonColor: ColorConstants.colorFF453F,date:'01 Jan,2025'),
      SurveyModels(buttonColor: ColorConstants.colorFF453F,date:'01 Jan,2025'),
      SurveyModels(buttonColor: ColorConstants.colorFF453F,date:'01 Jan,2025' ),
      SurveyModels(buttonColor: ColorConstants.colorFF453F,date:'01 Jan,2025')
    ];
    return Container(
      decoration: BoxDecoration(
        color: ColorConstants.colorFFFFFF,
        borderRadius: BorderRadius.circular(18),
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 14.h, left: 12.w, right: 12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomSearchBar(
              controller: searchController,
            ),
            H(25),
            Text(
              "All Surveys",
              style: ts16c000940w4,
            ),
            H(15),
            Expanded(
              child: ListView.separated(
                separatorBuilder: (context, index) {
                  return Column(
                    children: [
                      H(10),
                      CommonDivider(),
                       H(10),
                    ],
                  );
                },
                itemCount: surveyModel.length,
                itemBuilder: (context, index) {
                  return SurveyTiles(
                    userName: 'Khaled bin Ali',
                    userimage: 'user-image',
                    buttonColor: surveyModel[index].buttonColor??Colors.transparent,
                    buttonText: 'In Progress',
                    date: '01 Jan,2025',
                  );
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
