import 'package:flutter/material.dart';
import 'package:addc/features/authentication/services/intune_service.dart';

class IntuneTestScreen extends StatefulWidget {
  static const String route = '/intune-test';
  
  const IntuneTestScreen({super.key});

  @override
  State<IntuneTestScreen> createState() => _IntuneTestScreenState();
}

class _IntuneTestScreenState extends State<IntuneTestScreen> {
  String _testResults = 'Tap "Run Test" to check Intune integration';
  bool _isLoading = false;

  Future<void> _runIntuneTest() async {
    setState(() {
      _isLoading = true;
      _testResults = 'Running Intune integration test...';
    });

    try {
      // Run the comprehensive integration test
      await IntuneService.printIntegrationStatus();
      
      // Get detailed results
      final Map<String, dynamic> results = await IntuneService.testIntuneIntegrationDetailed();
      
      // Format results for display
      StringBuffer buffer = StringBuffer();
      buffer.writeln('=== INTUNE INTEGRATION TEST RESULTS ===\n');
      
      bool sdkAvailable = results['sdkAvailable'] ?? false;
      bool userInfoAvailable = results['userInfoAvailable'] ?? false;
      String? primaryUser = results['primaryUser'];
      
      if (sdkAvailable && userInfoAvailable && primaryUser != null && primaryUser != 'null') {
        buffer.writeln('✅ STATUS: FULLY INTEGRATED');
        buffer.writeln('✅ User is enrolled: $primaryUser');
      } else if (sdkAvailable) {
        buffer.writeln('⚠️ STATUS: PARTIALLY INTEGRATED');
        buffer.writeln('⚠️ SDK works but user not enrolled');
      } else {
        buffer.writeln('❌ STATUS: INTEGRATION FAILED');
        if (results.containsKey('error')) {
          buffer.writeln('❌ Error: ${results['error']}');
        }
      }
      
      buffer.writeln('\n📊 DETAILED RESULTS:');
      buffer.writeln('• SDK Available: $sdkAvailable');
      buffer.writeln('• UserInfo Available: $userInfoAvailable');
      buffer.writeln('• Primary User: ${primaryUser ?? 'null'}');
      
      if (results.containsKey('error')) {
        buffer.writeln('• Error: ${results['error']}');
        buffer.writeln('• Error Code: ${results['errorCode'] ?? 'N/A'}');
      }
      
      buffer.writeln('\n📋 CONFIGURATION:');
      buffer.writeln('• Method Channel: com.taqadistribution/intune');
      buffer.writeln('• Android Package: com.taqadistribution.employee');
      buffer.writeln('• MainActivity: FlutterFragmentActivity');
      buffer.writeln('• Manifest: MAMApplication configured');
      
      buffer.writeln('\n💡 NEXT STEPS:');
      if (sdkAvailable && userInfoAvailable && primaryUser != null && primaryUser != 'null') {
        buffer.writeln('• Integration is working perfectly!');
        buffer.writeln('• User is enrolled and policies should apply');
      } else if (sdkAvailable) {
        buffer.writeln('• SDK integration is working');
        buffer.writeln('• User needs to enroll via Company Portal');
        buffer.writeln('• Check if app protection policies are applied');
      } else {
        buffer.writeln('• Check Android logs for detailed errors');
        buffer.writeln('• Verify Intune SDK is properly included');
        buffer.writeln('• Ensure MAMApplication is configured in manifest');
      }
      
      setState(() {
        _testResults = buffer.toString();
      });
      
    } catch (e) {
      setState(() {
        _testResults = '❌ Test failed with error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testEnrolledAccount() async {
    setState(() {
      _isLoading = true;
      _testResults = 'Testing enrolled account...';
    });

    try {
      final String? enrolledAccount = await IntuneService.getEnrolledAccount();
      
      setState(() {
        if (enrolledAccount != null && enrolledAccount.isNotEmpty) {
          _testResults = '✅ Enrolled Account Found:\n$enrolledAccount';
        } else {
          _testResults = '⚠️ No enrolled account found.\nThis could mean:\n• User is not enrolled in Intune\n• Company Portal is not configured\n• App Protection Policy is not applied';
        }
      });
    } catch (e) {
      setState(() {
        _testResults = '❌ Error getting enrolled account: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Intune SDK Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Intune SDK Integration Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _runIntuneTest,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: _isLoading 
                ? const CircularProgressIndicator(color: Colors.white)
                : const Text('Run Full Integration Test'),
            ),
            
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testEnrolledAccount,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Test Enrolled Account Only'),
            ),
            
            const SizedBox(height: 20),
            
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[50],
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResults,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 10),
            
            const Text(
              'Note: Check the console logs for detailed information',
              style: TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
