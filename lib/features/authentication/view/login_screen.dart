// ignore_for_file: use_build_context_synchronously

import 'package:addc/features/authentication/view/sections/login_footer_section.dart';
import 'package:addc/features/authentication/view/widgets/corporate_single_sign_button.dart';
import 'package:addc/features/authentication/view/widgets/or_divider.dart';
import 'package:addc/features/authentication/view/widgets/password_text_form_field.dart';
import 'package:addc/features/authentication/view/widgets/username_text_form_field.dart';
import 'package:addc/features/master_screen/providers/master_provider.dart';
import 'package:addc/features/master_screen/view/master_screen.dart';
import 'package:addc/features/authentication/providers/login_provider.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/services/firebase_remote_config_service.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class LoginScreen extends StatefulWidget {
  static const route = '/login_screen';
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  late LogInProvider _provider;
  late MasterProvider _masterProvider;
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _userNameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final FocusNode _usernameFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _provider = context.read<LogInProvider>();
    _masterProvider = context.read<MasterProvider>();
    _provider.userNameErrorMessage = null;
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        _provider.isObscureEnabled = true;

        // Ensure Firebase Remote Config is initialized
        // Note: This is already done in SplashScreen, but we're adding it here as a fallback
        if (!FirebaseRemoteConfigService.instance.isInitialized) {
          FirebaseRemoteConfigService.instance.initialize().then((_) {
            FirebaseRemoteConfigService.instance.fetchAndActivate().then((_) {
              // Force a rebuild to apply the remote config value
              if (mounted) setState(() {});
            });
          });
        }
      },
    );
    _passwordFocusNode.addListener(_onPasswordFocus);
  }

  void _onPasswordFocus() {
    if (_passwordFocusNode.hasFocus) {
      Future.delayed(const Duration(milliseconds: 200), () {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  @override
  void dispose() {
    _userNameController.dispose();
    _passwordController.dispose();
    _usernameFocusNode.dispose();
    _passwordFocusNode.removeListener(_onPasswordFocus);
    _passwordFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        leadingWidth: 56.h,
        title: const Text('Log In'),
        leading: CommonBackButton(backgroundColor: ColorConstants.colorEFF1FF),
        backgroundColor: ColorConstants.colorEFF1FF,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Column(
            children: [
              SizedBox(height: 12.h),
              Container(
                margin: EdgeInsets.only(left: 12.w, right: 12.w, top: 6.h),
                padding: EdgeInsets.only(left: 12.w, right: 12.w),
                decoration: BoxDecoration(
                  color: ColorConstants.colorFFFFFF,
                  borderRadius: BorderRadius.circular(18.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 24),
                    Text("Welcome\nBack!", style: ts40c000940w4),
                    const CorporateSingleSignButton(),

                    // Manual login section - only show if enabled in Remote Config
                    if (FirebaseRemoteConfigService
                        .instance.showManualLogin) ...[
                      const OrDividerWidget(),
                      const SizedBox(height: 16),
                      Text("Enter your credentials manually",
                          style: ts14c242424w4),
                      const SizedBox(height: 16),
                      Consumer<LogInProvider>(
                        builder: (context, provider, _) {
                          return UsernameTextFormField(
                            controller: _userNameController,
                            errorMessage: provider.userNameErrorMessage,
                            focusNode: _usernameFocusNode,
                            keyboardAction: TextInputAction.next,
                            onFieldSubmitted: (_) {
                              FocusScope.of(context)
                                  .requestFocus(_passwordFocusNode);
                            },
                          );
                        },
                      ),
                      H(15),
                      PasswordTextFormField(
                        controller: _passwordController,
                        focusNode: _passwordFocusNode,
                        keyboardAction: TextInputAction.done,
                        onFieldSubmitted: (_) {
                          FocusScope.of(context).unfocus();
                        },
                      ),
                      H(10),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () async {
                          if (kDebugMode) {
                            _userNameController.text = '<EMAIL>';
                            _passwordController.text = 'User@#12345###';
                          }
                          // TODO: This fuction is hardcoded for deploying the app on the Play Store and App Store, beacause of there is no testing account for the app.
                          if (!_formKey.currentState!.validate()) return;
                          final isSuccess =
                              await _provider.signInWithUsernamePassword(
                            username: _userNameController.text,
                            password: _passwordController.text,
                            context: context,
                          );
                          if (!isSuccess) return;
                          _provider.userNameErrorMessage = null;
                          _userNameController.clear();
                          _passwordController.clear();
                          _formKey.currentState!.reset();
                          _masterProvider.masterIndex = 0;
                          Navigator.pushNamedAndRemoveUntil(
                              context, MasterScreen.route, (route) => false);
                        },
                        child: Text("Log In"),
                      ),
                    ],

                    const SizedBox(height: 16),
                    const LoginFooterSection(),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
              // H(24),
              // Container(
              //     height: 30.h,
              //     width: double.infinity,
              //     color: ColorConstants.colorFFFFFF),
            ],
          ),
        ),
      ),
    );
  }
}
