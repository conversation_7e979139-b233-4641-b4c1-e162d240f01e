import 'package:addc/features/privacy_policy/views/privacy_policies_screen.dart';
import 'package:addc/features/privacy_policy/views/terms_and_condition_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';

class LoginFooterSection extends StatelessWidget {
  const LoginFooterSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkResponse(
                  onTap: () {
                    Navigator.pushNamed(context, PrivacyPoliciesScreen.route);
                  },
                  child: Text('Privacy Policies', style: ts12c9D9D9Dw4)),
              W(8),
              Text('|', style: ts12c9D9D9Dw4),
              W(8),
              InkResponse(
                  onTap: () {
                    Navigator.pushNamed(context, TermsAndConditionScreen.route);
                  },
                  child: Text("Terms & Condition", style: ts12c9D9D9Dw4)),
            ],
          ),
          H(8.h),
          Text("© 2025 TAQA Distribution", style: ts12c9D9D9Dw4),
        ],
      ),
    );
  }
}
