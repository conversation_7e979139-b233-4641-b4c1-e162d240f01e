import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../widgets/common_text_form_field.dart';

class UsernameTextForm<PERSON>ield extends StatelessWidget {
  final TextEditingController controller;
  final String? errorMessage;
  final FocusNode? focusNode;
  final TextInputAction? keyboardAction;
  final void Function(String)? onFieldSubmitted;
  const UsernameTextFormField({
    super.key,
    required this.controller,
    this.errorMessage,
    this.focusNode,
    this.keyboardAction,
    this.onFieldSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return CommonTextFormField(
      title: 'Username*',
      hintText: 'Enter your username',
      controller: controller,
      prefixIcon: Padding(
        padding:
            EdgeInsets.only(left: 16.w, top: 14.h, bottom: 14.h, right: 11.w),
        child: SvgPicture.asset('user'.asIconSvg()),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      keyboardType: TextInputType.emailAddress,
      errorText: errorMessage,
      validator: (value) {
        if (value!.isEmpty) {
          return 'You must provide a username';
        }
        if (!RegExp(
                r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+\-/=?^_`{|}~ ]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
            .hasMatch(value)) {
          return 'A valid username required';
        }
        return null;
      },
      focusNode: focusNode,
      textInputAction: keyboardAction,
      onFieldSubmitted: onFieldSubmitted,
    );
  }
}
