import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';
import '../../../widgets/gap.dart';

class OrDividerWidget extends StatelessWidget {
  const OrDividerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1.h,
            color: ColorConstants.colorD7D7D7.withValues(alpha: 0.5),
          ),
        ),
        W(18.w),
        Text("Or", style: ts14c000940w4),
        W(18.w),
        Expanded(
          child: Container(
            height: 1.h,
            color: ColorConstants.colorD7D7D7.withValues(alpha: 0.5),
          ),
        ),
      ],
    );
  }
}
