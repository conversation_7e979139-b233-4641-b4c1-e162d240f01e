import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

import '../../../../shared/constants/color_constants.dart';
import '../../../../widgets/common_text_form_field.dart';
import '../../providers/login_provider.dart';

class PasswordTextFormField extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode? focusNode;
  final TextInputAction? keyboardAction;
  final void Function(String)? onFieldSubmitted;
  const PasswordTextFormField({
    super.key,
    required this.controller,
    this.focusNode,
    this.keyboardAction,
    this.onFieldSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LogInProvider>(
      builder: (context, provider, _) {
        return CommonTextFormField(
          hintText: 'Enter your password',
          title: 'Password*',
          controller: controller,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          validator: (value) {
            // RegExp uppercaseRegex = RegExp(r'[A-Z]');
            // RegExp lowercaseRegex = RegExp(r'[a-z]');
            // RegExp digitRegex = RegExp(r'[0-9]');
            // RegExp specialCharRegex = RegExp(r'[!@#\$&*~]');
            if (value!.isEmpty) {
              return 'Please enter your password';
            }
            //  else if (!uppercaseRegex.hasMatch(value)) {
            //   return 'Password should have at least one uppercase letter';
            // } else if (!lowercaseRegex.hasMatch(value)) {
            //   return 'Password should have at least one lowercase letter';
            // } else if (!digitRegex.hasMatch(value)) {
            //   return 'Password should have at least one digit';
            // } else if (!specialCharRegex.hasMatch(value)) {
            //   return 'Password should have at least one special character';
            // } else if (value.length < 8) {
            //   return 'Password should be at least 8 characters long';
            // }
            else {
              return null;
            }
          },
          // errorText: provider.passwordErrorMessage,
          obscureText: provider.isObscureEnabled,
          prefixIcon: Padding(
            padding: EdgeInsets.only(
                left: 16.w, top: 14.h, bottom: 14.h, right: 11.w),
            child: SvgPicture.asset('lock'.asIconSvg()),
          ),
          suffixIcon: Transform.scale(
            scale: .42,
            child: GestureDetector(
              onTap: () =>
                  provider.isObscureEnabled = !provider.isObscureEnabled,
              child: SvgPicture.asset(
                !provider.isObscureEnabled
                    ? 'assets/svg/unhide.svg'
                    : 'assets/svg/hide.svg',
                colorFilter: ColorFilter.mode(
                    ColorConstants.color9D9D9D, BlendMode.srcIn),
              ),
            ),
          ),
          focusNode: focusNode,
          textInputAction: keyboardAction,
          onFieldSubmitted: onFieldSubmitted,
        );
      },
    );
  }
}
