import 'package:addc/features/authentication/providers/login_provider.dart';
import 'package:addc/features/master_screen/providers/master_provider.dart';
import 'package:addc/features/master_screen/view/master_screen.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/common_success_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../shared/constants/color_constants.dart';
import '../../../../shared/constants/text_styles.dart';

class CorporateSingleSignButton extends StatelessWidget {
  const CorporateSingleSignButton({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async => _onTap(context: context),
      child: Container(
        height: 50.h,
        margin: EdgeInsets.only(top: 24.h, bottom: 31.h),
        decoration: BoxDecoration(
            color: ColorConstants.color0AB3A1,
            borderRadius: BorderRadius.circular(14)),
        child: Center(
            child: Text("Corporate Single Sign-On", style: ts16cFFFFFFw4)),
      ),
    );
  }

  _onTap({required BuildContext context}) async {
    final masterProvider = context.read<MasterProvider>();
    final provider = context.read<LogInProvider>();
    LoginStatus loginStatus =
        await provider.signInWithMicrosoft(context: context);

    // bool isSuccess = await provider.makePostApiCall();
    ();
    if (loginStatus == LoginStatus.failure) return;
    if (!context.mounted) return;
    if (loginStatus == LoginStatus.success) {
      masterProvider.masterIndex = 0;
      Navigator.pushNamedAndRemoveUntil(
          context, MasterScreen.route, (route) => false);
      // Show success dialog
      // successBottomsheet(
      //   context: context,
      //   title: 'Login Successful!',
      //   description: 'Welcome back! You have been\nsuccessfully authenticated.',
      //   status: BottomSheetStatus.success,
      //   onPressed: () {
      //     Navigator.pop(context); // Close the dialog
      //   },
      // );
    } else if (loginStatus == LoginStatus.logout) {
      // Show warning dialog for unregistered employee
      // Navigator.pushNamedAndRemoveUntil(
      //     context, LoginScreen.route, (route) => false);
      successBottomsheet(
        context: context,
        title: 'Access Denied',
        description:
            'You\'re not a registered employee in Oracle Fusion.\n Please contact HR.',
        status: BottomSheetStatus.warning,
        onPressed: () {
          Navigator.pop(context); // Close the dialog
        },
      );
    }
  }
}

enum LoginStatus { success, failure, loading, logout }
