import 'dart:convert';
import 'dart:developer';
import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/authentication/services/auth_services.dart';
import 'package:addc/features/authentication/view/widgets/corporate_single_sign_button.dart';
import 'package:addc/features/find%20people/models/find_people_model.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/utils/urls.dart';
import 'package:addc/features/widgets/common_success_bottom_sheet.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:microsoft_graph_api/microsoft_graph_api.dart';
import 'package:microsoft_graph_api/models/places/room/room_model.dart';
import 'package:microsoft_graph_api/models/user/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LogInProvider extends ChangeNotifier {
  bool isLoading = false;
  bool _isObscureEnabled = false;
  bool get isObscureEnabled => _isObscureEnabled;
  set isObscureEnabled(bool index) {
    _isObscureEnabled = index;
    notifyListeners();
  }

  ImageProvider? imageProvider;
  getProfileImage() async {
    if (LoginedUser.accessToken != null) {
      try {
        if (LoginedUser.accessToken != null) {
          imageProvider = await fetchUserProfileImage(
              size: '240x240', token: LoginedUser.accessToken ?? '');
        }
      } catch (e) {
        log('Failed to get profile image using MSGraphAPI: $e');
        // Fallback to default image if profile image is not available
        // imageProvider = AssetImage('john_big'.asDummyPng());
      }
      notifyListeners();
    }
  }

// Authenication with microsoft

  final Dio _dio = Dio();
  String? accessToken;
  final AuthService _authService = AuthService();
  Future<User> fetchUserInfo(
      {required String token, required BuildContext context}) async {
    log('User token $token');
    try {
      final response = await _dio.get(
        'https://graph.microsoft.com/v1.0/me/',
        options: Options(
            headers: {'Authorization': 'Bearer $token'},
            validateStatus: (status) => true),
      );
      log('fetchUserInfo - > ${response.data} - ${response.statusCode}');
      if (response.statusCode == 403) {
        showAPIError(
          context: context,
          apiURL: 'https://graph.microsoft.com/v1.0/me/',
          response: jsonEncode(response.data),
        );
      }
      return User.fromJson(response.data);
    } catch (e) {
      log('Failed to fetch user info: $e');
      return const User.empty();
    }
  }

  Future<ImageProvider?> fetchUserProfileImage(
      {required String size, required String token}) async {
    try {
      final response = await _dio.get(
        'https://graph.microsoft.com/v1.0/me/photo/\$value',
        options: Options(
          responseType: ResponseType.bytes,
          validateStatus: (status) => true,
          headers: {'Authorization': 'Bearer $token'},
        ),
      );
      MSGraphAPI graphAPI = MSGraphAPI(LoginedUser.accessToken ?? '');
      List<Room> rooms = await graphAPI.users.fetchAllUserRooms();
      log('rooms = ${rooms.map((e) => e.displayName).toList()}');
      log('rooms accesstoken = $token');
      log('fetchUserProfileImage - ${response.statusCode} - ${response.realUri}');

      if (response.statusCode == 200) {
        return MemoryImage(response.data);
      } else if (response.statusCode == 404) {
        // User doesn't have a profile image, return default image
        log('User profile image not found, using default image');
        // return AssetImage('john_big'.asDummyPng());
      } else {
        log('Unexpected status code: ${response.statusCode}');
        // return AssetImage('john_big'.asDummyPng());
      }
    } catch (e) {
      log('Failed to fetch user profile image: $e');
      if (e is DioException && e.response != null) {
        log('Server response: ${e.response?.data}');
        // If it's a 404 error, user doesn't have profile image
        if (e.response?.statusCode == 404) {
          log('User profile image not found, using default image');
          // return AssetImage('john_big'.asDummyPng());
        }
      }
      // return AssetImage('john_big'.asDummyPng());
    }
    return null;
  }

  Future<LoginStatus> signInWithMicrosoft(
      {required BuildContext context}) async {
    try {
      imageProvider = null;
      if (isLoading) return LoginStatus.loading;
      FindPeopleModel? personalModel;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      isLoading = true;
      final token = await _authService.signInWithMicrosoft();
      if (token != null) {
        await prefs.setString('access_token', token);
        log('Access Token: $token');
        Map<String, dynamic> data = {};
        EasyLoading.show();
        // MSGraphAPI graphAPI = MSGraphAPI(token);
        User user = await fetchUserInfo(token: token, context: context);
        // imageProvider = await graphAPI.me.fetchUserProfileImage('240x240');
// <EMAIL>
// User@#12345###
        if (user.id != null) {
          // await graphAPI.users.fetchSpecificUserInfo(user.id!);
          data['employee_id'] = await getEmpolyeeID(
              userID: user.id!, token: token, context: context);
          personalModel = await getPersonalID(
              employeeID: data['employee_id'], context: context);
          data['person_id'] = personalModel?.personId;
        }
        // Check if user email contains allowed domains
        // String userEmail = user.mail ?? '';
        // log('userEmail = $userEmail');
        // List<String> allowedDomains = [
        //   '@taqadistribution.com',
        //   '@addc.ae',
        //   '@aadc.ae',
        //   '@adweag.ae',
        //   '@element8.ae'
        // ];
        // bool hasValidDomain =
        //     allowedDomains.any((domain) => userEmail.contains(domain));

        // if (!hasValidDomain) {
        //   log('logout performed 1');
        //   await logout();
        //   return LoginStatus.logout;
        // }

        if (personalModel == null) {
          log('logout performed 2');
          await logout();
          return LoginStatus.logout;
        }

        imageProvider =
            await fetchUserProfileImage(size: '240x240', token: token);
        data['business_phones'] = user.businessPhones;
        data['display_name'] = user.displayName;
        data['given_name'] = user.givenName;
        data['id'] = user.id;
        data['job_title'] = user.jobTitle;
        data['mail'] = user.mail;
        data['mobile_phone'] = user.mobilePhone;
        data['office_location'] = user.officeLocation;
        data['preferred_language'] = user.preferredLanguage;
        data['surname'] = user.surname;
        data['user_principal_name'] = user.userPrincipalName;
        data['access_token'] = token;

        log('employee_id - ${data['employee_id']} - ${data['person_id']}');
        log('message - $data - ${imageProvider == null}');
        await fetchSpecificUserInfo(token: token);

        EasyLoading.dismiss();
        LoginedUser.fromJson(data);
        isLoading = false;
        notifyListeners();
        return LoginStatus.success;
      } else {
        debugPrint('Login failed');
        successBottomsheet(
          context: context,
          title: 'Login Failed',
          description: 'Could not connect to Microsoft Entra.',
          status: BottomSheetStatus.warning,
          onPressed: () {
            Navigator.pop(context); // Close the dialog
          },
        );
        return LoginStatus.failure;
      }
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint('Login failed');
      successBottomsheet(
        context: context,
        title: 'Error',
        description: e.toString(),
        status: BottomSheetStatus.warning,
        onPressed: () {
          Navigator.pop(context); // Close the dialog
        },
      );
      return LoginStatus.failure;
    } finally {
      isLoading = false;
      EasyLoading.dismiss();
      notifyListeners();
    }
  }

  String? userNameErrorMessage;
  Future<bool> signInWithUsernamePassword(
      {required String username,
      required String password,
      required BuildContext context}) async {
    try {
      if (isLoading) return false;
      EasyLoading.show();
      await Future.delayed(const Duration(seconds: 3), () {});
      if (username != '<EMAIL>' || password != 'User@#12345###') {
        userNameErrorMessage = 'Either Username or password is incorrect';
        notifyListeners();
        return false;
      }

      imageProvider = null;

      // SharedPreferences prefs = await SharedPreferences.getInstance();
      isLoading = true;
      // final token = await _authService.signInWithMicrosoft();
      // if (token != null) {
      // await prefs.setString('access_token', token);
      // log('Access Token: $token');
      Map<String, dynamic> data = {};

      // MSGraphAPI graphAPI = MSGraphAPI(token);
      // User user = await fetchUserInfo(token: token);
      final Map<String, dynamic> userTempData = {
        'businessPhones': [],
        'displayName': 'Ali Asger Modi',
        'givenName': 'Ali Asger',
        'jobTitle': 'Element 8 Lead',
        'mail': '<EMAIL>',
        'mobilePhone': '+971588156675',
        'officeLocation': null,
        'preferredLanguage': null,
        'surname': 'Modi',
        'userPrincipalName': '<EMAIL>',
        'id': '07ea372f-f1ca-4e6f-bfe6-23a78ee1ddac'
      };
      User user = User.fromJson(userTempData);
      // imageProvider = await graphAPI.me.fetchUserProfileImage('240x240');
      imageProvider = await fetchUserProfileImage(size: '240x240', token: '');
      if (user.id != null) {
        // await graphAPI.users.fetchSpecificUserInfo(user.id!);
        data['employee_id'] = '47726';

        final personalModel = await getPersonalID(
          employeeID: data['employee_id'],
          context: context,
        );
        data['person_id'] = personalModel?.personId;
        log('personalModel?.personId - ${personalModel?.personId}');
      }

      data['business_phones'] = user.businessPhones;
      data['display_name'] = user.displayName;
      data['given_name'] = user.givenName;
      data['id'] = user.id;
      data['job_title'] = user.jobTitle;
      data['mail'] = user.mail;
      data['mobile_phone'] = user.mobilePhone;
      data['office_location'] = user.officeLocation;
      data['preferred_language'] = user.preferredLanguage;
      data['surname'] = user.surname;
      data['user_principal_name'] = user.userPrincipalName;
      data['access_token'] =
          'knfgsiuckvwchv-wyfgloyruyei-orwuyfcnouaspcn-urfgohasfgh-mvhjkiou';

      log('message - $data - ${imageProvider == null}');
      // await fetchSpecificUserInfo(token: token);

      EasyLoading.dismiss();
      LoginedUser.fromJson(data);
      isLoading = false;
      notifyListeners();
      return true;
      // } else {
      //   debugPrint('Login failed');
      // }
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint('Login failed');
    } finally {
      isLoading = false;
      EasyLoading.dismiss();
      notifyListeners();
    }
    return false;
  }

  Future<String?> getEmpolyeeID({
    required String userID,
    required String token,
    required BuildContext context,
  }) async {
    final response = await _dio.get(
        options: Options(headers: {'Authorization': 'Bearer $token'}),
        'https://graph.microsoft.com/v1.0/users/$userID?\$select=EmployeeId');
    log('getEmpolyeeID => ${response.data} - ${response.realUri}');
    if (response.statusCode == 200) {
      Map<String, dynamic> data = response.data;
      if (data.containsKey('employeeId')) {
        return data['employeeId'];
      }
    } else {
      showAPIError(
        context: context,
        apiURL:
            'https://graph.microsoft.com/v1.0/users/$userID?\$select=EmployeeId',
        response: jsonEncode(response.data),
      );
    }
    return null;
  }

  Future<FindPeopleModel?> getPersonalID(
      {required String employeeID, required BuildContext context}) async {
    final response = await _dio.get(
        // 'https://fa-ewzp-dev3-saasfaprod1.fa.ocs.oraclecloud.com/hcmRestApi/resources/11.13.18.05/emps/?q=PersonNumber=47726',
        '${ApiConstants.oracleBaseURL}workers/?q=PersonNumber=$employeeID',
        options: Options(headers: ApiConstants.authHeader()));
    log('getPersonalID - ${response.data} - ${response.realUri}');
    if (response.statusCode == 200) {
      Map<String, dynamic> json = response.data;
      if (json.containsKey('items')) {
        List data = (json['items'] ?? []) as List;
        if (data.isNotEmpty) {
          return FindPeopleModel.fromJson(data.first);
        } else {
          null;
        }
      }
    } else {
      showAPIError(
        context: context,
        apiURL:
            '${ApiConstants.oracleBaseURL}workers/?q=PersonNumber=$employeeID',
        response: jsonEncode(response.data),
      );
    }
    return null;
  }

  Future<User> fetchSpecificUserInfo({required String token}) async {
    try {
      log('message-------------------------->>${LoginedUser.accessToken}');
      final response = await _dio.get(
        'https://graph.microsoft.com/v1.0/users/',
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );
      log('fetchSpecificUserInfo --> ${response.data}');
      return User.fromJson(response.data);
    } catch (e) {
      log('Failed to fetch specific user info: $e');
      return const User.empty();
    }
  }

  Future<void> logout() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.clear();
    await LoginedUser.clearData();
    imageProvider = null;
    await _authService.logout();
  }

  showAPIError(
      {required BuildContext context,
      required String apiURL,
      required String response}) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Error'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('API- $apiURL'),
            Text(response),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: ColorConstants.color0AB3A1,
            ),
            child: Text('OK'),
          )
        ],
      ),
    );
  }
}
