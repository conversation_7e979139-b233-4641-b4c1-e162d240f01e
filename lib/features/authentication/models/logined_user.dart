import 'dart:developer';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class LoginedUser {
  static List<String>? businessPhones;
  static String? displayName;
  static String? givenName;
  static String? id;
  static String? jobTitle;
  static String? mail;
  static String? mobilePhone;
  static String? officeLocation;
  static String? preferredLanguage;
  static String? surname;
  static String? userPrincipalName;
  static String? accessToken;
  static String? employeeId;
  static String? personId;

  static final _secureStorage = const FlutterSecureStorage();

  LoginedUser.fromJson(Map<String, dynamic> json) {
    businessPhones = json['business_phones']?.cast<String>();
    displayName = json['display_name'];
    givenName = json['given_name'];
    id = json['id'] ?? LoginedUser.id;
    jobTitle = json['job_title'];
    mail = json['mail'];
    mobilePhone = json['mobile_phone'];
    officeLocation = json['office_location'];
    preferredLanguage = json['preferred_language'];
    surname = json['surname'];
    userPrincipalName = json['user_principal_name'];
    accessToken = json['access_token'];
    employeeId = json['employee_id'];
    personId = json['person_id']?.toString();
    log('personId - 3 $personId');
    storeUserDetailsLocally();
  }

  storeUserDetailsLocally() async {
    // Store sensitive data in secure storage
    if (accessToken != null) {
      await _secureStorage.write(key: 'access_token', value: accessToken);
    }
    if (id != null) {
      await _secureStorage.write(key: 'id', value: id);
    }
    if (mail != null) {
      await _secureStorage.write(key: 'mail', value: mail);
    }
    if (mobilePhone != null) {
      await _secureStorage.write(key: 'mobile_phone', value: mobilePhone);
    }
    if (businessPhones != null) {
      await _secureStorage.write(
          key: 'business_phones', value: businessPhones?.join(','));
    }
    if (userPrincipalName != null) {
      await _secureStorage.write(
          key: 'user_principal_name', value: userPrincipalName);
    }
    if (employeeId != null) {
      await _secureStorage.write(key: 'employee_id', value: employeeId);
    }
    if (personId != null) {
      await _secureStorage.write(key: 'person_id', value: personId);
      log('personId - 0 $personId');
    }

    log('personId - 1 $personId');

    // Store non-sensitive data in SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (displayName != null) {
      prefs.setString('display_name', displayName ?? '');
    }
    if (givenName != null) {
      prefs.setString('given_name', givenName ?? '');
    }
    if (jobTitle != null) {
      prefs.setString('job_title', jobTitle ?? '');
    }
    if (officeLocation != null) {
      prefs.setString('office_location', officeLocation ?? '');
    }
    if (preferredLanguage != null) {
      prefs.setString('preferred_language', preferredLanguage ?? '');
    }
    if (surname != null) {
      prefs.setString('surname', surname ?? '');
    }
  }

  static Future<void> getUser() async {
    // Get sensitive data from secure storage
    accessToken = await _secureStorage.read(key: 'access_token');
    id = await _secureStorage.read(key: 'id');
    mail = await _secureStorage.read(key: 'mail');
    mobilePhone = await _secureStorage.read(key: 'mobile_phone');
    userPrincipalName = await _secureStorage.read(key: 'user_principal_name');
    String? phones = await _secureStorage.read(key: 'business_phones');
    businessPhones = phones?.split(',');
    employeeId = await _secureStorage.read(key: 'employee_id');
    personId = await _secureStorage.read(key: 'person_id');
    log('personId - 2 $personId');
    // Get non-sensitive data from SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    displayName = prefs.getString('display_name');
    givenName = prefs.getString('given_name');
    jobTitle = prefs.getString('job_title');
    officeLocation = prefs.getString('office_location');
    preferredLanguage = prefs.getString('preferred_language');
    surname = prefs.getString('surname');
  }

  static Future<void> clearData() async {
    // Clear secure storage
    await _secureStorage.deleteAll();

    // Clear SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.clear();

    // Clear static variables
    businessPhones = null;
    displayName = null;
    givenName = null;
    id = null;
    jobTitle = null;
    mail = null;
    mobilePhone = null;
    officeLocation = null;
    preferredLanguage = null;
    surname = null;
    userPrincipalName = null;
    accessToken = null;
  }
}
