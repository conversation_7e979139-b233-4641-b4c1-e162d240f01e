import 'dart:developer';
import 'package:flutter/cupertino.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter/services.dart';

class BiometricAuthService {
  SupportState supportState = SupportState.unknown;
  final LocalAuthentication auth = LocalAuthentication();
  bool isAuthorized = false;
  Future<SupportState> isDeviceSupported() async {
    bool isSupported = await auth.isDeviceSupported();
    supportState =
        isSupported ? SupportState.supported : SupportState.unsupported;
    log('supportState ------ $supportState');
    return isSupported ? SupportState.supported : SupportState.unsupported;
  }

  Future<bool> checkBiometrics() async {
    late bool canCheckBiometrics;
    try {
      canCheckBiometrics = await auth.canCheckBiometrics;
    } on PlatformException catch (e) {
      canCheckBiometrics = false;
      debugPrint(e.toString());
    }

    return canCheckBiometrics;
  }

  Future<List<BiometricType>> getAvailableBiometrics() async {
    List<BiometricType> availableBiometrics = [];
    try {
      availableBiometrics = await auth.getAvailableBiometrics();
    } on PlatformException catch (e) {
      availableBiometrics = <BiometricType>[];
      debugPrint(e.toString());
    }
    log('availableBiometrics - ${availableBiometrics.map((e) => e.toString()).toList()}');
    return availableBiometrics;
  }

  Future<BiometricStatus> authenticate() async {
    try {
      final authenticated = await auth.authenticate(
        localizedReason: 'You have enabled the Bio-metrics authentication',
        options: const AuthenticationOptions(
          stickyAuth: true,
        ),
      );
      isAuthorized = authenticated;
      return authenticated
          ? BiometricStatus.authorized
          : BiometricStatus.unAuthorized;
    } on PlatformException catch (e) {
      debugPrint(e.toString());
      // Check for user cancellation codes (string values)
      log('e.code == ${e.code}');
      if (e.code == 'NotAuthenticated' || e.code == 'UserCanceled') {
        return BiometricStatus.cancelled;
      }
      return BiometricStatus.unAuthorized;
    }
  }

  // Future<void> _authenticateWithBiometrics() async {
  //   bool authenticated = false;
  //   _isAutorized = false;
  //   try {
  //     setState(() {
  //       _isAuthenticating = true;
  //       _authorized = 'Authenticating';
  //     });
  //     authenticated = await auth.authenticate(
  //       localizedReason:
  //           'Scan your fingerprint (or face or whatever) to authenticate',
  //       options: const AuthenticationOptions(
  //         stickyAuth: true,
  //         biometricOnly: true,
  //       ),
  //     );
  //     setState(() {
  //       _isAuthenticating = false;
  //       _authorized = 'Authenticating';
  //     });
  //   } on PlatformException catch (e) {
  //     print(e);
  //     setState(() {
  //       _isAuthenticating = false;
  //       _authorized = 'Error - ${e.message}';
  //     });
  //     return;
  //   }
  //   if (!mounted) {
  //     return;
  //   }

  //   final String message = authenticated ? 'Authorized' : 'Not Authorized';
  //   _isAutorized = authenticated;
  //   setState(() {
  //     _authorized = message;
  //   });
  // }

  Future<void> cancelAuthentication() async {
    await auth.stopAuthentication();
  }

  Future<BiometricStatus> checkAllSecuritiesForOpenApp() async {
    try {
      // Check if device supports biometrics
      await isDeviceSupported();
      if (supportState != SupportState.supported) {
        return BiometricStatus.unsupported;
      }

      // Check if biometrics are available and enabled
      final hasBiometrics = await checkBiometrics();
      if (!hasBiometrics) {
        return BiometricStatus.notAvailable;
      }

      // Get available biometric types
      final availableBiometrics = await getAvailableBiometrics();
      if (availableBiometrics.isEmpty) {
        return BiometricStatus.notConfigured;
      }

      // Attempt biometric authentication
      final status = await authenticate();
      return status;
    } catch (e) {
      // Log error for debugging but return a secure default
      log('Biometric authentication error: $e');
      return BiometricStatus.unAuthorized;
    }
  }

  Future<bool> addedSecurityForDisableBiometricFeature() async {
    final status = await authenticate();
    return status == BiometricStatus.authorized;
  }
}

enum BiometricStatus {
  authorized,
  unAuthorized,
  cancelled,
  unsupported,
  notAvailable,
  notConfigured
}

enum SupportState { unknown, supported, unsupported }
