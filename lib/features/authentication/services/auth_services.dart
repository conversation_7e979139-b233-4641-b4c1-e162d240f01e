import 'dart:developer';
import 'package:aad_oauth/aad_oauth.dart';
import 'package:aad_oauth/model/config.dart';
import 'package:addc/app/app.dart';
import 'package:addc/config/env_config.dart';
import 'package:flutter/material.dart';

class AuthService {
  //--------------------------------varun--------------------------------------------
  // static const String clientId =
  //     "85199cb5-474a-482d-a04b-ce11ac157e8c"; //- varun
  // static const String redirectUri = "addc-test://auth"; //- varun
  // static const String tenantId =
  //     "53c094a1-96fa-4aa6-aaf3-7d8c211469ad"; //- varun
  //--------------------------------varun--------------------------------------------

  // static const String clientId = "1346cf1c-f29c-4c56-ab71-47f4f98b7132";
  // static const String redirectUri =
  //     "msauth://com.taqadistribution.employeeapp/df%2FWrQM67qwAZFa%2F4i5uTORfZgI%3D";
  // static const String tenantId = "5774259b-7c35-4df0-bf25-3901eb73df2c";
  static final Config config = Config(
    tenant: EnvConfig.tenantId,
    clientId: EnvConfig.clientId,
    scope: "openid profile offline_access User.Read",
    // redirectUri is Optional as a default is calculated based on app type/web location
    redirectUri: EnvConfig.redirectUri,
    navigatorKey: navigatorKey,
    webUseRedirect:
        true, // default is false - on web only, forces a redirect flow instead of popup auth
    //Optional parameter: Centered CircularProgressIndicator while rendering web page in WebView
    loader: Center(child: CircularProgressIndicator()),
    postLogoutRedirectUri: 'http://your_base_url/logout', //optional
  );

  final AadOAuth oauth = AadOAuth(config);
  Future<String?> signInWithMicrosoft() async {
    await oauth.logout();
    final result = await oauth.login();
    log('signInWithMicrosoft -- $result');
    result.fold((failure) => log('failure-> ${failure.message}'), (token) {});
    String? accessToken = await oauth.getAccessToken();
    // 3. (CRUCIAL STEP) Now, call the IntuneService to verify enrollment
    // print("Checking for Intune enrolled account...");
    // final String? enrolledAccount = await IntuneService.getEnrolledAccount();

    // if (enrolledAccount != null) {
    //   print("✅ Intune SDK is active. Enrolled User: $enrolledAccount");
    //   // You can optionally compare this with the logged-in user's UPN if available
    // } else {
    //   print(
    //       "⚠️ Intune SDK is active, but no enrolled account found. Policies may not apply.");
    //   // This could happen if the Company Portal app is not configured correctly
    //   // or if the user is not targeted with an App Protection Policy.
    // }
    return accessToken;
  }

  Future<void> logout() async {
    await oauth.logout();
  }
}
