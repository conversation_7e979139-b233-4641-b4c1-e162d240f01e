import 'dart:developer';
import 'package:flutter/services.dart';

class IntuneService {
  static const _channel = MethodChannel('com.taqadistribution/intune');

  /// Get the enrolled account from Intune SDK
  static Future<String?> getEnrolledAccount() async {
    try {
      log("📱 Calling Intune SDK getEnrolledAccount...");
      final String? enrolledAccount =
          await _channel.invokeMethod('getEnrolledAccount');
      log("📱 Intune SDK response: $enrolledAccount");
      return enrolledAccount;
    } on PlatformException catch (e) {
      log("❌ Failed to get enrolled account: '${e.message}' - Code: ${e.code}");
      return null;
    } catch (e) {
      log("❌ Unexpected error getting enrolled account: $e");
      return null;
    }
  }

  /// Test if the Intune SDK is properly integrated with detailed diagnostics
  static Future<Map<String, dynamic>> testIntuneIntegrationDetailed() async {
    try {
      log("🧪 Running detailed Intune SDK integration test...");
      final Map<String, dynamic> result =
          await _channel.invokeMethod('testIntegration');

      log("📊 Integration test results:");
      log("   - SDK Available: ${result['sdkAvailable']}");
      log("   - UserInfo Available: ${result['userInfoAvailable']}");
      log("   - Primary User: ${result['primaryUser']}");

      return result;
    } on PlatformException catch (e) {
      log("❌ Detailed integration test failed: ${e.message} (Code: ${e.code})");
      return {
        'sdkAvailable': false,
        'userInfoAvailable': false,
        'primaryUser': null,
        'error': e.message,
        'errorCode': e.code
      };
    } catch (e) {
      log("❌ Unexpected error in detailed integration test: $e");
      return {
        'sdkAvailable': false,
        'userInfoAvailable': false,
        'primaryUser': null,
        'error': e.toString()
      };
    }
  }

  /// Test if the Intune SDK is properly integrated
  static Future<bool> testIntuneIntegration() async {
    try {
      log("🧪 Testing Intune SDK integration...");

      // Test 1: Check if method channel is accessible
      final String? enrolledAccount = await getEnrolledAccount();

      if (enrolledAccount != null) {
        log("✅ Test 1 PASSED: Intune SDK is accessible and returned: $enrolledAccount");
        return true;
      } else {
        log("⚠️ Test 1 PARTIAL: Intune SDK is accessible but no enrolled account found");
        log("   This could mean:");
        log("   - User is not enrolled in Intune");
        log("   - Company Portal is not configured");
        log("   - App Protection Policy is not applied");
        return true; // SDK is working, just no enrollment
      }
    } on PlatformException catch (e) {
      log("❌ Test 1 FAILED: Platform exception - ${e.message} (Code: ${e.code})");
      if (e.code == 'UNAVAILABLE') {
        log("   This suggests Intune SDK is integrated but MAMUserInfo is null");
        log("   Check if the app is enrolled and policies are applied");
      }
      return false;
    } catch (e) {
      log("❌ Test 1 FAILED: Unexpected error - $e");
      return false;
    }
  }

  /// Print comprehensive Intune integration status
  static Future<void> printIntegrationStatus() async {
    log("🔍 === INTUNE SDK INTEGRATION STATUS ===");

    try {
      // Run detailed integration test
      final Map<String, dynamic> testResults =
          await testIntuneIntegrationDetailed();

      // Determine overall status
      bool sdkAvailable = testResults['sdkAvailable'] ?? false;
      bool userInfoAvailable = testResults['userInfoAvailable'] ?? false;
      String? primaryUser = testResults['primaryUser'];

      if (sdkAvailable &&
          userInfoAvailable &&
          primaryUser != null &&
          primaryUser != 'null') {
        log("✅ OVERALL STATUS: Intune SDK is fully integrated and user is enrolled");
        log("   Enrolled User: $primaryUser");
      } else if (sdkAvailable) {
        log("⚠️ OVERALL STATUS: Intune SDK is integrated but user may not be enrolled");
        log("   This is normal if the user hasn't enrolled through Company Portal yet");
      } else {
        log("❌ OVERALL STATUS: Intune SDK integration has issues");
        if (testResults.containsKey('error')) {
          log("   Error: ${testResults['error']}");
        }
      }

      log("📋 DETAILED RESULTS:");
      log("   - SDK Available: $sdkAvailable");
      log("   - UserInfo Available: $userInfoAvailable");
      log("   - Primary User: ${primaryUser ?? 'null'}");

      log("📋 CONFIGURATION CHECK:");
      log("   - Method Channel: 'com.taqadistribution/intune'");
      log("   - Android Package: com.taqadistribution.employee");
      log("   - MainActivity: FlutterFragmentActivity with Intune SDK");
      log("   - Manifest: MAMApplication configured");
    } catch (e) {
      log("❌ Error during integration status check: $e");
    }

    log("🔍 === END INTUNE STATUS ===");
  }
}
