import 'package:flutter/services.dart';

class IntuneService {
  static const _channel = MethodChannel('com.taqadistribution/intune');

  static Future<String?> getEnrolledAccount() async {
    try {
      final String? enrolledAccount = await _channel.invokeMethod('getEnrolledAccount');
      return enrolledAccount;
    } on PlatformException catch (e) {
      print("Failed to get enrolled account: '${e.message}'.");
      return null;
    }
  }
}