import 'dart:io';
import 'package:addc/features/home/<USER>/models/my_leaves_model.dart';
import 'package:addc/features/home/<USER>/providers/my_leaves_provider.dart';
import 'package:addc/features/home/<USER>/my_leaves_tile_widget.dart';
import 'package:addc/features/leaves/models/leave_balance_model.dart';
import 'package:addc/features/leaves/widgets/leaves_card.dart';
import 'package:addc/features/utils/general_functions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_divider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../home/<USER>/providers/leave_balance_provider.dart';

class LeavesScreen extends StatefulWidget {
  static const route = '/leaves_screen';
  const LeavesScreen({super.key});

  @override
  State<LeavesScreen> createState() => _LeavesScreenState();
}

class _LeavesScreenState extends State<LeavesScreen> {
  late MyLeavesProvider _provider;

  @override
  void initState() {
    super.initState();
    _provider = context.read<MyLeavesProvider>();
    _provider.initMyLeavesPagination();
  }

  @override
  void dispose() {
    super.dispose();
    _provider.myLeavesPagingController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final leaveBalanceProvider = context.read<LeaveBalanceProvider>();
    String? leaveBalance =
        leaveBalanceProvider.leaveBalanceModel?.formattedBalance;
    if (leaveBalance != null && leaveBalance.isNotEmpty) {
      final balanceItems = leaveBalance.split(' ');
      if (balanceItems.isNotEmpty) {
        leaveBalance = balanceItems.first;
      }
    }
    List<LeaveBalanceModel> items = [
      LeaveBalanceModel(
          name: 'Annual Leaves',
          balance: num.parse(leaveBalance ?? '0'),
          total: 51),
      // LeaveBalanceModel(name: 'Sick Leaves', balance: 10, total: 10),
      // LeaveBalanceModel(
      //     name: 'Industrial Sick Leave', balance: 180, total: 180),
      // LeaveBalanceModel(name: 'Unpaid Leaves', balance: 10, total: 10),
    ];
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text('Leaves', style: ts20c000940w4),
        leading: const CommonBackButton(),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: Column(
          children: [
            H(25.h),
            // GridView.builder(
            //   shrinkWrap: true,
            //   physics: const NeverScrollableScrollPhysics(),
            //   itemCount: items.length,
            //   gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            //     crossAxisCount: 2,
            //     crossAxisSpacing: 9.w,
            //     mainAxisSpacing: 10.h,
            //     childAspectRatio: 180.w / 90.h,
            //   ),
            //   itemBuilder: (context, index) {
            //     LeaveBalanceModel item = items[index];
            //     return LeavesCard(item: item);
            //   },
            // ),
            LeavesCard(item: items[0]),
            H(30.h),
            Expanded(child: LeaveHistoryModule())
          ],
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        color: ColorConstants.colorEFF1FF,
        child: ElevatedButton(
          onPressed: () {
            // Navigator.of(context).push(MaterialPageRoute(
            //   builder: (context) => InboxScreen(),
            // ));
            String link =
                'https://play.google.com/store/apps/details?id=com.oracle.cloud.hcm.mobile';
            if (Platform.isAndroid) {
              link = extractPackageName(link);
            }
            launchApp(
                androidPackageName: link,
                appStoreLink:
                    'https://apps.apple.com/us/app/oracle-fusion-applications/id1361813677',
                iosUrlScheme: 'oraclefusion://');
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("Apply Leave", style: ts16cFFFFFFw4),
              Text("(Redirecting to Oracle Fusion)", style: ts12cFFFFFFw4)
            ],
          ),
        ),
      ),
    );
  }
}

class LeaveHistoryModule extends StatelessWidget {
  const LeaveHistoryModule({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<MyLeavesProvider>();
    return Column(
      children: [
        Row(children: [Text("Leave History", style: ts20c000940w4)]),
        H(8),
        Flexible(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Container(
                  padding: EdgeInsets.only(left: 12, top: 12, right: 12),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(18),
                      color: ColorConstants.colorFFFFFF),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: EdgeInsets.all(4),
                        decoration: BoxDecoration(
                            color: ColorConstants.colorF9F9F9,
                            borderRadius: BorderRadius.circular(14)),
                        child: Consumer<MyLeavesProvider>(
                          builder: (context, provider, _) {
                            return SizedBox(
                              height: 28.h,
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                spacing: 6.w,
                                children: provider.leaveCategories.map(
                                  (e) {
                                    int index =
                                        provider.leaveCategories.indexOf(e);
                                    bool isSelected =
                                        provider.selectedCategoryIndex == index;
                                    return GestureDetector(
                                      onTap: () => provider.onCategoryChanged(
                                          index: index),
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 18),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? ColorConstants.color000940
                                              : Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        alignment: Alignment.center,
                                        child: Text(
                                          e,
                                          style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              color: isSelected
                                                  ? Colors.white
                                                  : ColorConstants.color111111),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    );
                                  },
                                ).toList(),
                              ),
                            );
                          },
                        ),
                      ),
                      Flexible(
                        child: PagedListView.separated(
                          shrinkWrap: true,
                          padding: EdgeInsets.only(
                              left: 6.w, right: 0.w, top: 20.h, bottom: 20.h),
                          pagingController: provider.myLeavesPagingController,
                          builderDelegate:
                              PagedChildBuilderDelegate<MyLeavesModel>(
                                  animateTransitions: true,
                                  firstPageErrorIndicatorBuilder: (context) =>
                                      Text('Something went wrong'),
                                  firstPageProgressIndicatorBuilder:
                                      (context) => _skeletonizer(itemCount: 20),
                                  newPageErrorIndicatorBuilder: (context) =>
                                      Text('Something went wrong'),
                                  newPageProgressIndicatorBuilder: (context) =>
                                      _skeletonizer(itemCount: 1),
                                  noItemsFoundIndicatorBuilder:
                                      (context) =>
                                          Center(child: Text('No data found')),
                                  itemBuilder: (context, item, index) =>
                                      MyLeavesTileWidget(item: item)),
                          separatorBuilder: (context, index) => CommonDivider(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _skeletonizer({required int itemCount}) {
    final item = MyLeavesModel(
        startDate: '2023-12-10',
        endDate: '2023-12-10',
        absenceType: 'Approved',
        approvalStatusCd: 'Approved');
    return Skeletonizer(
      enabled: true,
      child: ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        shrinkWrap: true,
        itemCount: itemCount,
        itemBuilder: (context, index) => MyLeavesTileWidget(item: item),
        separatorBuilder: (context, index) => CommonDivider(),
      ),
    );
  }
}

class TimeAndLeave extends StatelessWidget {
  final String dateandTime;
  final String leave;
  const TimeAndLeave(
      {super.key, required this.dateandTime, required this.leave});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          dateandTime,
          style: ts14c000940w4,
        ),
        Text(
          leave,
          style: ts12c9D9D9Dw4,
        )
      ],
    );
  }
}

class InBoxLeaveHistoryModule extends StatelessWidget {
  const InBoxLeaveHistoryModule({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<MyLeavesProvider>();
    return Container(
      padding: EdgeInsets.only(left: 12, top: 12, right: 12),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18),
          color: ColorConstants.colorFFFFFF),
      child: Column(
        children: [
          // Row(children: [Text("Leave History", style: ts20c000940w4)]),
          H(8),
          Container(
            padding: EdgeInsets.all(4),
            decoration: BoxDecoration(
                color: ColorConstants.colorF9F9F9,
                borderRadius: BorderRadius.circular(14)),
            child: Consumer<MyLeavesProvider>(
              builder: (context, provider, _) {
                return SizedBox(
                  height: 28.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: provider.leaveCategories.map(
                      (e) {
                        int index = provider.leaveCategories.indexOf(e);
                        bool isSelected =
                            provider.selectedCategoryIndex == index;
                        return GestureDetector(
                          onTap: () =>
                              provider.onInBoxCategoryChanged(index: index),
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 18),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? ColorConstants.color000940
                                  : Colors.white,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              e,
                              style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: isSelected
                                      ? Colors.white
                                      : ColorConstants.color111111),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        );
                      },
                    ).toList(),
                  ),
                );
              },
            ),
          ),
          Flexible(
            child: PagedListView.separated(
              pagingController: provider.inBoxLeavesPagingController,
              padding: EdgeInsets.only(top: 15, bottom: 10),
              builderDelegate: PagedChildBuilderDelegate<MyLeavesModel>(
                animateTransitions: true,
                firstPageErrorIndicatorBuilder: (context) =>
                    Text('Something went wrong'),
                firstPageProgressIndicatorBuilder: (context) {
                  final item = MyLeavesModel(
                      startDate: '2023-12-10',
                      endDate: '2023-12-10',
                      absenceType: 'Approved',
                      approvalStatusCd: 'Approved');
                  final items = List.generate(4, (index) => item).toList();
                  return Skeletonizer(
                    enabled: true,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: items.map(
                        (e) {
                          return Column(
                            children: [
                              MyLeavesTileWidget(item: e),
                              CommonDivider(),
                            ],
                          );
                        },
                      ).toList(),
                    ),
                  );
                },
                newPageErrorIndicatorBuilder: (context) =>
                    Text('Something went wrong'),
                newPageProgressIndicatorBuilder: (context) {
                  final item = MyLeavesModel(
                      startDate: '2023-12-10',
                      endDate: '2023-12-10',
                      absenceType: 'Approved',
                      approvalStatusCd: 'Approved');
                  return Skeletonizer(
                    enabled: true,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        MyLeavesTileWidget(item: item),
                      ],
                    ),
                  );
                },
                noItemsFoundIndicatorBuilder: (context) =>
                    Center(child: Text('No data found')),
                itemBuilder: (context, item, index) =>
                    MyLeavesTileWidget(item: item),
              ),
              separatorBuilder: (context, index) => CommonDivider(),
            ),
          ),
        ],
      ),
    );
  }
}
