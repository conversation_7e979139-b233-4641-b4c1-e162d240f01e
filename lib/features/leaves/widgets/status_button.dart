import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StatusButtonForleaves extends StatelessWidget {
  final Color color;
  final String text;
  const StatusButtonForleaves({super.key,required this.color,required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w,),
      height: 24.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: color,
      ),
      child: Center(child: Text(text,style: ts12cFFFFFFw4,)),
    );
  }
}