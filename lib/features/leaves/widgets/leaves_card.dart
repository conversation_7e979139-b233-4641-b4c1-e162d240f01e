import 'package:addc/features/leaves/models/leave_balance_model.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class LeavesCard extends StatelessWidget {
  final LeaveBalanceModel item;

  const LeavesCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String title = item.name ?? '';
    num balance = item.balance ?? 0;
    // int total = item.total ?? 0;
    return ClipRRect(
      borderRadius: BorderRadius.circular(18.r),
      child: Container(
        padding: EdgeInsets.only(left: 13.w, top: 0, bottom: 0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18.r),
          color: ColorConstants.colorFFFFFF,
        ),
        child: Stack(
          alignment: Alignment.bottomCenter,
          fit: StackFit.passthrough,
          children: [
            Align(
                alignment: Alignment.bottomRight,
                child: SvgPicture.asset('summary_graphics'.asIconSvg())),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(title,
                      style: ts12c0AB3A1w4,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2),
                ),
                Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(text: '$balance', style: ts46c1E1E1Ew4h1),
                      // TextSpan(text: '/${total}Days', style: ts16c1E1E1Ew4h1),
                    ],
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
