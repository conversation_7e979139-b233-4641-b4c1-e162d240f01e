import 'dart:convert';

import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/book_a_vehicle/models/vehicle_booking_history_model.dart';
import 'package:addc/features/utils/urls.dart';
import 'package:dio/dio.dart';

class VehicleBookingService {
  final Dio _dio = Dio();

  Future<Response> bookVehicle(Map<String, dynamic> payload) async {
    String url =
        "${ApiConstants.taqaBaseUrl}/gateway/ADDCMOB_REST_ADDCVR/1.0/REST_ADDCVR";

    try {
      Response response = await _dio.post(
        url,
        data: payload,
        options: Options(
          headers: {'Content-Type': 'application/json'},
          validateStatus: (status) => true,
        ),
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<List<VehicleBookingHistoryModel>> getVehicleBookings() async {
    String endpoint =
        '${ApiConstants.taqaBaseUrl}/gateway/ADDCMOB_REST_ADDCVRQuery/1.0/REST_ADDCVRQuery';
    final payload = {
      "STATUS": "VRAPPR,COMP,APPR,WRAAP,VRAAP,INPRG,VRENDR",
      // "WOEXTEND": {"REPORTEDBYFILENO": "47726"}
      "WOEXTEND": {"REPORTEDBYFILENO": "${LoginedUser.employeeId}"}
    };

    final response = await _dio.post(endpoint,
        data: payload,
        options: Options(
            headers: {'Content-Type': 'application/json'},
            validateStatus: (status) => true));
    if (response.statusCode == 200) {
      List<dynamic> dataList;
      if (response.data is String) {
        dataList = jsonDecode(response.data) as List<dynamic>;
      } else if (response.data is List) {
        dataList = response.data as List<dynamic>;
      } else {
        throw Exception(
            'Unexpected response type: [31m${response.data.runtimeType}[0m');
      }
      return dataList
          .map((e) => VehicleBookingHistoryModel.fromJson(e))
          .toList();
    }
    return [];
  }
}
