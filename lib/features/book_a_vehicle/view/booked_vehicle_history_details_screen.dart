import 'package:addc/features/book_a_vehicle/models/vehicle_booking_history_model.dart';
import 'package:addc/features/book_a_vehicle/view/widgets/book_a_vehicle_history_tile.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_circle_avatar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BookedVehicleHistoryDetailsScreen extends StatelessWidget {
  static const route = '/booked_vehicle_history_details_screen';

  final VehicleBookingHistoryModel item;
  const BookedVehicleHistoryDetailsScreen({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String assetNum = item.assetNum ?? '';
    String description = item.description ?? '';
    String onBehalfof = item.onBehalfof ?? '';
    String plusAssetType = item.plusAssetType ?? '';
    String schedStart = item.schedStart ?? '';
    String siteId = item.siteId ?? '';
    String status = item.status ?? '';
    String targCompDate = item.targCompDate ?? '';
    String targStartDate = item.targStartDate ?? '';
    String woExtend = item.woExtend?.onBehalfofFileno ?? '';
    String wonum = item.wonum ?? '';

    return Scaffold(
      appBar: AppBar(
          leading: const CommonBackButton(), title: Text('Request Details')),
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 25.h, left: 12.w, right: 12.w),
            margin: EdgeInsets.only(top: 25.h, left: 12.w, right: 12.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(18.r),
                color: ColorConstants.colorFFFFFF),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BookAVehicleHistoryTile(item: item, isRouteNeeded: false),
                H(10),
                Divider(
                    height: 0,
                    thickness: .5,
                    color: ColorConstants.colorD9D9D9),
                H(28),
                Row(
                  children: [
                    Expanded(
                        child: _keyValueWidget(
                            title: 'REQUEST FOR FILE NO', value: wonum)),
                    // Expanded(
                    //   child: _keyValueWidget(
                    //       title: 'PHONE NUMBER', value: '+971 2 1234567')
                    // ),
                  ],
                ),
                H(21),
                _keyValueWidget(title: 'DESCRIPTION', value: description),
                H(21),
                Row(
                  children: [
                    Expanded(
                      child: _keyValueWidget(
                          title: 'VEHICLE TYPE', value: plusAssetType),
                    ),
                    if (assetNum.isNotEmpty)
                      Expanded(
                        child: _keyValueWidget(
                            title: 'NO.OF PASSENGERS', value: assetNum),
                      ),
                  ],
                ),
                // H(21),
                // _keyValueWidget(title: 'PURPOSE', value: 'Official'),
                H(21),
                // Row(
                //   children: [
                //     Expanded(
                //       child: _keyValueWidget(
                //           title: 'DESTINATION FROM', value: 'Al Nahyan'),
                //     ),
                //     Expanded(
                //       child: _keyValueWidget(
                //           title: 'DESTINATION TO', value: 'Al Wahda'),
                //     ),
                //   ],
                // ),
                // H(21),
                // Row(
                //   crossAxisAlignment: CrossAxisAlignment.start,
                //   children: [
                //     Row(
                //       crossAxisAlignment: CrossAxisAlignment.start,
                //       children: [
                //         Text('DRIVER', style: ts12c9D9D9Dw4h1),
                //         W(5),
                //         CommonCircleAvatar(
                //             height: 20.h, image: 'khaled_bin_ali'),
                //         W(4),
                //       ],
                //     ),
                //     Column(
                //       mainAxisAlignment: MainAxisAlignment.start,
                //       crossAxisAlignment: CrossAxisAlignment.start,
                //       children: [
                //         Text('Hussain Al Nowais', style: ts12c000940w4h1),
                //         H(5),
                //         Text('ID- 123543', style: ts14c9D9D9Dw4h1),
                //       ],
                //     ),
                //   ],
                // ),
                // H(21),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _keyValueWidget({required String title, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: ts12c9D9D9Dw4h1),
        Text(value, style: ts14c000940w4h1),
      ],
    );
  }
}
