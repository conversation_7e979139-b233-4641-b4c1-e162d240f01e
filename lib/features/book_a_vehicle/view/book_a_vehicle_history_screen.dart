import 'package:addc/features/book_a_vehicle/models/book_a_vehicle_history_model.dart';
import 'package:addc/features/book_a_vehicle/providers/book_vehicle_history_provider.dart';
import 'package:addc/features/book_a_vehicle/view/book_a_vehicle_screen.dart';
import 'package:addc/features/book_a_vehicle/view/widgets/book_a_vehicle_history_tile.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../models/vehicle_booking_history_model.dart';

class BookAVehicleHistoryScreen extends StatefulWidget {
  static const route = '/book_a_vehicle_history_screen';
  const BookAVehicleHistoryScreen({super.key});

  @override
  State<BookAVehicleHistoryScreen> createState() =>
      _BookAVehicleHistoryScreenState();
}

class _BookAVehicleHistoryScreenState extends State<BookAVehicleHistoryScreen> {
  late BookVehicleHistoryProvider _provider;
  List<BookAVehicleHistoryModel> items = [];
  @override
  void initState() {
    super.initState();
    _provider = context.read<BookVehicleHistoryProvider>();
    // _provider.getBookedVehicleHistory();
    _provider.fetchVehicleBookings();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {});
  }

  final data = [
    {
      "WONUM": "DTR4761105",
      "ASSETNUM": null,
      "STATUS": "VRAPPR",
      "WOEXTEND": {"ONBEHALFOFFILENUM": "47726"},
      "TARGSTARTDATE": "2025-06-19T15:37:00+04:00",
      "DESCRIPTION": "Testing booking",
      "SCHEDSTART": null,
      "TARGCOMPDATE": "2025-06-19T15:37:00+04:00",
      "PLUSTASSETTYPE": "4X4",
      "SITEID": "ADDC",
      "ONBEHALFOF": "Axel"
    },
    {
      "WONUM": "DTR4761110",
      "ASSETNUM": null,
      "STATUS": "VRAPPR",
      "WOEXTEND": {"ONBEHALFOFFILENUM": "47726"},
      "TARGSTARTDATE": "2025-06-05T10:00:00+04:00",
      "DESCRIPTION": "New Vehicle Request for Testing",
      "SCHEDSTART": null,
      "TARGCOMPDATE": "2025-06-05T15:00:00+04:00",
      "PLUSTASSETTYPE": "4X4",
      "SITEID": "ADDC",
      "ONBEHALFOF": "Test User"
    },
    {
      "WONUM": "DTR4761014",
      "ASSETNUM": null,
      "STATUS": "VRAPPR",
      "WOEXTEND": {"ONBEHALFOFFILENUM": "47726"},
      "TARGSTARTDATE": "2025-06-04T10:00:00+04:00",
      "DESCRIPTION": "New Vehicle Request for Testing",
      "SCHEDSTART": null,
      "TARGCOMPDATE": "2025-06-04T15:00:00+04:00",
      "PLUSTASSETTYPE": "4X4",
      "SITEID": "ADDC",
      "ONBEHALFOF": "Test"
    },
    {
      "WONUM": "DTR4761015",
      "ASSETNUM": null,
      "STATUS": "VRAPPR",
      "WOEXTEND": {"ONBEHALFOFFILENUM": "47726"},
      "TARGSTARTDATE": "2025-06-05T10:00:00+04:00",
      "DESCRIPTION": "New Vehicle Request for Testing",
      "SCHEDSTART": null,
      "TARGCOMPDATE": "2025-06-05T15:00:00+04:00",
      "PLUSTASSETTYPE": "4X4",
      "SITEID": "ADDC",
      "ONBEHALFOF": "Test User"
    }
  ];

  @override
  Widget build(BuildContext context) {
    final items =
        data.map((e) => VehicleBookingHistoryModel.fromJson(e)).toList();
    return Theme(
      data: Theme.of(context).copyWith(
          dividerTheme: const DividerThemeData(color: Colors.transparent)),
      child: Scaffold(
        appBar: AppBar(
          leading: const CommonBackButton(),
          title: Text('Book a Vehicle'),
        ),
        body: Column(
          children: [
            H(29),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                      margin: EdgeInsets.symmetric(horizontal: 12.w),
                      width: double.infinity,
                      decoration: BoxDecoration(
                          color: ColorConstants.colorFFFFFF,
                          borderRadius: BorderRadius.circular(18.r)),
                      child: Consumer<BookVehicleHistoryProvider>(
                        builder: (context, provider, _) {
                          if (provider.isLoading) {
                            return _bookVehicleHistory(
                                context: context,
                                items: items,
                                isLoading: true);
                          }
                          if (!provider.isLoading &&
                              provider.vehicleBookings.isEmpty) {
                            return Center(
                              child: Text('No Data Found'),
                            );
                          }
                          return _bookVehicleHistory(
                              context: context,
                              isLoading: false,
                              items: provider.vehicleBookings);
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        persistentFooterButtons: [
          ElevatedButton(
            onPressed: () =>
                Navigator.pushNamed(context, BookAVehicleScreen.route),
            child: Text('Book a Vehicle'),
          ),
        ],
      ),
    );
  }

  Widget _bookVehicleHistory(
      {required BuildContext context,
      required List<VehicleBookingHistoryModel> items,
      required bool isLoading}) {
    return Skeletonizer(
      enabled: isLoading,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          H(29),
          Text('Booking History', style: ts16c000940w4h1),
          Flexible(
            child: ListView.separated(
              shrinkWrap: true,
              padding: EdgeInsets.symmetric(vertical: 20.h),
              itemCount: items.length,
              itemBuilder: (context, index) =>
                  BookAVehicleHistoryTile(item: items[index]),
              separatorBuilder: (context, index) => Divider(
                  height: 0, thickness: 0.5, color: ColorConstants.colorD9D9D9),
            ),
          ),
        ],
      ),
    );
  }
}
