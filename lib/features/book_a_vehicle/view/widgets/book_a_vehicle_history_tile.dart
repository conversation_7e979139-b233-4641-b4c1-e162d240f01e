import 'package:addc/features/book_a_vehicle/models/vehicle_booking_history_model.dart';
import 'package:addc/features/book_a_vehicle/view/booked_vehicle_history_details_screen.dart';
import 'package:addc/features/inbox/widgets/status_button.dart';
import 'package:addc/features/profile/view/sections/profile_avatar_section.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BookAVehicleHistoryTile extends StatelessWidget {
  final VehicleBookingHistoryModel item;
  final bool isRouteNeeded;
  const BookAVehicleHistoryTile(
      {super.key, required this.item, this.isRouteNeeded = true});

  @override
  Widget build(BuildContext context) {
    String equipment = item.plusAssetType ?? '';
    String id = item.wonum ?? '';
    // String requestedDate = item.requestedDate ?? '';Mar 10, 2025 10:00:00 - Mar 10, 2025 10:00:00
    String requestedFor = item.onBehalfof ?? '';
    String end = item.targCompDate ?? '';
    String start = item.targStartDate ?? '';
    String status = item.status ?? 'In Progress';
    String? behalfFileNo = item.woExtend?.onBehalfofFileno;
    Color statusColor = ColorConstants.colorFFBC47;
    switch (status.toLowerCase()) {
      case 'in progress':
        statusColor = ColorConstants.colorFFBC47;
        break;
      case 'rejected':
        statusColor = ColorConstants.colorFF453F;
        break;
      case 'approved':
        statusColor = ColorConstants.color0C902F;
        break;
    }
    if (start.isNotEmpty) {
      start = DateFormatter.formatDateTime(
          dateTime: DateTime.parse(start),
          outputFormat: 'MMM dd, yyyy hh:mm:ss');
    }
    if (end.isNotEmpty) {
      end = DateFormatter.formatDateTime(
          dateTime: DateTime.parse(end), outputFormat: 'MMM dd, yyyy hh:mm:ss');
    }

    return InkWell(
      onTap: isRouteNeeded
          ? () => Navigator.pushNamed(
              context, BookedVehicleHistoryDetailsScreen.route,
              arguments: BookedVehicleHistoryDetailsScreen(item: item))
          : null,
      child: Column(
        children: [
          H(13),
          Row(
            children: [
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text('ID - $id', style: ts12c9D9D9Dw4h1),
                        W(6),
                        Text('|', style: ts12c9D9D9Dw4h1),
                        W(6),
                        Text(equipment.toUpperCase(), style: ts12c9D9D9Dw4h1),
                      ],
                    ),
                    H(5),
                    Text(
                      '$start - $end',
                      style: ts14c000940w4h1,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              StatusButton(color: statusColor, text: status)
            ],
          ),
          H(12),
          Row(
            children: [
              Text('Requested for', style: ts12c9D9D9Dw4h1),
            ],
          ),
          H(4),
          Row(
            children: [
              CustomAvatar(
                  radius: (20.h / 2), name: requestedFor, fontSize: 10),
              W(4),
              Expanded(
                child: Text(
                  requestedFor,
                  style: ts12c000940w4h1,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (behalfFileNo != null) ...[
                Text('Behalf FileNo: ', style: ts11c1E1E1Ew4),
                Text(behalfFileNo, style: ts12c9D9D9Dw4h1)
              ],
            ],
          ),
          H(7),
        ],
      ),
    );
  }
}
