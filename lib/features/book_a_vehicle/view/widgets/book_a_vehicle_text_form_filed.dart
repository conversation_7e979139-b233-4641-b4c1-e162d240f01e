import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BookAVehicleTextFormFiled extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final String? title;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final InputBorder? border;
  final bool readOnly;
  final Function()? onTap;
  const BookAVehicleTextFormFiled({
    super.key,
    required this.controller,
    required this.hintText,
    this.title,
    this.prefixIcon,
    this.suffixIcon,
    this.keyboardType,
    this.inputFormatters,
    this.maxLines,
    this.border,
    this.readOnly = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return CommonTextFormField(
      controller: controller,
      contentPadding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
      titleStyle: ts14c000940w4h1,
      title: title,
      titleGap: 15.h,
      hintText: hintText,
      style: ts16c000940w4h1,
      prefixIcon: prefixIcon,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      textCapitalization: TextCapitalization.sentences,
      maxLines: maxLines,
      suffixIcon: suffixIcon,
      readOnly: readOnly,
      onTap: onTap,
      border: border ??
          OutlineInputBorder(
            borderSide: BorderSide(
              color: ColorConstants.colorF6F6F6,
              width: 0.5,
            ),
            borderRadius: BorderRadius.circular(62.r),
          ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return '$title is required';
        }
        return null;
      },
    );
  }
}
