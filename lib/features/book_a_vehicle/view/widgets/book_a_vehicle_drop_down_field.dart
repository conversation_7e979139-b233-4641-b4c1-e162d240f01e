import 'package:addc/features/widgets/common_dropdown_field.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BookAVehicleDropDownField extends StatelessWidget {
  final String hintText;
  final String? title;
  final List<DropdownMenuItem<Object?>>? items;
  final Function(dynamic)? onChanged;
  final Object? value;
  final String? Function(dynamic)? validator;
  final VoidCallback? onTap;
  const BookAVehicleDropDownField({
    super.key,
    required this.hintText,
    this.title,
    this.items,
    this.validator,
    this.value,
    this.onChanged,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return CommonDropdownFormField(
      title: title,
      titleGap: 15,
      titleStyle: ts14c000940w4h1,
      hintText: hintText,
      border: OutlineInputBorder(
          borderSide: BorderSide(color: ColorConstants.colorF6F6F6, width: 0.5),
          borderRadius: BorderRadius.circular(62.r)),
      items: items,
      onChanged: onChanged,
      onTap: onTap,
      validator: (value) {
        if (value == null || value.toString().isEmpty) {
          return 'Please select $title';
        }
        return null;
      },
    );
  }
}
