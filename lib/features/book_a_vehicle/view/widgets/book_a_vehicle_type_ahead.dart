import 'dart:async';

import 'package:addc/features/utils/extensions.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_type_ahead.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class BookAVehicleTypeAhead<T> extends StatelessWidget {
  final String? title;
  final String hintText;
  final TextEditingController controller;
  final Widget Function(BuildContext, T) itemBuilder;
  final Function(T) onSelected;
  final FutureOr<List<T>?> Function(String) suggestionsCallback;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  const BookAVehicleTypeAhead({
    super.key,
    this.title,
    this.suffixIcon,
    this.validator,
    required this.hintText,
    required this.itemBuilder,
    required this.suggestionsCallback,
    required this.onSelected,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return TypeAheadFormFieldWidget(
      title: title,
      controller: controller,
      hintText: hintText,
      titleStyle: ts14c000940w4h1,
      titleGap: 15.h,
      itemBuilder: itemBuilder,
      onSelected: onSelected,
      maxLines: 1,
      suggestionsCallback: suggestionsCallback,
      validator: validator,
      contentPadding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
      suffixIcon: Padding(
        padding: EdgeInsets.symmetric(vertical: 14),
        child: suffixIcon ?? SvgPicture.asset('dropdown_arrow'.asIconSvg()),
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(
          color: ColorConstants.colorF6F6F6,
          width: 0.5,
        ),
        borderRadius: BorderRadius.circular(62.r),
      ),
    );
  }
}
