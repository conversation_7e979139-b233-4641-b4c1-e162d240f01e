import 'package:addc/features/book_a_vehicle/providers/book_vehicle_provider.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../models/driver_category_model.dart';

class BookAVehicleDriverCategoryCheckboxTile extends StatelessWidget {
  final DriverCategoryModel item;
  const BookAVehicleDriverCategoryCheckboxTile({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    String title = item.title ?? '';
    return Row(
      children: [
        Transform.scale(
          scale: 1.1,
          child: Consumer<BookVehicleProvider>(
            builder: (context, provider, _) {
              return Checkbox(
                value: item.isSelected,
                onChanged: (value) =>
                    provider.onSelectedDriverCategory(item: item),
                side:
                    BorderSide(color: ColorConstants.colorD7D7D7, width: 0.83),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.r)),
                visualDensity:
                    const VisualDensity(horizontal: -4, vertical: -4),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                splashRadius: 50,
              );
            },
          ),
        ),
        W(12),
        Text(title, style: ts12c000940w4h1),
      ],
    );
  }
}
