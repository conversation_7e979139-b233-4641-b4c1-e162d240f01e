import 'dart:developer';
import 'package:addc/features/authentication/models/logined_user.dart';
import 'package:addc/features/book_a_vehicle/models/driver_category_model.dart';
import 'package:addc/features/book_a_vehicle/providers/book_vehicle_history_provider.dart';
import 'package:addc/features/book_a_vehicle/providers/book_vehicle_provider.dart';
import 'package:addc/features/book_a_vehicle/view/widgets/book_a_vehicle_driver_category_checkbox_tile.dart';
import 'package:addc/features/book_a_vehicle/view/widgets/book_a_vehicle_drop_down_field.dart';
import 'package:addc/features/book_a_vehicle/view/widgets/book_a_vehicle_text_form_filed.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/shared/helper/date_picker_provider.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:addc/widgets/common_circular_checkbox_tile.dart';
import 'package:date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../utils/general_functions.dart';
import 'book_a_vehicle_history_screen.dart';

class BookAVehicleScreen extends StatefulWidget {
  static const route = '/book_a_vehicle_screen';
  const BookAVehicleScreen({super.key});

  @override
  State<BookAVehicleScreen> createState() => _BookAVehicleScreenState();
}

class _BookAVehicleScreenState extends State<BookAVehicleScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _requestNoController = TextEditingController();
  final TextEditingController _requestForController = TextEditingController();
  final TextEditingController _phoneNoController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _driverFileNoController = TextEditingController();
  final TextEditingController _driverNameController = TextEditingController();
  final TextEditingController _destinationFromController =
      TextEditingController();
  final TextEditingController _destinationToController =
      TextEditingController();
  final TextEditingController _noOfPassengerController =
      TextEditingController();
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  late BookVehicleProvider _provider;
  late BookVehicleHistoryProvider _historyProvider;

  @override
  void initState() {
    super.initState();
    _provider = context.read<BookVehicleProvider>();
    _historyProvider = context.read<BookVehicleHistoryProvider>();
    _provider.selectedType = _provider.requestingTypes.first;
    _provider.driverStatus = _provider.driverStatuses.first;
  }

  @override
  Widget build(BuildContext context) {
    final datePickerProvider = context.read<DatePickerProvider>();
    return Theme(
      data: Theme.of(context).copyWith(
          dividerTheme: const DividerThemeData(color: Colors.transparent)),
      child: Scaffold(
        appBar: AppBar(
            leading: const CommonBackButton(), title: Text('Book A Vehicle')),
        body: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: SingleChildScrollView(
                      child: Form(
                        key: _formKey,
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(18.r),
                              color: ColorConstants.colorFFFFFF),
                          margin: EdgeInsets.only(
                              top: 29.h,
                              left: 12.w,
                              right: 12.w,
                              bottom: 100.h),
                          padding: EdgeInsets.only(
                              top: 29.h, left: 12.w, right: 12.w, bottom: 36.h),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Consumer<BookVehicleProvider>(
                                builder: (context, provider, _) {
                                  return Row(
                                    children: provider.requestingTypes.map(
                                      (e) {
                                        int index =
                                            provider.requestingTypes.indexOf(e);
                                        return Expanded(
                                          child: CommonCircularCheckboxTile(
                                            title: e,
                                            value: e == provider.selectedType,
                                            onChanged: (v) => provider
                                                .onChangedrequestingType(
                                                    index: index),
                                          ),
                                        );
                                      },
                                    ).toList(),
                                  );
                                },
                              ),
                              H(29),
                              BookAVehicleTextFormFiled(
                                title: 'Request for file No',
                                controller: _requestNoController,
                                hintText: 'Request No',
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly
                                ],
                              ),
                              H(25),
                              BookAVehicleTextFormFiled(
                                  title: 'Requested for',
                                  controller: _requestForController,
                                  hintText: 'Enter Name'),
                              H(25),
                              BookAVehicleTextFormFiled(
                                title: 'Phone Number',
                                controller: _phoneNoController,
                                hintText: 'Enter Number',
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly
                                ],
                                prefixIcon: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        W(16),
                                        Text('+971', style: ts16c000940w4h1),
                                        W(10),
                                        Text('|', style: ts16c9D9D9Dw4h1),
                                        W(10),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              H(25),
                              BookAVehicleTextFormFiled(
                                title: 'Description',
                                controller: _descriptionController,
                                hintText: 'Message here...',
                                maxLines: 5,
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: ColorConstants.colorF6F6F6,
                                    width: 0.5,
                                  ),
                                  borderRadius: BorderRadius.circular(20.r),
                                ),
                              ),
                              H(25),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: BookAVehicleDropDownField(
                                      title: 'Vehicle Type',
                                      hintText: 'Select',
                                      value: _provider.selectedVehicleType,
                                      // validator: (val) {
                                      //   if (val == null) {
                                      //     return 'This filed is required';
                                      //   }
                                      //   return null;
                                      // },
                                      onChanged: (val) {
                                        _provider.selectedVehicleType = val;
                                      },
                                      items: _provider.vehicleTypeList
                                          .map((e) => DropdownMenuItem(
                                                enabled: true,
                                                value: e,
                                                child: Text(e),
                                              ))
                                          .toList(),
                                    ),
                                  ),
                                  W(15),
                                  Expanded(
                                    child: BookAVehicleTextFormFiled(
                                      title: 'No.of Passengers',
                                      controller: _noOfPassengerController,
                                      hintText: 'Enter number',
                                      keyboardType: TextInputType.number,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              H(25),
                              BookAVehicleDropDownField(
                                  title: 'Purpose',
                                  hintText: 'Select',
                                  value: _provider.selectedVehiclePupose,
                                  // validator: (val) {
                                  //   if (val == null) {
                                  //     return 'This filed is required';
                                  //   }
                                  //   return null;
                                  // },
                                  onChanged: (val) {
                                    _provider.selectedVehiclePupose = val;
                                  },
                                  items: _provider.vehiclePuposes
                                      .map((e) => DropdownMenuItem(
                                            enabled: true,
                                            value: e,
                                            child: Text(e),
                                          ))
                                      .toList()),
                              H(25),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: BookAVehicleTextFormFiled(
                                      title: 'Est. Start Date & Time',
                                      controller: _startDateController,
                                      hintText: 'Select',
                                      maxLines: 1,
                                      keyboardType: TextInputType.number,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                      suffixIcon: Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 13.h),
                                          child: SvgPicture.asset(
                                              'calendar_3'.asIconSvg())),
                                      readOnly: true,
                                      onTap: () async {
                                        DateTime? dateTime =
                                            await datePickerProvider
                                                .dateTimePicker(
                                                    context: context);
                                        if (dateTime != null) {
                                          _startDateController.text =
                                              DateFormatter.formatDateTime(
                                                  dateTime: dateTime,
                                                  outputFormat:
                                                      'dd-MMM-yyyy hh:mm a');
                                        }
                                      },
                                    ),
                                  ),
                                  W(15),
                                  Expanded(
                                    child: BookAVehicleTextFormFiled(
                                      title: 'Est. End Date & Time',
                                      controller: _endDateController,
                                      hintText: 'Select',
                                      maxLines: 1,
                                      keyboardType: TextInputType.number,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                      suffixIcon: Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 13.h),
                                          child: SvgPicture.asset(
                                              'calendar_3'.asIconSvg())),
                                      readOnly: true,
                                      onTap: () async {
                                        DateTime? dateTime =
                                            await datePickerProvider
                                                .dateTimePicker(
                                                    context: context);
                                        if (dateTime != null) {
                                          _endDateController.text =
                                              DateFormatter.formatDateTime(
                                                  dateTime: dateTime,
                                                  outputFormat:
                                                      'dd-MMM-yyyy hh:mm a');
                                        }
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              H(25),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: BookAVehicleTextFormFiled(
                                      title: 'Destination From',
                                      controller: _destinationFromController,
                                      hintText: 'Enter',
                                    ),
                                  ),
                                  W(15),
                                  Expanded(
                                    child: BookAVehicleTextFormFiled(
                                      title: 'Destination To',
                                      controller: _destinationToController,
                                      hintText: 'Enter',
                                    ),
                                  ),
                                ],
                              ),
                              H(25),
                              Text('Driver Required', style: ts14c000940w4h1),
                              H(15),
                              Consumer<BookVehicleProvider>(
                                builder: (context, provider, _) {
                                  return Row(
                                    children: provider.driverStatuses.map(
                                      (e) {
                                        int index =
                                            provider.driverStatuses.indexOf(e);
                                        return Expanded(
                                          child: CommonCircularCheckboxTile(
                                            title: e,
                                            value: e == provider.driverStatus,
                                            onChanged: (v) =>
                                                provider.onChangedDriverStatus(
                                                    index: index),
                                          ),
                                        );
                                      },
                                    ).toList(),
                                  );
                                },
                              ),
                              H(25),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: BookAVehicleTextFormFiled(
                                      title: 'Driver File No',
                                      controller: _driverFileNoController,
                                      hintText: 'Enter',
                                      keyboardType: TextInputType.number,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                    ),
                                  ),
                                  W(15),
                                  Expanded(
                                    child: BookAVehicleTextFormFiled(
                                      title: 'Name',
                                      controller: _driverNameController,
                                      hintText: 'Enter',
                                    ),
                                  ),
                                ],
                              ),
                              H(25),
                              ListView.separated(
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: _provider.driverCategories.length,
                                itemBuilder: (context, index) {
                                  DriverCategoryModel item =
                                      _provider.driverCategories[index];
                                  return BookAVehicleDriverCategoryCheckboxTile(
                                      item: item);
                                },
                                separatorBuilder: (context, index) => H(12),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        persistentFooterButtons: [
          Selector<BookVehicleProvider, bool>(
            selector: (_, provider) => provider.isLoading,
            builder: (context, isLoading, _) {
              if (isLoading) {
                return Center(child: CircularProgressIndicator());
              }
              return Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: ColorConstants.color8B8D97),
                      onPressed: () => Navigator.pop(context),
                      child: Text('Cancel'),
                    ),
                  ),
                  W(15),
                  Expanded(
                    child: ElevatedButton(
                      // onPressed: () => successBottomsheet(
                      //   context: context,
                      //   title: 'Request submitted\nsuccessfully',
                      //   onPressed: () => Navigator.popUntil(context,
                      //       ModalRoute.withName(BookAVehicleHistoryScreen.route)),
                      // ),
                      onPressed: () async {
                        if (_formKey.currentState!.validate()) {
                          final payload = {
                            "DESCRIPTION": _descriptionController.text.trim(),
                            "REPORTEDBY": _provider.selectedType ==
                                    _provider.requestingTypes.first
                                ? LoginedUser.displayName
                                : _requestForController.text.trim(),
                            "ONBEHALFOF": _requestForController.text.trim(),
                            "PLUSGREVIEWEREMAIL": LoginedUser.mail,
                            "PLUSTASSETTYPE": _provider.selectedVehicleType,
                            "PLUSTPASSENGER":
                                _noOfPassengerController.text.trim(),
                            "TARGSTARTDATE": _convertToAPIDate(
                                _startDateController.text.trim()),
                            "TARGCOMPDATE": _convertToAPIDate(
                                _endDateController.text.trim()),
                            "DESTINATIONFROM":
                                _destinationFromController.text.trim(),
                            "DESTINATIONTO":
                                _destinationToController.text.trim(),
                            "PLUSTDRIVERREQ":
                                _provider.driverStatus == "Yes" ? "1" : "0",
                            "CASTPROTECTION":
                                _provider.driverCategories[0].isSelected == true
                                    ? "1"
                                    : "0",
                            "SALIK":
                                _provider.driverCategories[1].isSelected == true
                                    ? "1"
                                    : "0",
                            "HOOK":
                                _provider.driverCategories[2].isSelected == true
                                    ? "1"
                                    : "0",
                            "WOEXTEND": {
                              "REPORTEDBYFILENO":
                                  _requestNoController.text.trim(),
                              "ONBEHALFOFFILENUM":
                                  _requestNoController.text.trim(),
                              "ONBEHALFOFPHONE":
                                  "+971${_phoneNoController.text.trim()}",
                              "VRAPPROVERFILENAME": _driverNameController.text
                                  .trim(), // Can be static or dynamic
                              "VRAPPROVERFILENUM":
                                  _driverFileNoController.text.trim(),
                              "VRPURPOSE": _provider.selectedVehiclePupose,
                              "PLUSTOPERATORFILENUM":
                                  _driverFileNoController.text.trim(),
                              "PLUSTOPERATORNAME":
                                  _driverNameController.text.trim(),
                            }
                          };
                          log('payload - $payload');
                          final success = await _provider.bookVehicle(payload);

                          if (success) {
                            _historyProvider.fetchVehicleBookings();
                            if (!context.mounted) return;
                            successBottomsheet(
                              context: context,
                              title: 'Request submitted\nsuccessfully',
                              onPressed: () => Navigator.popUntil(
                                  context,
                                  ModalRoute.withName(
                                      BookAVehicleHistoryScreen.route)),
                            );
                          } else {
                            if (!context.mounted) return;
                            // Show Error Snackbar
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                  content: Text(_provider.errorMessage ??
                                      'Something went wrong')),
                            );
                          }
                        }
                      },
                      child: Text('Submit'),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  String _convertToAPIDate(String date) {
    try {
      final inputFormat = DateFormat('dd-MMM-yyyy hh:mm a');
      final outputFormat = DateFormat(
          'yyyy-MM-ddTHH:mm:ss'); // API needs ISO like 2019-06-17T10:00:00
      final dateTime = inputFormat.parse(date);
      return outputFormat.format(dateTime);
    } catch (e) {
      return "";
    }
  }
}
