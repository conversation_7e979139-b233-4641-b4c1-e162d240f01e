class VehicleBookingHistoryModel {
  String? wonum;
  String? assetNum;
  String? status;
  WOExtend? woExtend;
  String? targStartDate;
  String? description;
  String? schedStart;
  String? targCompDate;
  String? plusAssetType;
  String? siteId;
  String? onBehalfof;

  VehicleBookingHistoryModel({
    this.wonum,
    this.assetNum,
    this.status,
    this.woExtend,
    this.targStartDate,
    this.description,
    this.schedStart,
    this.targCompDate,
    this.plusAssetType,
    this.siteId,
    this.onBehalfof,
  });

  factory VehicleBookingHistoryModel.fromJson(Map<String, dynamic> json) {
    return VehicleBookingHistoryModel(
      wonum: json['WONUM'],
      assetNum: json['ASSETNUM'],
      status: json['STATUS'],
      woExtend:
          json['WOEXTEND'] != null ? WOExtend.fromJson(json['WOEXTEND']) : null,
      targStartDate: json['TARGSTARTDATE'],
      description: json['DESCRIPTION'],
      schedStart: json['SCHEDSTART'],
      targCompDate: json['TARGCOMPDATE'],
      plusAssetType: json['PLUSTASSETTYPE'],
      siteId: json['SITEID'],
      onBehalfof: json['ONBEHALFOF'],
    );
  }
}

class WOExtend {
  String? onBehalfofFileno;
  WOExtend({this.onBehalfofFileno});
  factory WOExtend.fromJson(Map<String, dynamic> json) {
    return WOExtend(
      onBehalfofFileno: json['ONBEHALFOFFILENUM'],
    );
  }
}
