class BookAVehicleHistoryModel {
  String? id;
  String? equipment;
  String? status;
  Schedule? schedule;
  String? requestedFor;
  String? requestedDate;
  String? requestFor;
  String? phoneNumber;
  String? alternatePhone;
  String? description;
  String? vehicleType;
  int? numberOfPassengers;
  String? purpose;
  String? destinationFrom;
  String? destinationTo;
  String? driver;
  String? driverId;

  BookAVehicleHistoryModel(
      {this.id,
      this.equipment,
      this.status,
      this.schedule,
      this.requestedFor,
      this.requestedDate,
      this.requestFor,
      this.phoneNumber,
      this.alternatePhone,
      this.description,
      this.vehicleType,
      this.numberOfPassengers,
      this.purpose,
      this.destinationFrom,
      this.destinationTo,
      this.driver,
      this.driverId});

  BookAVehicleHistoryModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    equipment = json['equipment'];
    status = json['status'];
    schedule =
        json['schedule'] != null ? Schedule.fromJson(json['schedule']) : null;
    requestedFor = json['requested_for'];
    requestedDate = json['requested_date'];
    requestFor = json['request_for'];
    phoneNumber = json['phone_number'];
    alternatePhone = json['alternate_phone'];
    description = json['description'];
    vehicleType = json['vehicle_type'];
    numberOfPassengers = json['number_of_passengers'];
    purpose = json['purpose'];
    destinationFrom = json['destination_from'];
    destinationTo = json['destination_to'];
    driver = json['driver'];
    driverId = json['driver_id'];
  }
}

class Schedule {
  String? start;
  String? end;

  Schedule({this.start, this.end});

  Schedule.fromJson(Map<String, dynamic> json) {
    start = json['start'];
    end = json['end'];
  }
}
