import 'dart:developer';

import 'package:addc/features/book_a_vehicle/models/book_a_vehicle_history_model.dart';
import 'package:addc/features/book_a_vehicle/models/vehicle_booking_history_model.dart';
import 'package:addc/features/book_a_vehicle/services/vehicle_booking_services.dart';
import 'package:flutter/material.dart';

class BookVehicleHistoryProvider extends ChangeNotifier {
  bool isLoading = false;
  final VehicleBookingService _bookingService = VehicleBookingService();
  List<BookAVehicleHistoryModel> bookVehicleHistory = [];
  Future<void> getBookedVehicleHistory() async {
    bookVehicleHistory.clear();
    isLoading = true;
    await Future.delayed(const Duration(seconds: 2));
    isLoading = false;
    log('message');
    final data = [
      {
        "id": "TR343587",
        "equipment": "Generator",
        "status": "In Progress",
        "schedule": {
          "start": "2025-03-10T10:00:00",
          "end": "2025-03-12T12:30:00"
        },
        "requested_for": "Mansour Al-Juaid",
        "requested_date": "01 Mar, 2025",
        "request_for": "File No",
        "phone_number": "124563211",
        "alternate_phone": "+971 2 1234567",
        "description": "Lorem ipsum dolor sit amet consectetur.",
        "vehicle_type": "Generator",
        "number_of_passengers": 2,
        "purpose": "Official",
        "destination_from": "Al Nahyan",
        "destination_to": "Al Wahda",
        "driver": "Hussain Al Nowais",
        "driver_id": "123543"
      },
      {
        "id": "TR343588",
        "equipment": "Air Compressor",
        "status": "Approved",
        "schedule": {
          "start": "2025-03-05T08:00:00",
          "end": "2025-03-05T16:00:00"
        },
        "requested_for": "Fatima Al-Nuaimi",
        "requested_date": "01 Apr, 2025",
        "request_for": "File No",
        "phone_number": "124563211",
        "alternate_phone": "+971 2 1234567",
        "description": "Lorem ipsum dolor sit amet consectetur.",
        "vehicle_type": "Generator",
        "number_of_passengers": 2,
        "purpose": "Official",
        "destination_from": "Al Nahyan",
        "destination_to": "Al Wahda",
        "driver": "Hussain Al Nowais",
        "driver_id": "123543"
      },
      {
        "id": "TR343589",
        "equipment": "Forklift",
        "status": "Approved",
        "schedule": {
          "start": "2025-04-01T09:00:00",
          "end": "2025-04-01T17:30:00"
        },
        "requested_for": "Ahmed Al-Mansoori",
        "requested_date": "01 May, 2025",
        "request_for": "File No",
        "phone_number": "124563211",
        "alternate_phone": "+971 2 1234567",
        "description": "Lorem ipsum dolor sit amet consectetur.",
        "vehicle_type": "Generator",
        "number_of_passengers": 2,
        "purpose": "Official",
        "destination_from": "Al Nahyan",
        "destination_to": "Al Wahda",
        "driver": "Hussain Al Nowais",
        "driver_id": "123543"
      },
      {
        "id": "TR343590",
        "equipment": "Water Pump",
        "status": "Rejected",
        "schedule": {
          "start": "2025-03-15T07:30:00",
          "end": "2025-03-16T15:00:00"
        },
        "requested_for": "Salma Al-Ketbi",
        "requested_date": "01 Jun, 2025",
        "request_for": "File No",
        "phone_number": "124563211",
        "alternate_phone": "+971 2 1234567",
        "description": "Lorem ipsum dolor sit amet consectetur.",
        "vehicle_type": "Generator",
        "number_of_passengers": 2,
        "purpose": "Official",
        "destination_from": "Al Nahyan",
        "destination_to": "Al Wahda",
        "driver": "Hussain Al Nowais",
        "driver_id": "123543"
      }
    ];
    bookVehicleHistory =
        data.map((e) => BookAVehicleHistoryModel.fromJson(e)).toList();
    notifyListeners();
  }

  List<VehicleBookingHistoryModel> vehicleBookings = [];
  Future<void> fetchVehicleBookings() async {
    try {
      vehicleBookings.clear();
      isLoading = true;
      List<VehicleBookingHistoryModel> bookings =
          await _bookingService.getVehicleBookings();
      bookings = bookings.where((element) => element.wonum != null).toList();
      if (bookings.isNotEmpty) {
        vehicleBookings = bookings;
      }
      isLoading = false;
    } catch (e) {
      print('Error fetching bookings: $e');
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }
}
