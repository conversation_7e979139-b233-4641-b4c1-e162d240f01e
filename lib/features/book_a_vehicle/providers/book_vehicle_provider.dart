import 'package:addc/features/book_a_vehicle/models/driver_category_model.dart';
import 'package:addc/features/book_a_vehicle/services/vehicle_booking_services.dart';
import 'package:flutter/material.dart';

class BookVehicleProvider extends ChangeNotifier {
  List<String> requestingTypes = ['Request for myself', 'Request for others'];
  String selectedType = 'Request for myself';
  onChangedrequestingType({required int index}) {
    selectedType = requestingTypes[index];
    notifyListeners();
  }

  String? selectedVehicleType;
  List<String> vehicleTypeList = ['4x4'];
  String? selectedVehiclePupose;
  List<String> vehiclePuposes = ['OTHER'];
  List<String> destinations = [
    'Bahrain',
    'Kuwait',
    'Oman',
    'Qatar',
    'Saudi Arabia',
    'United Arab Emirates (UAE)'
  ];
  List<String> driverStatuses = ['Yes', 'No'];
  String driverStatus = 'Yes';
  onChangedDriverStatus({required int index}) {
    driverStatus = driverStatuses[index];
    notifyListeners();
  }

  List<DriverCategoryModel> driverCategories = [
    DriverCategoryModel(
        title: 'Coastal Protection Authority', isSelected: false),
    DriverCategoryModel(title: 'Salik', isSelected: false),
    DriverCategoryModel(title: 'Hook', isSelected: false),
  ];

  onSelectedDriverCategory({required DriverCategoryModel item}) {
    driverCategories.map((e) => e.isSelected = false).toList();
    item.isSelected = true;
    notifyListeners();
  }

  final VehicleBookingService _service = VehicleBookingService();

  bool _isLoading = false;
  String? _errorMessage;

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  Future<bool> bookVehicle(Map<String, dynamic> payload) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await _service.bookVehicle(payload);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        _errorMessage = 'Failed to submit: ${response.data}';
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
