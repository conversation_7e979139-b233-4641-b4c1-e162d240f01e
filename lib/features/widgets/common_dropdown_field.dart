import 'package:addc/features/utils/extensions.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'gap.dart';

class CommonDropdownFormField extends StatelessWidget {
  final String hintText;
  final List<DropdownMenuItem<Object?>>? items;
  final Function(dynamic)? onChanged;
  final Object? value;
  final String? Function(dynamic)? validator;
  final InputBorder? border;
  final Color? filledColor;
  final VoidCallback? onTap;
  final Widget? icon;
  final TextStyle? style;
  final TextStyle? titleStyle;
  final String? title;
  final double? titleGap;
  const CommonDropdownFormField({
    super.key,
    required this.hintText,
    this.items,
    this.onChanged,
    this.value,
    this.validator,
    this.border,
    this.filledColor,
    this.onTap,
    this.icon,
    this.style,
    this.title,
    this.titleStyle,
    this.titleGap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (title != null) ...[
          Align(
              alignment: Alignment.topLeft,
              child: Text(title ?? '', style: titleStyle ?? ts12c9D9D9Dw4)),
          H(titleGap ?? 8),
        ],
        DropdownButtonFormField(
          icon: icon ?? SvgPicture.asset('dropdown_arrow'.asIconSvg()),
          items: items,
          style: style,
          onChanged: onChanged,
          onTap: onTap,
          hint: FittedBox(
            child: Text(
              hintText,
              style: ts15c9D9D9Dw4h1,
            ),
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: filledColor ?? ColorConstants.colorF6F6F6,
            contentPadding:
                EdgeInsets.symmetric(horizontal: 17.w, vertical: 12.5.h),
            border: border,
            enabledBorder: border ?? _border(),
            focusedBorder: border ?? _border(),
            disabledBorder: border ?? _border(),
            errorBorder: border ??
                OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.red[700]!),
                  borderRadius: BorderRadius.circular(5.r),
                ),
          ),
          value: value,
          validator: validator,
          isExpanded: true,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        ),
      ],
    );
  }

  OutlineInputBorder _border() {
    return OutlineInputBorder(
      borderSide: BorderSide(color: ColorConstants.colorF6F6F6, width: .5),
      borderRadius: BorderRadius.circular(20.r),
    );
  }
}
