import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../shared/constants/color_constants.dart';
import '../../shared/constants/text_styles.dart';

class CommonSuccessBottomSheet extends StatelessWidget {
  final BottomSheetStatus? status;
  final String title;
  final VoidCallback? onPressed;
  final String? buttonTitle;
  final String? desciption;
  const CommonSuccessBottomSheet({
    super.key,
    required this.title,
    this.buttonTitle,
    this.onPressed,
    this.status,
    this.desciption,
  });

  @override
  Widget build(BuildContext context) {
    String bgImage = 'success';
    if (status == BottomSheetStatus.warning ||
        status == BottomSheetStatus.failure) {
      bgImage = 'warning';
    }
    return Padding(
      padding: EdgeInsets.all(12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ConvexAppBar(
            curveSize: 75,
            cornerRadius: 30.r,
            initialActiveIndex: 0,
            style: TabStyle.fixedCircle,
            curve: Curves.decelerate,
            elevation: 0,
            backgroundColor: ColorConstants.colorFFFFFF,
            items: [
              TabItem(
                icon: SvgPicture.asset(bgImage.asIconSvg(),
                    height: 80.h, width: 80.w),
              )
            ],
          ),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
                color: ColorConstants.colorFFFFFF,
                borderRadius:
                    BorderRadius.vertical(bottom: Radius.circular(30.r))),
            child: Column(
              children: [
                H(10),
                Text(
                  title,
                  style: ts26c000940w4h1,
                  textAlign: TextAlign.center,
                ),
                if (desciption != null) ...[
                  H(18.65),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12.0),
                    child: Text(
                      desciption ?? '',
                      style: ts14c9D9D9Dw4h1,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
                if (onPressed != null) ...[
                  H(20),
                  ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          minimumSize: Size(155.w, 50.h)),
                      onPressed: onPressed,
                      child: Text(buttonTitle ?? 'Okay')),
                ],
                H(20),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

enum BottomSheetStatus { success, warning, failure, none }
