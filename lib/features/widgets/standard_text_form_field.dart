import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../shared/constants/color_constants.dart';
import '../../widgets/common_text_form_field.dart';

class StandartTextFormField extends StatelessWidget {
  final String hintText;
  final TextEditingController? controller;
  final int maxLines;
  final double borderRaduis;
  final String? Function(String?)? validator;
  const StandartTextFormField(
      {super.key,
      required this.hintText,
      this.controller,
      this.maxLines = 1,
      this.borderRaduis = 62,
      this.validator});

  @override
  Widget build(BuildContext context) {
    return CommonTextFormField(
      hintText: hintText,
      controller: controller,
      maxLines: maxLines,
      contentPadding: EdgeInsets.symmetric(horizontal: 17.w, vertical: 12.5.h),
      validator: validator,
      border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRaduis.r),
          borderSide: BorderSide(color: ColorConstants.colorF6F6F6)),
    );
  }
}
