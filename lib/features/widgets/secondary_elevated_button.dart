import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SecondaryElevatedButton extends StatelessWidget {
  final String text;
  final VoidCallback? ontap;
  const SecondaryElevatedButton({super.key,required this.text,this.ontap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: ontap,
      child: Container(
        height: 52.h,
        decoration: BoxDecoration(
          color: ColorConstants.color8B8D97,
          borderRadius: BorderRadius.circular(38),
        ),
        child: Center(child: Text(text,style: ts16cFFFFFFw4,),),
      ),
    );
  }
}