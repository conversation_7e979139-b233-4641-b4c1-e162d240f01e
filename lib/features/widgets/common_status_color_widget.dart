import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../shared/constants/text_styles.dart';

class CommonStatusColorWidget extends StatelessWidget {
  final String status;
  final Color color;
  const CommonStatusColorWidget(
      {super.key, required this.color, required this.status});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 7.h),
      decoration: BoxDecoration(
          color: color, borderRadius: BorderRadius.circular(20.r)),
      child: Text(status, style: ts13cFFFFFFw4h1),
    );
  }
}
