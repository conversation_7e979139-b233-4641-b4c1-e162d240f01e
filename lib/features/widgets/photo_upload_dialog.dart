import 'package:addc/shared/helper/image_picker_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';

import 'gap.dart';

class PhotoUploadDialog extends StatelessWidget {
  final VoidCallback? onCamera;
  final VoidCallback? onGallery;
  const PhotoUploadDialog({super.key, this.onCamera, this.onGallery});

  @override
  Widget build(BuildContext context) {
    final provider = context.read<ImagePickerProvider>();
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.h)),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 20.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Select your source'),
            H(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                InkWell(
                  onTap: onCamera ??
                      () async => await provider.imagePicker(
                          source: ImageSource.camera),
                  child: Column(
                    children: [
                      const Icon(Icons.camera),
                      H(5),
                      Text('Camera'),
                    ],
                  ),
                ),
                InkWell(
                  onTap: onGallery ??
                      () async => await provider.imagePicker(
                          source: ImageSource.gallery),
                  child: Column(
                    children: [
                      const Icon(Icons.photo_library_rounded),
                      H(5),
                      Text('Gallery'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
