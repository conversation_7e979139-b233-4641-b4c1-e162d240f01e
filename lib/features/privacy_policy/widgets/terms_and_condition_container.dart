import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TermsAndConditionContainer extends StatelessWidget {
  const TermsAndConditionContainer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 9.w, top: 18.h),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(18), topRight: Radius.circular(18)),
          color: ColorConstants.colorFFFFFF),
      child: ListView(
        children: [
          Text(
            "By accessing or using the App, you agree to comply with and be bound by these Terms. Please read them carefully.",
            style: ts12c9D9D9Dw4h1,
          ),
          H(14),
          Text(
            "Acceptance of Terms",
            style: ts16c000940w4,
          ),
          H(5),
          Text(
            "By registering for or using the App, you agree to these Terms, our Privacy Policy, and any additional policies or guidelines we may introduce. If you do not agree, you must not use the App.",
            style: ts12c9D9D9Dw4h1,
          ),
          H(17),
          Text(
            "User Responsibilities",
            style: ts16c000940w4,
          ),
          H(5),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(text: 'Account Security', style: ts12c1E1E1Ew4),
                TextSpan(
                  text:
                      ': You are responsible for maintaining the confidentiality of your account credentials and for all activities under your account.',
                  style: ts12c9D9D9Dw4h1,
                ),
              ],
            ),
          ),
          H(6),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(text: 'Compliance', style: ts12c1E1E1Ew4),
                TextSpan(
                  text:
                      ': You agree to use the App only for lawful purposes and in accordance with all applicable laws and regulations.',
                  style: ts12c9D9D9Dw4h1,
                ),
              ],
            ),
          ),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(text: 'Data Accuracy', style: ts12c1E1E1Ew4),
                TextSpan(
                  text:
                      ': You are responsible for ensuring that all information entered into the App is accurate and up-to-date.',
                  style: ts12c9D9D9Dw4h1,
                ),
              ],
            ),
          ),
          H(17),
          Text(
            "Permitted Use",
            style: ts16c000940w4,
          ),
          H(5),
          Text(
              "The App is designed for managing human resource activities such as attendance and leave management. You agree not to:\n\t\t\t1.\t\tMisuse the App in any way, including by attempting to access un authorized areas or data.\n\t\t\t2.\t\tReverse engineer, decompile, or modify the App.",
              style: ts12c9D9D9Dw4h1),
          H(14),
          Text(
            "Data Privacy",
            style: ts16c000940w4,
          ),
          H(5),
          Text(
              "We are committed to protecting your data in accordance with our Privacy Policy. By using the App, you consent to the collection, storage, and processing of your data as described in the Privacy Policy.",
              style: ts12c9D9D9Dw4h1),
        ],
      ),
    );
  }
}
