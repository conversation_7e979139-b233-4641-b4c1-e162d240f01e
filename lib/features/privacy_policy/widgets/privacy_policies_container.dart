import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PrivacyPoliciesContainer extends StatelessWidget {
  const PrivacyPoliciesContainer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 9.w, top: 18.h),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(18), topRight: Radius.circular(18)),
          color: ColorConstants.colorFFFFFF),
      child: ListView(
        children: [
          Text(
            "This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our App. Please read this policy carefully. By using the App, you consent to the practices described in this Privacy Policy.",
            style: ts12c9D9D9Dw4h1,
          ),
          H(6),
          Text(
            "Information We Collect",
            style: ts16c000940w4,
          ),
          H(5),
          Text(
            "Personal Information:",
            style: ts12c1E1E1Ew4,
          ),
          Text(
            "When you register or use our app, we may collect:\nName\nEmail\naddress\nPhone number\nEmployee ID\nProfile Picture (optional)\nJob Title and Department\nEmergency Contact Information ",
            style: ts12c9D9D9Dw4,
          ),
          H(15),
          Text(
            "Usage Data:",
            style: ts12c1E1E1Ew4,
          ),
          Text(
            "We may automatically collect information about how you interact with the App, including:\nDevice Information (e.g., model, operating system, unique device ID)\nIP Address\nApp Usage Data (e.g., pages visited, features accessed, session duration)",
            style: ts12c9D9D9Dw4,
          ),
          H(15),
          Text(
            "Sensitive Information:",
            style: ts12c1E1E1Ew4,
          ),
          Text(
            "Where applicable, we may collect sensitive data, such as:\n\t\t\t\u2022\t\t\tAttendance records\n\t\t\t\u2022\t\t\tLeave requests and approvals",
            style: ts12c9D9D9Dw4h14,
          ),
          H(15),
          Text(
            "How We Use Your Information",
            style: ts16c000940w4,
          ),
          H(6),
          Text(
            "We use the information we collect to:\n\t\t\t\u2022\t\t\tFacilitate your access to and use of the App’s features.\n\t\t\t\u2022\t\t\tMaintain accurate employee records.\n\t\t\t\u2022\t\t\tProcess attendance and leave requests.\n\t\t\t\u2022\t\t\tImprove App functionality and user experience.\n\t\t\t\u2022\t\t\tCommunicate important updates, announcements, or changes.\n\t\t\t\u2022\t\t\tComply with legal obligations and enforce company policies",
            style: ts12c9D9D9Dw4h1,
          ),
          H(15),
          Text(
            "How We Share Your Information",
            style: ts16c000940w4,
          ),
          H(6),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: 'Internal Departments',
                  style: ts12c000940w4,
                ),
                TextSpan(
                  text:
                      ': Relevant teams such as HR and IT support may access your data to perform their duties.',
                  style: ts12c9D9D9Dw4h1,
                ),
              ],
            ),
          ),
          H(15),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: 'Third-Party Service Providers',
                  style: ts12c000940w4,
                ),
                TextSpan(
                  text:
                      ': We may share data with trusted service providers who help us Host and maintain the App, Deliver notifications and communication services.',
                  style: ts12c9D9D9Dw4h1,
                ),
              ],
            ),
          ),
          H(15),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: 'Legal Requirements',
                  style: ts12c000940w4,
                ),
                TextSpan(
                  text:
                      ': We may disclose your information to comply with applicable laws, regulations, or legal processes.',
                  style: ts12c9D9D9Dw4h1,
                ),
              ],
            ),
          ),
          H(15),
          Text(
            "Data Security",
            style: ts16c000940w4,
          ),
          H(6),
          Text(
            "We implement robust security measures to protect your data, including:\n\t\t\t\u2022\t\t\tEncryption of data in transit and at rest.\n\t\t\t\u2022\t\t\tRegular security audits.\n\t\t\t\u2022\t\t\tRestricted access to sensitive information.",
            style: ts12c9D9D9Dw4h1,
          ),
          H(26),
        ],
      ),
    );
  }
}
