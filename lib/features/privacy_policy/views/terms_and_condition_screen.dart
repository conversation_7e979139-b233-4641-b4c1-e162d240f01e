import 'package:addc/features/privacy_policy/widgets/terms_and_condition_container.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TermsAndConditionScreen extends StatelessWidget {
  static const route = '/terms_and_condition';
  const TermsAndConditionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: Text(
            'Terms & Conditions',
            style: ts20c000940w4,
          ),
          leading: const CommonBackButton(),
        ),
        body: Padding(
          padding: EdgeInsets.only(left: 12.w, right: 12.w, top: 25.h),
          child: TermsAndConditionContainer(),
        ));
  }
}
