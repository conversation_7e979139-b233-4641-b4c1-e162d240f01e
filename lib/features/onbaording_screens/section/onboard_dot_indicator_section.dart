import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../shared/constants/color_constants.dart';
import '../../widgets/gap.dart';
import '../providers/onboard_provider.dart';

class OnboardDotIndicatorSection extends StatelessWidget {
  const OnboardDotIndicatorSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.0.w),
      child: SizedBox(
        height: 8.h,
        child: Consumer<OnboardProvider>(
          builder: (context, provider, _) {
            return ListView.separated(
              itemCount: provider.onBoardingModels.length,
              scrollDirection: Axis.horizontal,
              itemBuilder: (context, index) {
                bool isSelected = index == provider.selectedIndex;
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: isSelected ? 40 : 12.w,
                  decoration: BoxDecoration(
                      color: isSelected
                          ? ColorConstants.color0AB3A1
                          : ColorConstants.colorFFFFFF,
                      borderRadius: BorderRadius.circular(62)),
                );
              },
              separatorBuilder: (context, index) => W(6.w),
            );
          },
        ),
      ),
    );
  }
}
