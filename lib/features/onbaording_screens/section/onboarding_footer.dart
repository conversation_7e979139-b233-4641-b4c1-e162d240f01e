import 'package:addc/features/authentication/view/login_screen.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../shared/constants/color_constants.dart';
import '../providers/onboard_provider.dart';

class OnboardingFooter extends StatelessWidget {
  final PageController pageController;
  const OnboardingFooter({super.key, required this.pageController});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0.w),
          child: Consumer<OnboardProvider>(
            builder: (context, provider, _) {
              bool showSkipButton = provider.selectedIndex < 2;
              bool isEnabledContinueButton = provider.selectedIndex ==
                  provider.onBoardingModels.length - 1;
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (!showSkipButton) const SizedBox.shrink(),
                  if (showSkipButton)
                    TextButton(
                      style: ButtonStyle(
                          foregroundColor: WidgetStatePropertyAll(
                              ColorConstants.colorFFFFFF)),
                      child: Text('Skip'),
                      onPressed: () {
                        provider.selectedIndex =
                            provider.onBoardingModels.length - 1;
                        pageController.animateToPage(provider.selectedIndex,
                            duration: const Duration(milliseconds: 800),
                            curve: Curves.easeIn);
                        _onConituePressed(context: context);
                      },
                    ),
                  AnimatedContainer(
                    alignment: Alignment.center,
                    width: isEnabledContinueButton
                        ? MediaQuery.of(context).size.width - 40.h
                        : 120.w,
                    duration: const Duration(milliseconds: 500),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                                minimumSize: Size(120.w, 52.h)),
                            child: Text(
                                isEnabledContinueButton ? 'Continue' : 'Next'),
                            onPressed: () {
                              if (!isEnabledContinueButton) {
                                provider.selectedIndex =
                                    provider.selectedIndex + 1;
                                pageController.animateToPage(
                                    provider.selectedIndex,
                                    duration: const Duration(milliseconds: 800),
                                    curve: Curves.easeIn);
                              }
                              if (isEnabledContinueButton) {
                                _onConituePressed(context: context);
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        H(40.h),
      ],
    );
  }

  _onConituePressed({required BuildContext context}) {
    Navigator.pushNamed(context, LoginScreen.route);
  }
}
