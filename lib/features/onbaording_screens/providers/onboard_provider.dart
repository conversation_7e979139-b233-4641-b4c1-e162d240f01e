import 'package:flutter/material.dart';

import '../models/onboard_model.dart';

class OnboardProvider extends ChangeNotifier {
  int _selectedIndex = 0;
  int get selectedIndex => _selectedIndex;
  set selectedIndex(int index) {
    _selectedIndex = index;
    notifyListeners();
  }

  final List<OnboardingModel> onBoardingModels = [
    // OnboardingModel(
    //   title: 'Employee-centric\nmobile hub',
    //   subTitle:
    //       'Seamlessly access all available mobile\napps to simplify your work.',
    //   backgroundgImage: 'onboarding_1',
    // ),
    OnboardingModel(
      title: 'Welcome to\nMyTQD',
      subTitle:
          'Your new employee app is here. Enjoy faster performance, a modern interface, and easier access to everything you need - all in one place.',
      backgroundgImage: 'onboarding_1_temp',
    ),
    OnboardingModel(
      title: 'Your daily\ntools at a glance',
      subTitle:
          'Log in, check your leave balance, review your attendance and punch-in online.',
      backgroundgImage: 'onboarding_2',
    ),
    OnboardingModel(
      title: 'Smarter\naccess starts here',
      subTitle:
          'Use the new Apps Hub to quickly reach the mobile tools you need every day.',
      backgroundgImage: 'onboarding_3',
    ),
  ];
}
