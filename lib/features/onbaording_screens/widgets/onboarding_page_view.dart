import 'package:addc/features/onbaording_screens/models/onboard_model.dart';
import 'package:addc/features/utils/extensions.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../shared/constants/text_styles.dart';

class OnboardingPageView extends StatelessWidget {
  final OnboardingModel item;
  const OnboardingPageView({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final String backgroundImage = item.backgroundgImage;
    final String title = item.title;
    final String subTitle = item.subTitle;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Flexible(
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Image.asset(
              backgroundImage.asImagePng(),
              fit: BoxFit.fill,
              width: double.infinity,
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0.w),
          child: Text(title, style: ts44cFFFFFFw7),
        ),
        // H(15),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0.w),
          child: Text(subTitle, style: ts16cD7D7D7w4),
        ),
        // const Spacer(),
        H(20),
      ],
    );
  }
}
