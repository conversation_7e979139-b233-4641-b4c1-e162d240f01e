import 'package:addc/features/onbaording_screens/providers/onboard_provider.dart';
import 'package:addc/features/onbaording_screens/section/onboard_dot_indicator_section.dart';
import 'package:addc/features/onbaording_screens/section/onboarding_footer.dart';
import 'package:addc/features/onbaording_screens/widgets/onboarding_page_view.dart';
import 'package:addc/features/splash/view/widget/splash_background.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../widgets/gap.dart';

class OnbaordingScreens extends StatefulWidget {
  static const route = '/onbaording_screens';
  const OnbaordingScreens({super.key});

  @override
  State<OnbaordingScreens> createState() => _OnbaordingScreensState();
}

class _OnbaordingScreensState extends State<OnbaordingScreens> {
  late PageController _pageController;
  late OnboardProvider _provider;

  @override
  void initState() {
    super.initState();
    _provider = context.read<OnboardProvider>();
    _pageController = PageController(initialPage: 0, keepPage: false);
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        _provider.selectedIndex = 0;
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          SplashBackground(opacity: 0.5),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: PageView.builder(
                  itemCount: _provider.onBoardingModels.length,
                  controller: _pageController,
                  itemBuilder: (context, index) => OnboardingPageView(
                      item: _provider.onBoardingModels[index]),
                  onPageChanged: (index) => _provider.selectedIndex = index,
                ),
              ),
              // H(60.h),
              const OnboardDotIndicatorSection(),
              H(75.h),
              OnboardingFooter(pageController: _pageController),

              // const Spacer(flex: 9),
              // H(75),
            ],
          ),
        ],
      ),
    );
  }
}
