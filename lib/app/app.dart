import 'package:addc/app/providers.dart';
import 'package:addc/app/routes.dart';
import 'package:addc/app/theme_data.dart';
import 'package:addc/features/splash/view/splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: providers,
      child: ScreenUtilInit(
        designSize: const Size(393, 852),
        enableScaleWH: () => false,
        enableScaleText: () => false,
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return MaterialApp(
            navigatorKey: navigatorKey,
            debugShowCheckedModeBanner: false,
            title: 'MyTQD',
            theme: ThemeDataStructure.themeData,
            onGenerateRoute: onAppGenerateRoute(),
            initialRoute: SplashScreen.route,
            builder: EasyLoading.init(),
          );
        },
      ),
    );
  }
}
