import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ThemeDataStructure {
  static ThemeData get themeData => ThemeData(
      splashColor: Colors.transparent,
      appBarTheme: AppBarTheme(
        backgroundColor: ColorConstants.colorFFFFFF,
        surfaceTintColor: Colors.transparent,
        centerTitle: true,
        titleTextStyle: ts20c000940w4,
        toolbarHeight: 80.h,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20.r),
          ),
        ),
      ),
      fontFamily: 'Grold',
      colorScheme: ColorScheme.fromSeed(
        seedColor: ColorConstants.primaryColor,
        primary: ColorConstants.primaryColor,
      ),
      useMaterial3: true,
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          textStyle: ts16c0AB3A1w4,
          foregroundColor: ColorConstants.colorFFFFFF,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorConstants.primaryColor,
          minimumSize: Size(double.infinity, 56.h),
          textStyle: ts18cFFFFFFw4h1,
          foregroundColor: ColorConstants.colorFFFFFF,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        fillColor: ColorConstants.colorF6F6F6,
        filled: true,
        isDense: true,
        outlineBorder: BorderSide(),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.h),
        border: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstants.colorF6F6F6,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(30.r),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstants.colorF6F6F6,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(30.r),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstants.primaryColor,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(30.r),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstants.primaryColor,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(30.r),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstants.primaryColor,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(30.r),
        ),
        hintStyle: ts15c9D9D9Dw4h1,
      ),
      dropdownMenuTheme: DropdownMenuThemeData(
          inputDecorationTheme: InputDecorationTheme(
        filled: true,
        hintStyle: ts14c9D9D9Dw4,
        fillColor: ColorConstants.colorF6F6F6,
        isDense: false,
        iconColor: ColorConstants.color1DC0B3,
        contentPadding:
            EdgeInsets.symmetric(horizontal: 17.w, vertical: 10.5.h),
        border: OutlineInputBorder(
          borderSide: const BorderSide(width: 0),
          borderRadius: BorderRadius.circular(62.r),
        ),
        outlineBorder: const BorderSide(width: 0),
        activeIndicatorBorder: BorderSide(width: 0),
        constraints: BoxConstraints(maxHeight: 46.h),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 0),
          borderRadius: BorderRadius.circular(62.r),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 0),
          borderRadius: BorderRadius.circular(62.r),
        ),
        disabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 0),
          borderRadius: BorderRadius.circular(62.r),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.red[700]!),
          borderRadius: BorderRadius.circular(62.r),
        ),
      )),
      scaffoldBackgroundColor: ColorConstants.colorEFF1FF,
      switchTheme: SwitchThemeData(
          trackColor: WidgetStatePropertyAll(ColorConstants.color0AB3A1)));
}
