import 'dart:io';
import 'package:addc/app/slide_right_route.dart';
import 'package:addc/features/book_a_vehicle/view/book_a_vehicle_history_screen.dart';
import 'package:addc/features/book_a_vehicle/view/book_a_vehicle_screen.dart';
import 'package:addc/features/book_a_vehicle/view/booked_vehicle_history_details_screen.dart';
import 'package:addc/features/find%20people/view/find_people_screen.dart';
import 'package:addc/features/geo_finder/view/geo_finder_detail_screen.dart';
import 'package:addc/features/geo_finder/view/geo_finder_screen.dart';
import 'package:addc/features/inbox/views/cc_and_b_details_screen.dart';
import 'package:addc/features/inbox/views/cmt_details_screen.dart';
import 'package:addc/features/inbox/views/inbox_screen.dart';
import 'package:addc/features/inbox/views/time_and_attendance_detais_screen.dart';
import 'package:addc/features/leaves/view/leaves_screen.dart';
import 'package:addc/features/my_leaves_screen/view/my_leaves_screen.dart';
import 'package:addc/features/notifications/views/notifications_screen.dart';
import 'package:addc/features/prayer_time/view/prayer_time_screen.dart';
import 'package:addc/features/privacy_policy/views/privacy_policies_screen.dart';
import 'package:addc/features/privacy_policy/views/terms_and_condition_screen.dart';
import 'package:addc/features/splash/view/splash_screen.dart';
import 'package:addc/features/survey/views/survey_screen.dart';
import 'package:flutter/cupertino.dart';
import '../features/complaints/view/common_success_screen.dart';
import '../features/complaints/view/complaints_screen.dart';
import '../features/complaints/view/raise_a_complaint_screen.dart';
import '../features/contact_us/view/contact_us_screen.dart';
import '../features/customize_home/view/customize_home_screen.dart';
import '../features/event_detail/view/event_detalis_screen.dart';
import '../features/master_screen/view/master_screen.dart';
import '../features/my_business_card/view/my_business_card_screen.dart';
import '../features/onbaording_screens/onbaording_screens.dart';
import '../features/authentication/view/login_screen.dart';
import '../features/past_events/view/past_events_screen.dart';
import '../features/permissions/view/permission_submission_screen.dart';
import '../features/permissions/view/permissions_screen.dart';
import '../features/profile/view/profile_screen.dart';
import '../features/suggestions/view/add_new_suggestion_screen.dart';
import '../features/suggestions/view/suggestions_screen.dart';
import '../features/upcoming_events/view/upcoming_events_screen.dart';
import '../features/debug/remote_config_debug_screen.dart';

RouteFactory onAppGenerateRoute() => (settings) {
      Route<dynamic> getRoute(Widget child) {
        if (Platform.isIOS) {
          return CupertinoPageRoute(
            builder: (context) => child,
            settings: settings,
          );
        } else {
          return SlideRightRoute(child, settings.name);
        }
      }

      switch (settings.name) {
        case SplashScreen.route:
          return getRoute(const SplashScreen());
        case MasterScreen.route:
          return getRoute(const MasterScreen());
        case OnbaordingScreens.route:
          return getRoute(const OnbaordingScreens());
        case LoginScreen.route:
          return getRoute(const LoginScreen());
        case LeavesScreen.route:
          return getRoute(const LeavesScreen());
        case InboxScreen.route:
          final args = settings.arguments as InboxScreen;
          return getRoute(
              InboxScreen(isShowMasterAppBar: args.isShowMasterAppBar));
        case CmtDetailsScreen.route:
          return getRoute(const CmtDetailsScreen());
        case CcAndBDetailsScreen.route:
          return getRoute(const CcAndBDetailsScreen());
        case EventDetalisScreen.route:
          return getRoute(const EventDetalisScreen());
        case UpcomingEventsScreen.route:
          return getRoute(const UpcomingEventsScreen());
        case PastEventsScreen.route:
          return getRoute(const PastEventsScreen());
        case FindPeopleScreen.route:
          return getRoute(const FindPeopleScreen());
        case SurveyScreen.route:
          return getRoute(const SurveyScreen());
        case TermsAndConditionScreen.route:
          return getRoute(const TermsAndConditionScreen());
        case PrivacyPoliciesScreen.route:
          return getRoute(const PrivacyPoliciesScreen());
        case RaiseAComplaintScreen.route:
          return getRoute(const RaiseAComplaintScreen());
        case AddNewSuggestionScreen.route:
          return getRoute(const AddNewSuggestionScreen());
        case PermissionsScreen.route:
          return getRoute(const PermissionsScreen());
        case CommonSuccessScreen.route:
          final args = settings.arguments as CommonSuccessScreen;
          return getRoute(CommonSuccessScreen(
              description: args.description,
              imageInSVG: args.imageInSVG,
              onDonePressed: args.onDonePressed,
              title: args.title));
        case SuggestionsScreen.route:
          return getRoute(const SuggestionsScreen());
        case NotificationsScreen.route:
          return getRoute(const NotificationsScreen());
        case ComplaintsScreen.route:
          return getRoute(const ComplaintsScreen());
        case ProfileScreen.route:
          return getRoute(const ProfileScreen());
        case CustomizeHomeScreen.route:
          return getRoute(const CustomizeHomeScreen());
        case MyBusinessCardScreen.route:
          final args = settings.arguments as MyBusinessCardScreen;
          return getRoute(MyBusinessCardScreen(item: args.item));
        case ContactUsScreen.route:
          return getRoute(const ContactUsScreen());
        case PermissionSubmissionScreen.route:
          final args = settings.arguments as PermissionSubmissionScreen;
          return getRoute(PermissionSubmissionScreen(
              type: args.type, violation: args.violation));
        case TimeAndAttendancedetailsScreen.route:
          final args = settings.arguments as TimeAndAttendancedetailsScreen;
          return getRoute(TimeAndAttendancedetailsScreen(item: args.item));
        case GeoFinderScreen.route:
          return getRoute(const GeoFinderScreen());
        case GeoFinderDetailScreen.route:
          return getRoute(const GeoFinderDetailScreen());
        case BookAVehicleHistoryScreen.route:
          return getRoute(const BookAVehicleHistoryScreen());
        case BookAVehicleScreen.route:
          return getRoute(const BookAVehicleScreen());
        case BookedVehicleHistoryDetailsScreen.route:
          final args = settings.arguments as BookedVehicleHistoryDetailsScreen;
          return getRoute(BookedVehicleHistoryDetailsScreen(item: args.item));
        case MyLeavesScreen.route:
          return getRoute(const MyLeavesScreen());
        case PrayerTimeScreen.route:
          return getRoute(const PrayerTimeScreen());
        case RemoteConfigDebugScreen.route:
          return getRoute(const RemoteConfigDebugScreen());
        default:
          return null;
      }
    };
