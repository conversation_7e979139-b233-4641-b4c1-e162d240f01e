import 'package:addc/features/book_a_vehicle/providers/book_vehicle_history_provider.dart';
import 'package:addc/features/book_a_vehicle/providers/book_vehicle_provider.dart';
import 'package:addc/features/contact_us/providers/contact_us_provider.dart';
import 'package:addc/features/find%20people/providers/find_people_provider.dart';
import 'package:addc/features/geo_finder/providers/geo_finder_provider.dart';
import 'package:addc/features/home/<USER>/providers/leave_balance_provider.dart';
import 'package:addc/features/home/<USER>/providers/my_leaves_provider.dart';
import 'package:addc/features/home/<USER>/providers/punch_in_punch_out_provider.dart';
import 'package:addc/features/inbox/providers/inbox_provider.dart';
import 'package:addc/features/inbox/providers/maximo_provider.dart';
import 'package:addc/features/inbox/providers/webta_provider.dart';
import 'package:addc/features/leaves/providers/leaves_provider.dart';
import 'package:addc/features/master_screen/providers/master_provider.dart';
import 'package:addc/features/more/providers/more_provider.dart';
import 'package:addc/features/onbaording_screens/providers/onboard_provider.dart';
import 'package:addc/features/permissions/providers/employee_balance_provider.dart';
import 'package:addc/features/permissions/providers/post_permission_provider.dart';
import 'package:addc/features/permissions/providers/violation_provider.dart';
import 'package:addc/features/survey/providers/survey_provider.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import '../features/authentication/providers/login_provider.dart';
import '../features/complaints/providers/complaint_provider.dart';
import '../features/feeds/providers/feeds_provider.dart';
import '../features/my_business_card/providers/business_provider.dart';
import '../features/permissions/providers/permission_provider.dart';
import '../features/prayer_time/providers/prayer_time_provider.dart';
import '../features/profile/providers/profile_provider.dart';
import '../features/suggestions/providers/suggestions_provider.dart';
import '../shared/helper/date_picker_provider.dart';
import '../shared/helper/image_picker_provider.dart';

List<SingleChildWidget> providers = [
  ChangeNotifierProvider<OnboardProvider>(create: (_) => OnboardProvider()),
  ChangeNotifierProvider<MasterProvider>(create: (_) => MasterProvider()),
  ChangeNotifierProvider<LogInProvider>(create: (_) => LogInProvider()),
  ChangeNotifierProvider<LeavesProvider>(create: (_) => LeavesProvider()),
  ChangeNotifierProvider<InboxProvider>(create: (_) => InboxProvider()),
  ChangeNotifierProvider<MoreProvider>(create: (_) => MoreProvider()),
  ChangeNotifierProvider<FeedsProvider>(create: (_) => FeedsProvider()),
  ChangeNotifierProvider<SurveyProvider>(create: (_) => SurveyProvider()),
  ChangeNotifierProvider<ComplaintProvider>(create: (_) => ComplaintProvider()),
  ChangeNotifierProvider<PermissionProvider>(
      create: (_) => PermissionProvider()),
  ChangeNotifierProvider<SuggestionsProvider>(
      create: (_) => SuggestionsProvider()),
  ChangeNotifierProvider<ImagePickerProvider>(
      create: (_) => ImagePickerProvider()),
  ChangeNotifierProvider<DatePickerProvider>(
      create: (_) => DatePickerProvider()),
  ChangeNotifierProvider<ProfileProvider>(create: (_) => ProfileProvider()),
  ChangeNotifierProvider<BusinessProvider>(create: (_) => BusinessProvider()),
  ChangeNotifierProvider<ContactUsProvider>(create: (_) => ContactUsProvider()),
  ChangeNotifierProvider<GeoFinderProvider>(create: (_) => GeoFinderProvider()),
  ChangeNotifierProvider<BookVehicleHistoryProvider>(
      create: (_) => BookVehicleHistoryProvider()),
  ChangeNotifierProvider<BookVehicleProvider>(
      create: (_) => BookVehicleProvider()),
  ChangeNotifierProvider<FindPeopleProvider>(
      create: (_) => FindPeopleProvider()),
  ChangeNotifierProvider<MyLeavesProvider>(create: (_) => MyLeavesProvider()),
  ChangeNotifierProvider<PrayerTimeProvider>(
      create: (_) => PrayerTimeProvider()),
  ChangeNotifierProvider<LeaveBalanceProvider>(
      create: (_) => LeaveBalanceProvider()),
  ChangeNotifierProvider<PunchInPunchOutProvider>(
      create: (_) => PunchInPunchOutProvider()),
  ChangeNotifierProvider<MaximoProvider>(create: (_) => MaximoProvider()),
  ChangeNotifierProvider<WebTAProvider>(create: (_) => WebTAProvider()),
  ChangeNotifierProvider<ViolationProvider>(create: (_) => ViolationProvider()),
  ChangeNotifierProvider<EmployeeBalanceProvider>(
    create: (_) => EmployeeBalanceProvider(),
  ),
  ChangeNotifierProvider<PostPermissionProvider>(
      create: (_) => PostPermissionProvider()),
];
