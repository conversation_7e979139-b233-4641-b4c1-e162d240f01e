import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/widgets/common_circular_checkbox.dart';
import 'package:flutter/material.dart';

class CommonCircularCheckboxTile extends StatelessWidget {
  final void Function(bool?) onChanged;
  final bool value;
  final String title;
  const CommonCircularCheckboxTile(
      {super.key,
      required this.onChanged,
      required this.title,
      required this.value});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CommonCircularCheckbox(onChanged: onChanged, value: value),
        W(9),
        Text(title, style: ts12c000940w4h1),
      ],
    );
  }
}
