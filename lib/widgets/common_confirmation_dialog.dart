import 'package:addc/features/utils/extensions.dart';
import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:addc/features/widgets/gap.dart';
import 'package:flutter_svg/svg.dart';

class CommonConfirmationDialog extends StatelessWidget {
  final String title;
  final String content;
  final String confirmText;
  final String cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final bool isDestructive;
  const CommonConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    this.confirmText = 'Confirm',
    this.cancelText = 'Cancel',
    this.onConfirm,
    this.onCancel,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    String bgImage = 'warning_1';

    return Padding(
      padding: EdgeInsets.all(12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ConvexAppBar(
            curveSize: 100,
            cornerRadius: 30.r,
            initialActiveIndex: 0,
            style: TabStyle.fixed,
            curve: Curves.decelerate,
            elevation: 0,
            backgroundColor: ColorConstants.colorFFFFFF,
            items: [
              TabItem(
                icon: SvgPicture.asset(bgImage.asIconSvg(),
                    height: 90.h, width: 90.w),
              )
            ],
          ),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
                color: ColorConstants.colorFFFFFF,
                borderRadius:
                    BorderRadius.vertical(bottom: Radius.circular(30.r))),
            child: Column(
              children: [
                H(10),
                Text(
                  title,
                  style: ts26c000940w4h1,
                  textAlign: TextAlign.center,
                ),
                H(18.65),
                Text(
                  content,
                  style: ts14c9D9D9Dw4h1,
                  textAlign: TextAlign.center,
                ),
                H(20),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 22.53.w),
                  child: Row(
                    spacing: 14.08.w,
                    children: [
                      Expanded(
                        child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                                minimumSize: Size(double.infinity, 50.h),
                                backgroundColor: ColorConstants.color8B8D97),
                            onPressed: onCancel ??
                                () => Navigator.of(context).pop(false),
                            child: Text(cancelText)),
                      ),
                      Expanded(
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              minimumSize: Size(double.infinity, 50.h)),
                          onPressed: onConfirm ??
                              () => Navigator.of(context).pop(true),
                          child: Text(confirmText),
                        ),
                      ),
                    ],
                  ),
                ),
                H(20),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
