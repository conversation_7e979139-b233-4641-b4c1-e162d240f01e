import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../shared/constants/color_constants.dart';
import '../shared/constants/text_styles.dart';

class CommonTabBarTile extends StatelessWidget {
  final bool isSelected;
  final String title;
  final VoidCallback onTap;

  const CommonTabBarTile(
      {super.key,
      required this.isSelected,
      required this.onTap,
      required this.title});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.5.h),
        decoration: BoxDecoration(
            border: Border.all(
                color: ColorConstants.colorFFFFFF.withValues(alpha: 0.1),
                width: 0.5),
            color: isSelected
                ? ColorConstants.color000940
                : ColorConstants.colorFFFFFF,
            borderRadius: BorderRadius.circular(10.r)),
        child:
            Text(title, style: isSelected ? ts14cFFFFFFw4h1 : ts14c111111w4h1),
      ),
    );
  }
}
