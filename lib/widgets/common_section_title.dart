import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';

import '../shared/constants/text_styles.dart';

class CommonSectionTitle extends StatelessWidget {
  final String title;
  final VoidCallback? onViewAllPressed;
  const CommonSectionTitle(
      {super.key, required this.title, this.onViewAllPressed});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Flexible(
          child: Text(
            title,
            style: ts20c000940w4,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
        if (onViewAllPressed != null)
          TextButton(
              style: TextButton.styleFrom(
                  foregroundColor: ColorConstants.primaryColor),
              onPressed: onViewAllPressed,
              child: Text('View all')),
      ],
    );
  }
}
