import 'package:addc/features/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../shared/constants/color_constants.dart';
import '../shared/constants/text_styles.dart';

class CommonSearchTextFormField extends StatelessWidget {
  final TextEditingController? controller;
  final String? hintText;
  final Widget? prefixIcon;
  final EdgeInsetsGeometry? contentPadding;
  final TextStyle? hintStyle;
  const CommonSearchTextFormField({
    super.key,
    this.controller,
    this.hintText,
    this.prefixIcon,
    this.contentPadding,
    this.hintStyle,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      maxLines: 1,
      cursorHeight: 12.h,
      cursorWidth: 1,
      expands: false,
      onTapOutside: (event) => FocusManager.instance.primaryFocus?.unfocus(),
      decoration: InputDecoration(
        isDense: true,
        filled: true,
        prefixIcon: prefixIcon ??
            Padding(
              padding: EdgeInsets.all(10.w),
              child: SvgPicture.asset(
                'search-normal'.asIconSvg(),
              ),
            ),
        contentPadding:
            contentPadding ?? EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        maintainHintHeight: true,
        fillColor: ColorConstants.colorF6F6F6,
        border: OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.transparent,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(38.r),
        ),
        disabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstants.primaryColor,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(38.r),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstants.primaryColor,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(38.r),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.transparent,
            width: 0,
          ),
          borderRadius: BorderRadius.circular(38.r),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstants.primaryColor,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(38.r),
        ),
        hintStyle: hintStyle ?? ts14c9D9D9Dw4,
        hintText: hintText,
      ),
    );
  }
}
