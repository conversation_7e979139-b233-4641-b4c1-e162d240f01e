import 'package:addc/features/utils/extensions.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CommonBackButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  const CommonBackButton({super.key, this.onPressed, this.backgroundColor});

  @override
  Widget build(BuildContext context) {
    if (!Navigator.canPop(context)) {
      return const SizedBox.shrink();
    }
    return PopScope(
      child: Padding(
        padding: EdgeInsets.only(left: 12.w),
        child: ElevatedButton(
          onPressed: onPressed ??
              () {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                }
              },
          style: ElevatedButton.styleFrom(
            elevation: 0,
            backgroundColor: backgroundColor ?? ColorConstants.colorFFFFFF,
            padding: EdgeInsets.zero,
            minimumSize: Size(44.h, 44.h),
            maximumSize: Size(44.h, 44.h),
            fixedSize: <PERSON>ze(44.h, 44.h),
            shape: CircleBorder(
              side: BorderSide(
                width: 0.5,
                color: ColorConstants.color000940.withValues(alpha: 0.1),
              ),
            ),
          ),
          child: SvgPicture.asset('arrow-left'.asIconSvg()),
        ),
      ),
    );
  }
}
