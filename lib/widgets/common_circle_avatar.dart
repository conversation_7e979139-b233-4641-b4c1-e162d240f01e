import 'package:addc/features/utils/extensions.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';

class CommonCircleAvatar extends StatelessWidget {
  final String? image;
  final double? height;
  const CommonCircleAvatar({super.key, this.image, this.height});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(width: 1, color: ColorConstants.colorE6E7EC),
          shape: BoxShape.circle),
      child: CircleAvatar(
        radius: height != null ? ((height ?? 1) / 2) : 1,
        backgroundColor: ColorConstants.primaryColor,
        foregroundColor: ColorConstants.primaryColor,
        foregroundImage: AssetImage((image ?? '').asDummyPng()),
        onForegroundImageError: (exception, stackTrace) {},
      ),
    );
  }
}
