import 'dart:async';
import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';

class TypeAheadFormFieldWidget<T> extends StatelessWidget {
  final String hintText;
  final TextEditingController controller;
  final Widget Function(BuildContext, T) itemBuilder;
  final Function(T) onSelected;
  final String? Function(String?)? validator;
  final Widget? suffixIcon;
  final TextStyle? labelStyle;
  final FutureOr<List<T>?> Function(String) suggestionsCallback;
  final String? title;
  final bool obscureText;
  final InputBorder? border;
  final Color? fillColor;
  final int? maxLines;
  final TextStyle? hintStyle;
  final Widget? prefixIcon;
  final Function()? onTap;
  final String? errorText;
  final Function(String)? onChanged;
  final AutovalidateMode? autovalidateMode;
  final EdgeInsetsGeometry? contentPadding;
  final int? maxLength;
  final String counterText;
  final TextInputType? keyboardType;
  final bool? enabled;
  final FocusNode? focusNode;
  final TextCapitalization? textCapitalization;
  final int? minLines;
  final TextStyle? style;
  final TextStyle? titleStyle;
  final double? titleGap;

  const TypeAheadFormFieldWidget({
    super.key,
    required this.controller,
    required this.hintText,
    required this.itemBuilder,
    required this.onSelected,
    required this.suggestionsCallback,
    this.suffixIcon,
    this.validator,
    this.labelStyle,
    this.autovalidateMode,
    this.border,
    this.fillColor,
    this.contentPadding,
    this.counterText = '',
    this.enabled = true,
    this.focusNode,
    this.errorText,
    this.hintStyle,
    this.keyboardType,
    this.maxLength,
    this.maxLines,
    this.minLines,
    this.obscureText = false,
    this.onChanged,
    this.prefixIcon,
    this.style,
    this.titleGap,
    this.titleStyle,
    this.onTap,
    this.textCapitalization,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Align(
              alignment: Alignment.topLeft,
              child: Text(title ?? '', style: titleStyle ?? ts12c9D9D9Dw4)),
          H(titleGap ?? 8),
        ],
        TypeAheadField<T>(
          controller: controller,
          suggestionsCallback: suggestionsCallback,
          itemBuilder: itemBuilder,
          onSelected: onSelected,
          builder: (context, controller, focusNode) {
            return TextFormField(
              controller: controller,
              onChanged: onChanged,
              autofocus: false,
              focusNode: focusNode,
              //  readOnly: readOnly,
              obscureText: obscureText,
              style: style,
              maxLines: maxLines,
              minLines: minLines,
              cursorColor: ColorConstants.primaryColor,
              onTap: onTap,
              maxLength: maxLength,
              keyboardType: keyboardType,
              // inputFormatters: inputFormatters,
              enabled: enabled,
              textCapitalization: textCapitalization ?? TextCapitalization.none,
              validator: validator,
              onTapOutside: (event) =>
                  FocusManager.instance.primaryFocus?.unfocus(),
              decoration: InputDecoration(
                errorText: errorText,
                hintText: hintText,
                hintStyle: hintStyle,
                suffixIcon: suffixIcon,
                prefixIcon: prefixIcon,
                counterText: counterText,
                errorMaxLines: 3,
                contentPadding: contentPadding,
                border: border,
                enabledBorder: border,
                focusedBorder: border,
                fillColor: fillColor,
              ),
            );
          },
        ),
      ],
    );
  }
}
