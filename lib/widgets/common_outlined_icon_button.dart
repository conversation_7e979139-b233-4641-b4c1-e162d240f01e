import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../shared/constants/color_constants.dart';

class CommonOutlinedIconButton extends StatelessWidget {
  final Widget icon;
  final VoidCallback onPressed;
  final bool isBagdeEnabled;
  const CommonOutlinedIconButton(
      {super.key,
      required this.icon,
      required this.onPressed,
      this.isBagdeEnabled = false});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        elevation: 0,
        backgroundColor: ColorConstants.colorFFFFFF,
        padding: EdgeInsets.zero,
        minimumSize: Size(44.h, 44.h),
        maximumSize: Size(44.h, 44.h),
        fixedSize: Size(44.h, 44.h),
        shape: CircleBorder(
          side: BorderSide(
            width: 0.5,
            color: ColorConstants.color000940.withValues(alpha: 0.1),
          ),
        ),
      ),
      child: icon,
    );
    // return Material(
    //   elevation: 5,
    //   type: MaterialType.transparency,
    //   child: InkResponse(
    //     customBorder: CircleBorder(),
    //     onTap: onPressed,
    //     child: Ink(
    //       decoration: BoxDecoration(
    //         border: Border.all(
    //             color: ColorConstants.color000940.withValues(alpha: 0.1)),
    //         shape: BoxShape.circle,
    //       ),
    //       child: Padding(
    //         padding: const EdgeInsets.all(11.0),
    //         child: icon,
    //       ),
    //     ),
    //   ),
    // );
  }
}
