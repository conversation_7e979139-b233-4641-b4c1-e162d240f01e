import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void callBottomSheet({required BuildContext ctx, required Widget body}) async {
  await showModalBottomSheet(
    context: ctx,
    isScrollControlled: true,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
    ),
    builder: (context) {
      return Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          decoration: BoxDecoration(
            color: ColorConstants.colorFFFFFF,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 4.31.h,
                width: 68.w,
                margin: EdgeInsets.only(top: 10.78.h),
                decoration: BoxDecoration(
                    color: ColorConstants.colorE7E7E7,
                    borderRadius: BorderRadius.circular(10.r)),
              ),
              body,
            ],
          ),
        ),
      );
    },
  );
}
