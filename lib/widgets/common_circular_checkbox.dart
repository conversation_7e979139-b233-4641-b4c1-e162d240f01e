import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';

class CommonCircularCheckbox extends StatelessWidget {
  final void Function(bool?) onChanged;
  final bool value;
  const CommonCircularCheckbox(
      {super.key, required this.onChanged, required this.value});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: ColorConstants.colorD7D7D7, width: 1)),
      child: Checkbox(
        activeColor: ColorConstants.color000940,
        visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        splashRadius: 50,
        shape: CircleBorder(side: BorderSide.none),
        side: BorderSide.none,
        value: value,
        onChanged: onChanged,
      ),
    );
  }
}
