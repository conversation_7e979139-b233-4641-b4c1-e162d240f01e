import 'package:addc/features/widgets/gap.dart';
import 'package:addc/shared/constants/color_constants.dart';
import 'package:addc/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CommonTextFormField extends StatelessWidget {
  final String? title;
  final String hintText;
  final bool obscureText;
  final Widget? suffixIcon;
  final InputBorder? border;
  final Color? fillColor;
  final int? maxLines;
  final TextStyle? hintStyle;
  final bool readOnly;
  final Widget? prefixIcon;
  final Function()? onTap;
  final String? Function(String?)? validator;
  final TextEditingController? controller;
  final String? errorText;
  final Function(String)? onChanged;
  final AutovalidateMode? autovalidateMode;
  final EdgeInsetsGeometry? contentPadding;
  final int? maxLength;
  final String counterText;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType? keyboardType;
  final bool? enabled;
  final FocusNode? focusNode;
  final TextCapitalization? textCapitalization;
  final int? minLines;
  final TextStyle? style;
  final TextStyle? titleStyle;
  final double? titleGap;
  final TextInputAction? textInputAction;
  final void Function(String)? onFieldSubmitted;
  const CommonTextFormField({
    super.key,
    this.title,
    required this.hintText,
    this.obscureText = false,
    this.suffixIcon,
    this.border,
    this.fillColor,
    this.maxLines = 1,
    this.hintStyle,
    this.readOnly = false,
    this.prefixIcon,
    this.onTap,
    this.validator,
    this.controller,
    this.errorText,
    this.onChanged,
    this.autovalidateMode,
    this.contentPadding,
    this.maxLength,
    this.inputFormatters,
    this.keyboardType,
    this.counterText = '',
    this.enabled = true,
    this.focusNode,
    this.textCapitalization,
    this.minLines,
    this.style,
    this.titleStyle,
    this.titleGap,
    this.textInputAction,
    this.onFieldSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (title != null) ...[
          Align(
              alignment: Alignment.topLeft,
              child: Text(title ?? '', style: titleStyle ?? ts12c9D9D9Dw4)),
          H(titleGap ?? 8),
        ],
        TextFormField(
          controller: controller,
          validator: validator,
          onChanged: onChanged,
          autofocus: false,
          focusNode: focusNode,
          autovalidateMode: autovalidateMode,
          readOnly: readOnly,
          obscureText: obscureText,
          style: style,
          maxLines: maxLines,
          minLines: minLines,
          cursorColor: ColorConstants.primaryColor,
          onTap: onTap,
          maxLength: maxLength,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          enabled: enabled,
          textCapitalization: textCapitalization ?? TextCapitalization.none,
          textInputAction: textInputAction,
          onFieldSubmitted: onFieldSubmitted,
          onTapOutside: (event) =>
              FocusManager.instance.primaryFocus?.unfocus(),
          decoration: InputDecoration(
            errorText: errorText,
            hintText: hintText,
            hintStyle: hintStyle,
            suffixIcon: suffixIcon,
            prefixIcon: prefixIcon,
            counterText: counterText,
            errorMaxLines: 3,
            contentPadding: contentPadding,
            border: border,
            enabledBorder: border,
            focusedBorder: border,
            fillColor: fillColor,
          ),
        ),
      ],
    );
  }
}
