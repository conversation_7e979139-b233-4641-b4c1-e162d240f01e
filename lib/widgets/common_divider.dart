import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../shared/constants/color_constants.dart';

class CommonDivider extends StatelessWidget {
  final Color? color;
  final double? thickness;
  final double? height;
  const CommonDivider({super.key, this.color, this.thickness, this.height});

  @override
  Widget build(BuildContext context) {
    return Divider(
        color: color ?? ColorConstants.colorF2F2F2,
        thickness: thickness ?? 1.h,
        height: height);
  }
}
