import 'dart:developer';
import 'package:firebase_remote_config/firebase_remote_config.dart';

class FirebaseRemoteConfigService {
  static FirebaseRemoteConfigService? _instance;
  static FirebaseRemoteConfig? _remoteConfig;

  // Singleton pattern
  static FirebaseRemoteConfigService get instance {
    _instance ??= FirebaseRemoteConfigService._internal();
    return _instance!;
  }

  FirebaseRemoteConfigService._internal();

  // Remote Config parameter keys
  static const String _oracleBaseUrlKey = 'oracle_fusion_base_url';
  static const String _webtaBaseUrlKey = 'webta_base_url';
  static const String _taqaMaximoBaseUrlKey = 'taqa_maximo_base_url';
  static const String _showManualLoginKey = 'show_manual_login';

  // Default values - Remote Config will only contain the base domain
  static const String _defaultOracleBaseUrl =
      'https://fa-ewzp-saasfaprod1.fa.ocs.oraclecloud.com';
  // 'https://fa-ewzp-dev3-saasfaprod1.fa.ocs.oraclecloud.com';//Demo

  static const String _defaultWebtaBaseUrl =
      'https://api.taqa.ae/ws/ADDCMobileWebServiceServices/1.0';
  // 'https://api-uat.taqa.ae/ws/ADDCMobileWebServiceServices/1.0';//Demo

  static const String _defaultTaqaMaximoBaseUrl = 'https://api.taqa.ae';
  static const bool _defaultShowManualLogin = false;

  // Oracle Fusion API path and port (static, not from Remote Config)
  static const String _oracleApiPath = '/hcmRestApi/resources/***********/';
  static const String _oraclePort = ':443';

  /// Initialize Firebase Remote Config
  Future<void> initialize() async {
    try {
      _remoteConfig = FirebaseRemoteConfig.instance;

      // Set configuration settings
      // Use shorter fetch interval for development/testing
      await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval:
            Duration.zero, // Changed from 1 hour to 5 minutes for testing
      ));

      // Set default values (only base URLs without path/port for Oracle)
      await _remoteConfig!.setDefaults({
        _oracleBaseUrlKey: _defaultOracleBaseUrl,
        _webtaBaseUrlKey: _defaultWebtaBaseUrl,
        _taqaMaximoBaseUrlKey: _defaultTaqaMaximoBaseUrl,
        _showManualLoginKey: _defaultShowManualLogin,
      });

      log('Firebase Remote Config initialized successfully');
    } catch (e) {
      log('Error initializing Firebase Remote Config: $e');
      rethrow;
    }
  }

  /// Fetch and activate remote config values
  Future<bool> fetchAndActivate() async {
    try {
      if (_remoteConfig == null) {
        await initialize();
      }

      final bool updated = await _remoteConfig!.fetchAndActivate();

      if (updated) {
        log('Remote Config values updated successfully');
      } else {
        log('Remote Config values are up to date');
      }

      return updated;
    } catch (e) {
      log('Error fetching Remote Config: $e');
      return false;
    }
  }

  /// Get Oracle Fusion base URL (combines Remote Config base URL with static path and port)
  String get oracleBaseUrl {
    try {
      String baseUrl;

      if (_remoteConfig == null) {
        log('Remote Config not initialized, using default Oracle base URL');
        baseUrl = _defaultOracleBaseUrl;
      } else {
        final remoteUrl = _remoteConfig!.getString(_oracleBaseUrlKey);
        baseUrl = remoteUrl.isNotEmpty ? remoteUrl : _defaultOracleBaseUrl;
        log('Oracle Base URL from Remote Config: $remoteUrl');
      }

      // Combine base URL with port and API path
      final fullUrl = '$baseUrl$_oraclePort$_oracleApiPath';
      log('Complete Oracle URL: $fullUrl');
      return fullUrl;
    } catch (e) {
      log('Error getting Oracle base URL: $e');
      return '$_defaultOracleBaseUrl$_oraclePort$_oracleApiPath';
    }
  }

  /// Get WebTA base URL
  String get webtaBaseUrl {
    try {
      if (_remoteConfig == null) {
        log('Remote Config not initialized, returning default WebTA URL');
        return _defaultWebtaBaseUrl;
      }

      final url = _remoteConfig!.getString(_webtaBaseUrlKey);
      log('WebTA Base URL from Remote Config: $url');
      return url.isNotEmpty ? url : _defaultWebtaBaseUrl;
    } catch (e) {
      log('Error getting WebTA base URL: $e');
      return _defaultWebtaBaseUrl;
    }
  }

  /// Get Taqa Maximo base URL
  String get taqaMaximoBaseUrl {
    try {
      if (_remoteConfig == null) {
        log('Remote Config not initialized, returning default Taqa Maximo URL');
        return _defaultTaqaMaximoBaseUrl;
      }

      final url = _remoteConfig!.getString(_taqaMaximoBaseUrlKey);
      log('Taqa Maximo Base URL from Remote Config: $url');
      return url.isNotEmpty ? url : _defaultTaqaMaximoBaseUrl;
    } catch (e) {
      log('Error getting Taqa Maximo base URL: $e');
      return _defaultTaqaMaximoBaseUrl;
    }
  }

  /// Get show manual login flag
  bool get showManualLogin {
    try {
      if (_remoteConfig == null) {
        log('Remote Config not initialized, returning default show manual login value');
        return _defaultShowManualLogin;
      }

      final value = _remoteConfig!.getBool(_showManualLoginKey);
      log('Show Manual Login from Remote Config: $value');
      return value;
    } catch (e) {
      log('Error getting show manual login value: $e');
      return _defaultShowManualLogin;
    }
  }

  /// Get all remote config values for debugging
  Map<String, String> getAllValues() {
    Map<String, String> remoteValues;

    if (_remoteConfig == null) {
      remoteValues = {
        '${_oracleBaseUrlKey}_raw': _defaultOracleBaseUrl,
        _webtaBaseUrlKey: _defaultWebtaBaseUrl,
        _taqaMaximoBaseUrlKey: _defaultTaqaMaximoBaseUrl,
        _showManualLoginKey: _defaultShowManualLogin.toString(),
      };
    } else {
      remoteValues = {
        '${_oracleBaseUrlKey}_raw': _remoteConfig!.getString(_oracleBaseUrlKey),
        _webtaBaseUrlKey: _remoteConfig!.getString(_webtaBaseUrlKey),
        _taqaMaximoBaseUrlKey: _remoteConfig!.getString(_taqaMaximoBaseUrlKey),
        _showManualLoginKey:
            _remoteConfig!.getBool(_showManualLoginKey).toString(),
      };
    }

    // Add the complete URLs that will actually be used
    remoteValues['${_oracleBaseUrlKey}_complete'] = oracleBaseUrl;
    remoteValues['${_webtaBaseUrlKey}_complete'] = webtaBaseUrl;
    remoteValues['${_taqaMaximoBaseUrlKey}_complete'] = taqaMaximoBaseUrl;
    remoteValues['${_showManualLoginKey}_complete'] =
        showManualLogin.toString();

    return remoteValues;
  }

  /// Check if remote config is initialized
  bool get isInitialized => _remoteConfig != null;

  /// Force fetch remote config (bypasses minimum fetch interval for debugging)
  Future<bool> forceFetch() async {
    try {
      if (_remoteConfig == null) {
        await initialize();
      }

      // Set minimum fetch interval to 0 for immediate fetch
      await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: Duration.zero, // Allow immediate fetch
      ));

      final bool updated = await _remoteConfig!.fetchAndActivate();

      log('Force fetch completed. Updated: $updated');

      // Reset to normal fetch interval after force fetch
      await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(minutes: 5),
      ));

      return updated;
    } catch (e) {
      log('Error force fetching Remote Config: $e');
      return false;
    }
  }

  /// Get detailed debug information
  Map<String, dynamic> getDebugInfo() {
    if (_remoteConfig == null) {
      return {'initialized': false, 'error': 'Remote Config not initialized'};
    }

    return {
      'initialized': true,
      'lastFetchTime': _remoteConfig!.lastFetchTime.toIso8601String(),
      'lastFetchStatus': _remoteConfig!.lastFetchStatus.toString(),
      'settings': {
        'fetchTimeout': '1 minute',
        'minimumFetchInterval': '1 hour',
      },
      'values': {
        _oracleBaseUrlKey: _remoteConfig!.getString(_oracleBaseUrlKey),
        _webtaBaseUrlKey: _remoteConfig!.getString(_webtaBaseUrlKey),
        _taqaMaximoBaseUrlKey: _remoteConfig!.getString(_taqaMaximoBaseUrlKey),
        _showManualLoginKey:
            _remoteConfig!.getBool(_showManualLoginKey).toString(),
      },
      'sources': {
        _oracleBaseUrlKey:
            _remoteConfig!.getValue(_oracleBaseUrlKey).source.toString(),
        _webtaBaseUrlKey:
            _remoteConfig!.getValue(_webtaBaseUrlKey).source.toString(),
        _taqaMaximoBaseUrlKey:
            _remoteConfig!.getValue(_taqaMaximoBaseUrlKey).source.toString(),
        _showManualLoginKey:
            _remoteConfig!.getValue(_showManualLoginKey).source.toString(),
      }
    };
  }
}
