// Example usage of the new show_manual_login parameter from Firebase Remote Config

import 'package:flutter/material.dart';
import 'firebase_remote_config_service.dart';

class LoginScreenExample extends StatelessWidget {
  const LoginScreenExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Corporate SSO Button (always shown)
          const Text("Corporate Single Sign-On"),
          ElevatedButton(
            onPressed: () {
              // Handle SSO login
            },
            child: const Text("Sign in with Corporate Account"),
          ),
          
          // Manual login section - controlled by Remote Config
          if (FirebaseRemoteConfigService.instance.showManualLogin) ...[
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            const Text("Enter your credentials manually"),
            const SizedBox(height: 16),
            
            // Username field
            TextFormField(
              decoration: const InputDecoration(
                labelText: "Username",
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            
            // Password field
            TextFormField(
              obscureText: true,
              decoration: const InputDecoration(
                labelText: "Password",
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            
            // Manual login button
            ElevatedButton(
              onPressed: () {
                // Handle manual login
              },
              child: const Text("Sign In"),
            ),
          ],
        ],
      ),
    );
  }
}

// Alternative usage with FutureBuilder for async initialization
class LoginScreenWithFutureBuilder extends StatelessWidget {
  const LoginScreenWithFutureBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: _getShowManualLoginFlag(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final showManualLogin = snapshot.data ?? false;
        
        return Scaffold(
          body: Column(
            children: [
              // Corporate SSO Button (always shown)
              const Text("Corporate Single Sign-On"),
              ElevatedButton(
                onPressed: () {
                  // Handle SSO login
                },
                child: const Text("Sign in with Corporate Account"),
              ),
              
              // Manual login section - controlled by Remote Config
              if (showManualLogin) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),
                const Text("Enter your credentials manually"),
                // ... rest of manual login UI
              ],
            ],
          ),
        );
      },
    );
  }

  Future<bool> _getShowManualLoginFlag() async {
    // Ensure Remote Config is initialized and fetched
    await FirebaseRemoteConfigService.instance.fetchAndActivate();
    return FirebaseRemoteConfigService.instance.showManualLogin;
  }
}

// Usage in a provider or state management
class LoginProvider extends ChangeNotifier {
  bool _showManualLogin = false;
  bool get showManualLogin => _showManualLogin;

  Future<void> initializeRemoteConfig() async {
    try {
      await FirebaseRemoteConfigService.instance.initialize();
      await FirebaseRemoteConfigService.instance.fetchAndActivate();
      _showManualLogin = FirebaseRemoteConfigService.instance.showManualLogin;
      notifyListeners();
    } catch (e) {
      // Handle error - use default value
      _showManualLogin = false;
      notifyListeners();
    }
  }
}
