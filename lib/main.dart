import 'package:addc/app/app.dart';
import 'package:addc/features/utils/easy_loading_config.dart';
import 'package:addc/shared/helper/shared_preference_helper.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

bool isTestVersion = true;
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp();

  configLoading();
  await SharedPreferenceHelper.instance.init();
  await dotenv.load(fileName: ".env");

  runApp(const MyApp());
}
