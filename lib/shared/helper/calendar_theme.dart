import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';

Widget calendarTheme(context, child) {
  return Theme(
    data: Theme.of(context).copyWith(
      colorScheme: ColorScheme.light(
        primary: ColorConstants.primaryColor, // header background color
        onPrimary: Colors.black, // header text color
        onSurface: Colors.black, // body text color
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: Colors.black, // button text color
        ),
      ),
    ),
    child: child!,
  );
}
