// ignore_for_file: use_build_context_synchronously

import 'dart:developer';

import 'package:flutter/material.dart';

import 'calendar_theme.dart';

class DatePickerProvider extends ChangeNotifier {
  DateTime? enteredDate;
  Future<DateTime?> selectDate(
      {required BuildContext context, DateTime? initialDate}) async {
    enteredDate = await showDatePicker(
        context: context,
        firstDate: DateTime(2020),
        lastDate: DateTime(2100),
        initialDate: initialDate ?? DateTime.now(),
        builder: calendarTheme);
    return enteredDate;
  }

  Future<DateTime?> pickTimer({required BuildContext context}) async {
    enteredDate = null;
    TimeOfDay? timeOfDay = await showTimePicker(
        context: context, builder: calendarTheme, initialTime: TimeOfDay.now());
    final now = DateTime.now();
    if (timeOfDay != null) {
      log('message');
      enteredDate = DateTime(
          now.year, now.month, now.day, timeOfDay.hour, timeOfDay.minute);
    }
    return enteredDate;
  }

  Future<DateTime?> dateTimePicker({required BuildContext context}) async {
    await showDatePicker(
      builder: calendarTheme,
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    ).then(
      (selectedDate) async {
        // After selecting the date, display the time picker.
        if (selectedDate != null) {
          enteredDate = await showTimePicker(
            builder: calendarTheme,
            context: context,
            initialTime: TimeOfDay.now(),
          ).then(
            (selectedTime) {
              // Handle the selected date and time here.
              if (selectedTime != null) {
                DateTime selectedDateTime = DateTime(
                  selectedDate.year,
                  selectedDate.month,
                  selectedDate.day,
                  selectedTime.hour,
                  selectedTime.minute,
                );
                // You can use the selectedDateTime as needed.
                return selectedDateTime;
              }
              return null;
            },
          );
        }
        return null;
      },
    );
    return enteredDate;
  }
}
