import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

class ImagePickerProvider extends ChangeNotifier {
  final ImagePicker _imgPicker = ImagePicker();
  XFile? pickedImage;
  Future<void> imagePicker(
      {required ImageSource source, bool isFromDrawer = false}) async {
    XFile? imgCamera = await _imgPicker.pickImage(
        source: source,
        preferredCameraDevice: CameraDevice.front,
        imageQuality: 100,
        // maxHeight: 500,
        maxWidth: 1000);
    if (imgCamera != null) {
      CroppedFile? croppedImage = await _cropImage(image: imgCamera);
      if (croppedImage != null) {
        pickedImage = XFile(croppedImage.path);
        notifyListeners();
      }
    }
  }

  Future<CroppedFile?> _cropImage({required XFile image}) async {
    return ImageCropper().cropImage(
      sourcePath: image.path,
      // aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1),
      uiSettings: [
        AndroidUiSettings(
          statusBarColor: Colors.black,
          initAspectRatio: CropAspectRatioPreset.square,
          lockAspectRatio: true,
          hideBottomControls: true,
        ),
        IOSUiSettings(
          // minimumAspectRatio: 1,
          // resetAspectRatioEnabled: false,
          rectHeight: 1000,
          rectWidth: 1000,
          rectX: 1000,
          rectY: 1000,
          // aspectRatioLockDimensionSwapEnabled: true,
          rotateButtonsHidden: true,
          aspectRatioLockEnabled: true,
          // aspectRatioPickerButtonHidden: true,
          rotateClockwiseButtonHidden: false,
          resetButtonHidden: true,
          showCancelConfirmationDialog: false,
          hidesNavigationBar: true,
        ),
      ],
    );
  }
}
