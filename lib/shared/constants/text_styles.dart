import 'package:addc/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

TextStyle getTextStyle({
  double? fontSize,
  FontWeight? fontWeight,
  Color? color,
  double? letterSpacing,
  double? height,
  TextDecoration? decoration,
}) {
  return TextStyle(
      fontFamily: 'Grold',
      color: color,
      fontSize: fontSize,
      fontWeight: fontWeight,
      letterSpacing: letterSpacing,
      height: height,
      decoration: decoration);
}

//---------------------------------46---------------------------------------------//
TextStyle get ts46c1E1E1Ew4 => getTextStyle(
      fontSize: 46.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color1E1E1E,
    );
TextStyle get ts46c1E1E1Ew4h1 => getTextStyle(
      fontSize: 46.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color1E1E1E,
      height: 1,
    );
//---------------------------------44---------------------------------------------//
TextStyle get ts44cFFFFFFw7 => getTextStyle(
    fontSize: 44.sp,
    fontWeight: FontWeight.w700,
    height: 1,
    color: ColorConstants.colorFFFFFF);
//---------------------------------32---------------------------------------------//
TextStyle get ts32c000940w4h1 => getTextStyle(
      fontSize: 32.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color000940,
      height: 1,
    );
//---------------------------------30---------------------------------------------//
TextStyle get ts30c94684Ew7 => getTextStyle(
    fontSize: 30.sp,
    fontWeight: FontWeight.w700,
    color: ColorConstants.color0AB3A1);
TextStyle get ts30c94684Ew6 => getTextStyle(
    fontSize: 30.sp,
    fontWeight: FontWeight.w600,
    color: ColorConstants.color0AB3A1);
//---------------------------------40---------------------------------------------//
TextStyle get ts40c000940w4 => getTextStyle(
    fontSize: 40.sp,
    fontWeight: FontWeight.w700,
    color: ColorConstants.color000940,
    height: 1.1);
TextStyle get ts40c1E1E1Ew4h1 => getTextStyle(
    fontSize: 40.sp,
    fontWeight: FontWeight.w700,
    color: ColorConstants.color1E1E1E,
    height: 1);
//---------------------------------28---------------------------------------------//
TextStyle get ts28cFFFFFFw4 => getTextStyle(
    fontSize: 28.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.colorFFFFFF,
    height: 1);
//---------------------------------24---------------------------------------------//
TextStyle get ts24c000940w4h1 => getTextStyle(
      fontSize: 24.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color000940,
      height: 1,
    );
//---------------------------------18---------------------------------------------//
TextStyle get ts18cB3B6C6w4h1 => getTextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.colorB3B6C6,
      height: 1,
    );
TextStyle get ts18c0AB3A1w4h1 => getTextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color0AB3A1,
      height: 1,
    );
TextStyle get ts18c1E1E1Ew4h1 => getTextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color1E1E1E,
      height: 1,
    );
TextStyle get ts18cFFFFFFw4h1 => getTextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.colorFFFFFF,
      height: 1,
    );
//---------------------------------17---------------------------------------------//
TextStyle get ts17c000940w4h1 => getTextStyle(
      fontSize: 17.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color000940,
      height: 1,
    );
//---------------------------------16---------------------------------------------//
TextStyle get ts16c000940w4 => getTextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color000940);
TextStyle get ts16c989898w4 => getTextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color989898);
TextStyle get ts16c000940w4h1 => getTextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color000940,
      height: 1,
    );
TextStyle get ts16c181818w2h1 => getTextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w200,
      color: ColorConstants.color181818,
      height: 1,
    );
TextStyle get ts16cFFFFFFw4 => getTextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.colorFFFFFF);
TextStyle get ts16c1E1E1Ew4 => getTextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color1E1E1E);
TextStyle get ts16c1E1E1Ew4h1 => getTextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color1E1E1E,
      height: 1,
    );
TextStyle get ts16c1E1E1Ew6 => getTextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
    color: ColorConstants.color1E1E1E);
TextStyle get ts16c9D9D9Dw4h1 => getTextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color9D9D9D,
      height: 1,
    );
//---------------------------------15---------------------------------------------//
TextStyle get ts15c0AB3A1w2 => getTextStyle(
    fontSize: 15.sp,
    fontWeight: FontWeight.w200,
    color: ColorConstants.color0AB3A1);
TextStyle get ts15c9D9D9Dw4h1 => getTextStyle(
    fontSize: 15.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color9D9D9D,
    height: 1.1);
TextStyle get ts15c000940w4h1 => getTextStyle(
      fontSize: 15.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color000940,
      height: 1,
    );
//---------------------------------14---------------------------------------------//
TextStyle get ts14c000940w4 => getTextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color000940);
TextStyle get ts14cFFFFFFw4h1 => getTextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.colorFFFFFF,
      height: 1,
    );
TextStyle get ts14c111111w4h1 => getTextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color111111,
      height: 1,
    );
TextStyle get ts14c000940w4h1 => getTextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color000940,
      height: 1,
    );
TextStyle get ts14c000940w4h1U => getTextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color000940,
      height: 1,
      decoration: TextDecoration.underline,
    );
TextStyle get ts14cFF453Fw4h1 => getTextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.colorFF453F,
      height: 1,
    );
TextStyle get ts14c000940w6 => getTextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
    color: ColorConstants.color000940);
TextStyle get ts14c9D9D9Dw4 => getTextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color9D9D9D);
TextStyle get ts14c9D9D9Dw4h1 => getTextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color9D9D9D,
    height: 1.1);
TextStyle get ts14c1E1E1Ew4 => getTextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color1E1E1E);
TextStyle get ts14c1E1E1Ew6 => getTextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
    color: ColorConstants.color1E1E1E);
TextStyle get ts14c242424w4 => getTextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color242424);

//---------------------------------13---------------------------------------------//
TextStyle get ts13c9D9D9Dw4 => getTextStyle(
    fontSize: 13.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color9D9D9D);
TextStyle get ts13cFFFFFFw4h1 => getTextStyle(
      fontSize: 13.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.colorFFFFFF,
      height: 1,
    );
TextStyle get ts13c9D9D9Dw4h1 => getTextStyle(
    fontSize: 13.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color9D9D9D,
    height: 1);
TextStyle get ts13c0AB3A1w4h1 => getTextStyle(
      fontSize: 13.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color0AB3A1,
      height: 1,
    );
//---------------------------------12---------------------------------------------//
TextStyle get ts12c000940w4 => getTextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color242424);
TextStyle get ts12c000940w4h1 => getTextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color242424,
    height: 1);
TextStyle get ts12c6B73A5w4 => getTextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color6B73A5);
TextStyle get ts12c9D9D9Dw4 => getTextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color9D9D9D);

TextStyle get ts12c9D9D9Dw4h1 => getTextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color9D9D9D,
      height: 1,
    );
TextStyle get ts12c9D9D9Dw4h09 => getTextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color9D9D9D,
      height: 0.09,
    );
TextStyle get ts12c9D9D9Dw4h14 => getTextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color9D9D9D,
      height: 1.4,
    );
TextStyle get ts12cD7D7D7w4 => getTextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.colorD7D7D7);
TextStyle get ts12cFFFFFFw4 => getTextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.colorFFFFFF);
TextStyle get ts12c0AB3A1w4 => getTextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color0AB3A1,
    );
TextStyle get ts12c0AB3A1w4h1 => getTextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color0AB3A1,
      height: 1,
    );
TextStyle get ts12c434343w4 => getTextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color434343);
TextStyle get ts12cFFBC47w4 => getTextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.colorFFBC47);
TextStyle get ts12c1E1E1Ew4 => getTextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color1E1E1E);

TextStyle get ts11cFFFFFFw4 => getTextStyle(
    fontSize: 11.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.colorFFFFFF);
TextStyle get ts11c1E1E1Ew4 => getTextStyle(
    fontSize: 11.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color1E1E1E);
//---------------------------------10---------------------------------------------//
TextStyle get ts10c9D9D9Dw4 => getTextStyle(
    fontSize: 10.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color9D9D9D);
TextStyle get ts10c1E1E1Ew4 => getTextStyle(
    fontSize: 10.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color1E1E1E);
TextStyle get ts10c0AB3A1w4h1 => getTextStyle(
    fontSize: 10.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color0AB3A1,
    height: 1);
TextStyle get ts10cB6B6B6w4 => getTextStyle(
    fontSize: 10.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.colorB6B6B6);
TextStyle get ts10cFFFFFFw4h1 => getTextStyle(
      fontSize: 10.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.colorFFFFFF,
      height: 1,
    );
//---------------------------------20---------------------------------------------//
TextStyle get ts20c000940w4 => getTextStyle(
    fontSize: 20.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color000940);
TextStyle get ts20c000940w4h1 => getTextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w400,
      color: ColorConstants.color000940,
      height: 1,
    );
TextStyle get ts20cF6F6F6w4 => getTextStyle(
    fontSize: 20.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.colorF6F6F6);

//---------------------------------16---------------------------------------------//

TextStyle get ts16cD7D7D7w4 => getTextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.colorD7D7D7,
    height: 1.1);
TextStyle get ts16c0AB3A1w4 => getTextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color0AB3A1);

//---------------------------------16---------------------------------------------//
TextStyle get ts26c000940w4 => getTextStyle(
    fontSize: 26.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color000940);
TextStyle get ts26c000940w4h1 => getTextStyle(
    fontSize: 26.sp,
    fontWeight: FontWeight.w400,
    color: ColorConstants.color000940,
    height: 1);
