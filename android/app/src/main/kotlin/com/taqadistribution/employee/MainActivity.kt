package com.taqadistribution.employee

import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.microsoft.intune.mam.client.app.MAMComponents
import com.microsoft.intune.mam.policy.MAMUserInfo

class MainActivity: FlutterFragmentActivity() {
    // Define the channel name. This must match the one used in your Dart code.
    private val CHANNEL = "com.taqadistribution/intune"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
            // This method is invoked on the main thread.
            call, result ->
            when (call.method) {
                "getEnrolledAccount" -> {
                    try {
                        // Get the UserInfo object from the Intune SDK
                        val userInfo = MAMComponents.get(MAMUserInfo::class.java)
                        if (userInfo != null) {
                            val primaryUser = userInfo.primaryUser
                            android.util.Log.d("IntuneSDK", "✅ Primary user found: $primaryUser")
                            result.success(primaryUser)
                        } else {
                            android.util.Log.w("IntuneSDK", "⚠️ MAMUserInfo is null - SDK integrated but no enrollment")
                            result.error("UNAVAILABLE", "Intune user info not available.", null)
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("IntuneSDK", "❌ Error getting enrolled account: ${e.message}")
                        result.error("ERROR", "Exception getting enrolled account: ${e.message}", null)
                    }
                }
                "testIntegration" -> {
                    try {
                        android.util.Log.d("IntuneSDK", "🧪 Testing Intune SDK integration...")

                        // Test 1: Check if MAMComponents is available
                        val userInfo = MAMComponents.get(MAMUserInfo::class.java)
                        val integrationStatus = mutableMapOf<String, Any>()

                        integrationStatus["sdkAvailable"] = true
                        integrationStatus["userInfoAvailable"] = userInfo != null

                        if (userInfo != null) {
                            integrationStatus["primaryUser"] = userInfo.primaryUser ?: "null"
                            android.util.Log.d("IntuneSDK", "✅ Integration test passed - User: ${userInfo.primaryUser}")
                        } else {
                            android.util.Log.w("IntuneSDK", "⚠️ Integration test partial - SDK available but no user info")
                        }

                        result.success(integrationStatus)
                    } catch (e: Exception) {
                        android.util.Log.e("IntuneSDK", "❌ Integration test failed: ${e.message}")
                        result.error("ERROR", "Integration test failed: ${e.message}", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
