package com.taqadistribution.employee

import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.microsoft.intune.mam.client.app.MAMComponents
import com.microsoft.intune.mam.policy.MAMUserInfo

class MainActivity: FlutterFragmentActivity() {
    // Define the channel name. This must match the one used in your Dart code.
    private val CHANNEL = "com.taqadistribution/intune"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
            // This method is invoked on the main thread.
            call, result ->
            if (call.method == "getEnrolledAccount") {
                // Get the UserInfo object from the Intune SDK
                val userInfo = MAMComponents.get(MAMUserInfo::class.java)
                if (userInfo != null) {
                    // Return the primary user account
                    result.success(userInfo.primaryUser)
                } else {
                    result.error("UNAVAILABLE", "Intune user info not available.", null)
                }
            } else {
                result.notImplemented()
            }
        }
    }
}